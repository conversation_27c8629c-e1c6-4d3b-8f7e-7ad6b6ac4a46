#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试频率错误处理后重新开始循环修复

作者：AI助手
创建时间：2025-01-28
"""

import sys
import logging
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

def test_frequency_restart_fix():
    """测试频率错误处理后重新开始循环修复"""
    logger = setup_logging()
    logger.info("🧪 开始测试频率错误处理后重新开始循环修复...")
    
    try:
        # 1. 测试导入
        logger.info("📦 测试模块导入...")
        from modules.frequency_error_handler import FrequencyErrorHandler
        from modules.wechat_auto_add_simple import WeChatAutoAddFriend
        from main_controller import WeChatMainController
        logger.info("✅ 模块导入成功")
        
        # 2. 测试频率错误处理器的重新开始标志功能
        logger.info("🔧 测试频率错误处理器重新开始标志功能...")
        handler = FrequencyErrorHandler()
        
        # 检查初始状态
        if not handler.is_restart_required():
            logger.info("✅ 初始状态：重新开始标志为 False")
        else:
            logger.error("❌ 初始状态错误：重新开始标志应为 False")
            return False
        
        # 测试设置重新开始标志
        handler._set_restart_flag()
        if handler.is_restart_required():
            logger.info("✅ 设置重新开始标志成功")
        else:
            logger.error("❌ 设置重新开始标志失败")
            return False
        
        # 测试清除重新开始标志
        handler.clear_restart_flag()
        if not handler.is_restart_required():
            logger.info("✅ 清除重新开始标志成功")
        else:
            logger.error("❌ 清除重新开始标志失败")
            return False
        
        # 3. 测试重新开始状态信息
        logger.info("📊 测试重新开始状态信息...")
        status = handler.get_restart_status()
        logger.info(f"   - 重新开始标志: {status['restart_required']}")
        logger.info(f"   - 最后检测时间: {status['last_detection_time']}")
        logger.info(f"   - 检测次数: {status['detection_count']}")
        
        # 4. 测试 wechat_auto_add_simple.py 的返回类型修改
        logger.info("🔍 检查 wechat_auto_add_simple.py 的返回类型修改...")
        
        # 检查 run_automation 方法的类型注解
        import inspect
        signature = inspect.signature(WeChatAutoAddFriend.run_automation)
        return_annotation = signature.return_annotation
        logger.info(f"   - run_automation 返回类型: {return_annotation}")
        
        if "Union" in str(return_annotation) or "str" in str(return_annotation):
            logger.info("✅ run_automation 方法支持字符串返回值")
        else:
            logger.warning("⚠️ run_automation 方法可能不支持字符串返回值")
        
        # 5. 测试 main_controller.py 的修改
        logger.info("🔍 检查 main_controller.py 的修改...")
        
        # 检查 execute_step_3_simple_add 方法的类型注解
        signature = inspect.signature(WeChatMainController.execute_step_3_simple_add)
        return_annotation = signature.return_annotation
        logger.info(f"   - execute_step_3_simple_add 返回类型: {return_annotation}")
        
        if "Union" in str(return_annotation) or "str" in str(return_annotation):
            logger.info("✅ execute_step_3_simple_add 方法支持字符串返回值")
        else:
            logger.warning("⚠️ execute_step_3_simple_add 方法可能不支持字符串返回值")
        
        # 检查 execute_single_window_flow 方法的类型注解
        signature = inspect.signature(WeChatMainController.execute_single_window_flow)
        return_annotation = signature.return_annotation
        logger.info(f"   - execute_single_window_flow 返回类型: {return_annotation}")
        
        if "Union" in str(return_annotation) or "str" in str(return_annotation):
            logger.info("✅ execute_single_window_flow 方法支持字符串返回值")
        else:
            logger.warning("⚠️ execute_single_window_flow 方法可能不支持字符串返回值")
        
        # 6. 验证修复逻辑
        logger.info("🔍 验证修复逻辑...")
        
        # 检查频率错误处理完成后的流程
        expected_flow = [
            "1. 频率错误处理器检测到错误",
            "2. 执行窗口关闭操作（添加朋友窗口、微信窗口）",
            "3. 设置重新开始标志",
            "4. wechat_auto_add_simple.py 检测到重新开始标志",
            "5. 返回 'RESTART_REQUIRED' 状态码",
            "6. main_controller.py 接收到状态码",
            "7. 重新开始当前窗口的处理流程"
        ]
        
        logger.info("📋 预期的修复流程:")
        for step in expected_flow:
            logger.info(f"   {step}")
        
        # 7. 测试关键方法存在性
        logger.info("🔍 检查关键方法存在性...")
        
        # 检查频率错误处理器的新方法
        required_methods = [
            '_set_restart_flag',
            'is_restart_required',
            'clear_restart_flag',
            'get_restart_status'
        ]
        
        for method_name in required_methods:
            if hasattr(handler, method_name):
                logger.info(f"✅ {method_name} 方法存在")
            else:
                logger.error(f"❌ {method_name} 方法不存在")
                return False
        
        # 8. 模拟频率错误处理流程
        logger.info("🧪 模拟频率错误处理流程...")
        
        # 模拟频率错误检测结果
        from modules.frequency_error_handler import ErrorDetectionResult, WindowInfo
        
        mock_window_info = WindowInfo(
            hwnd=12345,
            title="微信",
            class_name="Qt51514QWindowIcon",
            rect=(100, 100, 430, 294),
            is_visible=True,
            is_enabled=True
        )
        
        mock_detection_result = ErrorDetectionResult(
            has_error=True,
            error_type="too_frequent",
            error_message="模拟的操作过于频繁错误",
            window_info=mock_window_info,
            detection_time=time.time(),
            confidence=0.9
        )
        
        logger.info("📊 模拟错误检测结果创建成功")
        logger.info(f"   - 错误类型: {mock_detection_result.error_type}")
        logger.info(f"   - 错误信息: {mock_detection_result.error_message}")
        
        # 模拟处理频率错误（不实际执行点击操作）
        logger.info("🔄 模拟处理频率错误...")
        logger.info("   - 步骤1: 点击确定按钮（模拟）")
        logger.info("   - 步骤2: 关闭添加朋友窗口（模拟）")
        logger.info("   - 步骤3: 关闭当前微信窗口（模拟）")
        logger.info("   - 步骤4: 设置重新开始标志")
        
        # 设置重新开始标志
        handler._set_restart_flag()
        
        if handler.is_restart_required():
            logger.info("✅ 模拟频率错误处理完成，重新开始标志已设置")
        else:
            logger.error("❌ 模拟频率错误处理失败，重新开始标志未设置")
            return False
        
        logger.info("🎉 频率错误处理后重新开始循环修复测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生异常: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """主函数"""
    logger = setup_logging()
    logger.info("=" * 70)
    logger.info("频率错误处理后重新开始循环修复测试")
    logger.info("=" * 70)
    
    success = test_frequency_restart_fix()
    
    if success:
        logger.info("✅ 测试通过：频率错误处理后重新开始循环修复成功")
        logger.info("📋 修复说明：")
        logger.info("  - 频率错误处理完成后设置重新开始标志")
        logger.info("  - wechat_auto_add_simple.py 检测标志并返回特殊状态码")
        logger.info("  - main_controller.py 接收状态码并重新开始流程")
        logger.info("  - 避免了无限循环输入手机号的问题")
        logger.info("  - 确保从第一步重新开始完整的6步骤流程")
    else:
        logger.error("❌ 测试失败：频率错误处理后重新开始循环修复有问题")
    
    return success

if __name__ == "__main__":
    main()
