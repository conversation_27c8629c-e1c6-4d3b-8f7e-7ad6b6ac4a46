2025-07-28 15:39:53,200 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 15:39:53,201 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 15:39:53,201 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-28 15:39:53,202 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 15:39:53,203 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-28 15:39:53,204 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 15:39:53,204 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 15:39:53,205 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 15:39:53,206 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 15:39:53,206 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 15:39:53,208 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:39:53,214 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 15:39:53,217 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250728_153953.log
2025-07-28 15:39:53,219 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-28 15:39:53,220 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-28 15:39:53,221 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-28 15:39:53,223 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-28 15:39:53,226 - __main__ - INFO - 📅 当前北京时间: 2025-07-28 15:39:53
2025-07-28 15:39:53,227 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-28 15:39:53,228 - __main__ - INFO - 📅 启动时间: 2025-07-28 15:39:53
2025-07-28 15:39:53,228 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-28 15:39:53,228 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 15:39:53,804 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 15:39:53,805 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 15:39:54,380 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 15:39:54,380 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 15:39:54,384 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-28 15:39:54,385 - __main__ - INFO - ✅ 找到 2 个微信窗口
2025-07-28 15:39:54,385 - __main__ - INFO -   窗口 1: 微信 (句柄: 197994)
2025-07-28 15:39:54,385 - __main__ - INFO -   窗口 2: 微信 (句柄: 525668)
2025-07-28 15:39:54,386 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-28 15:39:55,517 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-28 15:39:55,517 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-28 15:39:55,518 - __main__ - INFO - 📋 待处理联系人数: 2988
2025-07-28 15:39:55,519 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-28 15:39:55,519 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2988
2025-07-28 15:39:55,519 - __main__ - INFO - 
============================================================
2025-07-28 15:39:55,519 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口
2025-07-28 15:39:55,520 - __main__ - INFO - ============================================================
2025-07-28 15:39:55,520 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-28 15:39:55,520 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-28 15:39:55,521 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-28 15:39:55,521 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-28 15:39:55,521 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 15:39:55,825 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 15:39:55,825 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 15:39:55,826 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 15:39:55,826 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 15:39:55,826 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 15:39:55,826 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 15:39:55,827 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 15:39:55,827 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 15:39:55,827 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 15:39:55,828 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 15:39:56,029 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 15:39:56,030 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 15:39:56,030 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-28 15:39:56,030 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 15:39:56,334 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 15:39:56,335 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 15:39:56,335 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 15:39:56,335 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 15:39:56,336 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 15:39:56,336 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 15:39:56,337 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 15:39:56,337 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 15:39:56,337 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 15:39:56,337 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 15:39:56,539 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 15:39:56,540 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 15:39:56,541 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-28 15:39:56,842 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 15:39:56,842 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 15:39:56,842 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 15:39:56,843 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 15:39:56,843 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 15:39:56,843 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 15:39:56,843 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-28 15:39:57,844 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-28 15:39:57,844 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-28 15:39:57,845 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-28 15:39:57,846 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-28 15:39:57,846 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-28 15:39:57,846 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-28 15:39:57,847 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-28 15:39:57,847 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-28 15:39:58,051 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-28 15:39:58,056 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-28 15:40:00,436 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-28 15:40:00,437 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-28 15:40:00,438 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.9 秒...
2025-07-28 15:40:02,350 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-28 15:40:02,551 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-28 15:40:02,552 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-28 15:40:04,936 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-28 15:40:04,937 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-28 15:40:04,937 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.5 秒...
2025-07-28 15:40:07,389 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-28 15:40:07,589 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-28 15:40:07,590 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-28 15:40:09,990 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-28 15:40:09,991 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-28 15:40:09,991 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.7 秒...
2025-07-28 15:40:11,649 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-28 15:40:11,850 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-28 15:40:11,851 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-28 15:40:14,236 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-28 15:40:14,236 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-28 15:40:14,236 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 3.0 秒...
2025-07-28 15:40:17,207 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-28 15:40:17,408 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-28 15:40:17,408 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-28 15:40:19,808 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-28 15:40:19,809 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-28 15:40:19,809 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-28 15:40:19,809 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-28 15:40:19,810 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 15:40:19,812 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 15:40:19,812 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 15:40:19,813 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 15:40:19,813 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 15:40:19,816 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1772484, 进程: Weixin.exe)
2025-07-28 15:40:19,819 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-28 15:40:19,825 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 1772484)
2025-07-28 15:40:19,825 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1772484) - 增强版
2025-07-28 15:40:20,129 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 15:40:20,129 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 15:40:20,129 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-28 15:40:20,130 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-28 15:40:20,130 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-28 15:40:20,131 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-28 15:40:20,335 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-28 15:40:20,336 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-28 15:40:20,538 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 15:40:20,538 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 15:40:20,539 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-28 15:40:20,539 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-28 15:40:20,539 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-28 15:40:20,539 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-28 15:40:20,539 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-28 15:40:21,540 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-28 15:40:21,540 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 15:40:21,542 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 15:40:21,542 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 15:40:21,543 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 15:40:21,544 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 15:40:21,545 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1772484, 进程: Weixin.exe)
2025-07-28 15:40:21,548 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-28 15:40:21,549 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-28 15:40:21,549 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-28 15:40:21,550 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-28 15:40:21,550 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-28 15:40:21,550 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 15:40:21,858 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 15:40:21,858 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 15:40:21,859 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 15:40:21,859 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 15:40:21,860 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 15:40:21,860 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 15:40:21,860 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 15:40:21,861 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 15:40:21,861 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 15:40:21,861 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 15:40:22,063 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 15:40:22,063 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 15:40:22,065 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-28 15:40:22,365 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 15:40:22,366 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-28 15:40:23,366 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-28 15:40:23,367 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-28 15:40:23,367 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-28 15:40:23,370 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250728_154023.log
2025-07-28 15:40:23,370 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-28 15:40:23,370 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-28 15:40:23,371 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-28 15:40:23,371 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-28 15:40:23,373 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-28 15:40:23,374 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-28 15:40:23,374 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-28 15:40:23,374 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-28 15:40:23,375 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-28 15:40:23,375 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-28 15:40:23,376 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-28 15:40:23,377 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 15:40:23,377 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 1772484
2025-07-28 15:40:23,377 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1772484) - 增强版
2025-07-28 15:40:23,686 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 15:40:23,686 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 15:40:23,687 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-28 15:40:23,687 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-28 15:40:23,688 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-28 15:40:23,688 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-28 15:40:23,688 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-28 15:40:23,689 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-28 15:40:23,891 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 15:40:23,891 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 15:40:23,895 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 1772484 (API返回: None)
2025-07-28 15:40:24,195 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 15:40:24,196 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-28 15:40:24,196 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-28 15:40:24,196 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-28 15:40:24,197 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 15:40:24,198 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-28 15:40:24,198 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-28 15:40:24,204 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-28 15:40:24,205 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-28 15:40:24,780 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-28 15:40:24,780 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 15:40:25,102 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2988 个
2025-07-28 15:40:25,103 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2988 个 (总计: 3135 个)
2025-07-28 15:40:25,104 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:40:25,104 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-28 15:40:25,105 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2988
2025-07-28 15:40:25,105 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 19232700797 (叶子浩)
2025-07-28 15:40:25,106 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:40:31,692 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 19232700797
2025-07-28 15:40:31,692 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 15:40:31,692 - modules.wechat_auto_add_simple - INFO - 👥 开始为 19232700797 执行添加朋友操作...
2025-07-28 15:40:31,692 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 15:40:31,693 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 15:40:31,694 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 15:40:31,694 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 15:40:31,697 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 2 个图片文件
2025-07-28 15:40:31,698 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 15:40:31,698 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 15:40:31,699 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 15:40:31,700 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 15:40:31,700 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 15:40:31,700 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 15:40:31,701 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 15:40:31,716 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-28 15:40:31,719 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 15:40:31,720 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 15:40:31,727 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 15:40:31,729 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-28 15:40:31,732 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-28 15:40:32,239 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-28 15:40:32,241 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-28 15:40:32,331 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差37.33, 边缘比例0.0352
2025-07-28 15:40:32,339 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_154032.png
2025-07-28 15:40:32,340 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 15:40:32,341 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 15:40:32,342 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-28 15:40:32,342 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-28 15:40:32,343 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-28 15:40:32,349 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_154032.png
2025-07-28 15:40:32,355 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-28 15:40:32,358 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-28 15:40:32,359 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-28 15:40:32,360 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-28 15:40:32,361 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-28 15:40:32,363 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-28 15:40:32,365 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-28 15:40:32,373 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 15:40:32,384 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_154032.png
2025-07-28 15:40:32,387 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 15:40:32,388 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-28 15:40:32,394 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_154032.png
2025-07-28 15:40:32,453 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 15:40:32,455 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-28 15:40:32,457 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 15:40:32,458 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 15:40:32,762 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-28 15:40:33,567 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 15:40:33,571 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 15:40:33,573 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:40:33,573 - modules.wechat_auto_add_simple - INFO - ✅ 19232700797 添加朋友操作执行成功
2025-07-28 15:40:33,575 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:40:33,575 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 15:40:35,577 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-28 15:40:35,578 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-28 15:40:35,578 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-28 15:40:35,579 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 15:40:35,579 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 15:40:35,580 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 15:40:35,581 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 15:40:35,581 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 15:40:35,582 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-28 15:40:35,582 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 19232700797
2025-07-28 15:40:35,589 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-28 15:40:35,591 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 15:40:35,591 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:684 in _handle_friend_request_window
2025-07-28 15:40:35,593 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-28 15:40:35,593 - modules.friend_request_window - INFO -    📱 phone: '19232700797'
2025-07-28 15:40:35,594 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-28 15:40:35,595 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-28 15:40:36,176 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-28 15:40:36,177 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-28 15:40:36,177 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-28 15:40:36,177 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 15:40:36,179 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 19232700797
2025-07-28 15:40:36,179 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-28 15:40:36,181 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 15:40:36,182 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-28 15:40:36,183 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-28 15:40:36,183 - modules.friend_request_window - INFO -    📱 手机号码: 19232700797
2025-07-28 15:40:36,184 - modules.friend_request_window - INFO -    🆔 准考证: 015825120174
2025-07-28 15:40:36,184 - modules.friend_request_window - INFO -    👤 姓名: 叶子浩
2025-07-28 15:40:36,184 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 15:40:36,184 - modules.friend_request_window - INFO -    📝 备注格式: '015825120174-叶子浩-2025-07-28 15:40:36'
2025-07-28 15:40:36,185 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 15:40:36,185 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '015825120174-叶子浩-2025-07-28 15:40:36'
2025-07-28 15:40:36,185 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 15:40:36,187 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 3148786, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-28 15:40:36,189 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 3148786)
2025-07-28 15:40:36,189 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-28 15:40:36,190 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-28 15:40:36,190 - modules.friend_request_window - INFO - 🔄 激活窗口: 3148786
2025-07-28 15:40:36,894 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-28 15:40:36,894 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-28 15:40:36,895 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-28 15:40:36,895 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:499 in _analyze_search_result
2025-07-28 15:40:36,895 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-28 15:40:36,896 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 15:40:36,896 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-28 15:40:36,896 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:684 in _handle_friend_request_window
2025-07-28 15:40:36,898 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-28 15:40:36,898 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1158 in execute_friend_request_flow
2025-07-28 15:40:36,899 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-28 15:40:36,899 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-28 15:40:36,899 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-28 15:40:36,900 - modules.friend_request_window - INFO -    📝 remark参数: '015825120174-叶子浩-2025-07-28 15:40:36' (类型: <class 'str'>, 长度: 36)
2025-07-28 15:40:36,900 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-28 15:40:36,901 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '015825120174-叶子浩-2025-07-28 15:40:36'
2025-07-28 15:40:36,901 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-28 15:40:36,902 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-28 15:40:36,902 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-28 15:40:36,903 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-28 15:40:36,903 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-28 15:40:36,903 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-28 15:40:36,904 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-28 15:40:37,818 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-28 15:40:43,061 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-28 15:40:43,062 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-28 15:40:43,062 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-28 15:40:43,063 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-28 15:40:43,065 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'modules\window_manager.py在调用modules\wechat_auto_ad...' (前50字符)
2025-07-28 15:40:43,377 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 15:40:43,378 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 15:40:44,281 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 15:40:44,290 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 15:40:44,291 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-28 15:40:44,291 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-28 15:40:44,292 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-28 15:40:44,292 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-28 15:40:44,793 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-28 15:40:44,793 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-28 15:40:44,794 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-28 15:40:44,794 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-28 15:40:44,795 - modules.friend_request_window - INFO -    📝 内容: '015825120174-叶子浩-2025-07-28 15:40:36'
2025-07-28 15:40:44,797 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-28 15:40:44,798 - modules.friend_request_window - INFO -    🔤 内容编码: b'015825120174-\xe5\x8f\xb6\xe5\xad\x90\xe6\xb5\xa9-2025-07-28 15:40:36'
2025-07-28 15:40:44,799 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-28 15:40:45,716 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-28 15:40:50,963 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-28 15:40:50,964 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-28 15:40:50,966 - modules.friend_request_window - INFO -    📝 原始文本: '015825120174-叶子浩-2025-07-28 15:40:36'
2025-07-28 15:40:50,966 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-28 15:40:50,968 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'modules\window_manager.py在调用modules\wechat_auto_ad...' (前50字符)
2025-07-28 15:40:51,282 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 15:40:51,282 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 15:40:52,185 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 15:40:52,193 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 15:40:52,194 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '015825120174-叶子浩-2025-07-28 15:40:36'
2025-07-28 15:40:52,194 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-28 15:40:52,195 - modules.friend_request_window - INFO -    📝 已填写内容: '015825120174-叶子浩-2025-07-28 15:40:36'
2025-07-28 15:40:52,195 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-28 15:40:52,696 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '015825120174-叶子浩-2025-07-28 15:40:36'
2025-07-28 15:40:52,696 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-28 15:40:52,697 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-28 15:40:52,697 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-28 15:40:52,698 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-28 15:40:52,698 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-28 15:40:52,698 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-28 15:40:53,500 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-28 15:40:53,500 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-28 15:40:53,501 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-28 15:40:54,117 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:40:54,118 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-28 15:40:54,118 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-28 15:40:54,118 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-28 15:40:54,635 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:40:54,870 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:40:55,103 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:40:55,333 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:40:55,564 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:40:55,804 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:40:56,035 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:40:56,267 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:40:56,500 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:40:56,735 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:40:56,973 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:40:57,208 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:40:57,440 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:40:57,669 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:40:57,906 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:40:58,138 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:40:58,379 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:40:58,607 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:40:58,841 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:40:59,077 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:40:59,295 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-28 15:40:59,296 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-28 15:41:00,297 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 15:41:00,299 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-28 15:41:00,300 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-28 15:41:00,300 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-28 15:41:00,301 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-28 15:41:00,301 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 15:41:00,301 - modules.friend_request_window - INFO -    📝 备注信息: '015825120174-叶子浩-2025-07-28 15:40:36'
2025-07-28 15:41:00,804 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-28 15:41:00,805 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:41:00,806 - modules.wechat_auto_add_simple - INFO - ✅ 19232700797 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-28 15:41:00,806 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 19232700797
2025-07-28 15:41:00,807 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:41:04,484 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2988
2025-07-28 15:41:04,484 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15926256979 (王文涛)
2025-07-28 15:41:04,485 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:41:11,059 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15926256979
2025-07-28 15:41:11,060 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 15:41:11,060 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15926256979 执行添加朋友操作...
2025-07-28 15:41:11,060 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 15:41:11,061 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 15:41:11,063 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 15:41:11,074 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 15:41:11,090 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 15:41:11,092 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 15:41:11,092 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 15:41:11,093 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 15:41:11,093 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 15:41:11,094 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 15:41:11,094 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 15:41:11,094 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 15:41:11,105 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-28 15:41:11,108 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 15:41:11,109 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 15:41:11,115 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 15:41:11,122 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-28 15:41:11,124 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-28 15:41:11,627 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-28 15:41:11,629 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-28 15:41:11,697 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差42.79, 边缘比例0.0350
2025-07-28 15:41:11,705 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_154111.png
2025-07-28 15:41:11,706 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 15:41:11,707 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 15:41:11,708 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-28 15:41:11,710 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-28 15:41:11,711 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-28 15:41:11,723 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_154111.png
2025-07-28 15:41:11,725 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-28 15:41:11,726 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-28 15:41:11,728 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-28 15:41:11,737 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-28 15:41:11,738 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-28 15:41:11,739 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-28 15:41:11,740 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-28 15:41:11,741 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 15:41:11,752 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_154111.png
2025-07-28 15:41:11,754 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 15:41:11,754 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-28 15:41:11,759 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_154111.png
2025-07-28 15:41:11,786 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 15:41:11,790 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-28 15:41:11,791 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 15:41:11,793 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 15:41:12,096 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-28 15:41:12,864 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 15:41:12,866 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 15:41:12,868 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:41:12,868 - modules.wechat_auto_add_simple - INFO - ✅ 15926256979 添加朋友操作执行成功
2025-07-28 15:41:12,870 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:41:12,871 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 15:41:14,873 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-28 15:41:14,874 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-28 15:41:14,875 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-28 15:41:14,875 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 15:41:14,875 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 15:41:14,875 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 15:41:14,876 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 15:41:14,876 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 15:41:14,876 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-28 15:41:14,877 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 15926256979
2025-07-28 15:41:14,877 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-28 15:41:14,878 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 15:41:14,878 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:684 in _handle_friend_request_window
2025-07-28 15:41:14,878 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-28 15:41:14,879 - modules.friend_request_window - INFO -    📱 phone: '15926256979'
2025-07-28 15:41:14,879 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-28 15:41:14,880 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-28 15:41:15,608 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-28 15:41:15,609 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-28 15:41:15,609 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-28 15:41:15,610 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 15:41:15,612 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 15926256979
2025-07-28 15:41:15,613 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-28 15:41:15,613 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 15:41:15,614 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-28 15:41:15,616 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-28 15:41:15,616 - modules.friend_request_window - INFO -    📱 手机号码: 15926256979
2025-07-28 15:41:15,617 - modules.friend_request_window - INFO -    🆔 准考证: 015825120175
2025-07-28 15:41:15,617 - modules.friend_request_window - INFO -    👤 姓名: 王文涛
2025-07-28 15:41:15,617 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 15:41:15,618 - modules.friend_request_window - INFO -    📝 备注格式: '015825120175-王文涛-2025-07-28 15:41:15'
2025-07-28 15:41:15,618 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 15:41:15,618 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '015825120175-王文涛-2025-07-28 15:41:15'
2025-07-28 15:41:15,619 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 15:41:15,621 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 7341460, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-28 15:41:15,623 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 7341460)
2025-07-28 15:41:15,624 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-28 15:41:15,624 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-28 15:41:15,625 - modules.friend_request_window - INFO - 🔄 激活窗口: 7341460
2025-07-28 15:41:16,328 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-28 15:41:16,329 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-28 15:41:16,330 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-28 15:41:16,330 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:499 in _analyze_search_result
2025-07-28 15:41:16,331 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-28 15:41:16,331 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 15:41:16,332 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-28 15:41:16,332 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:684 in _handle_friend_request_window
2025-07-28 15:41:16,332 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-28 15:41:16,333 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1158 in execute_friend_request_flow
2025-07-28 15:41:16,335 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-28 15:41:16,336 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-28 15:41:16,336 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-28 15:41:16,337 - modules.friend_request_window - INFO -    📝 remark参数: '015825120175-王文涛-2025-07-28 15:41:15' (类型: <class 'str'>, 长度: 36)
2025-07-28 15:41:16,337 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-28 15:41:16,337 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '015825120175-王文涛-2025-07-28 15:41:15'
2025-07-28 15:41:16,338 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-28 15:41:16,338 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-28 15:41:16,339 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-28 15:41:16,339 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-28 15:41:16,340 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-28 15:41:16,340 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-28 15:41:16,340 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-28 15:41:17,249 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-28 15:41:22,492 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-28 15:41:22,493 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-28 15:41:22,493 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-28 15:41:22,493 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-28 15:41:22,494 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'modules\window_manager.py在调用modules\wechat_auto_ad...' (前50字符)
2025-07-28 15:41:22,804 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 15:41:22,804 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 15:41:23,707 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 15:41:23,719 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 15:41:23,720 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-28 15:41:23,721 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-28 15:41:23,721 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-28 15:41:23,721 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-28 15:41:24,222 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-28 15:41:24,222 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-28 15:41:24,223 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-28 15:41:24,223 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-28 15:41:24,223 - modules.friend_request_window - INFO -    📝 内容: '015825120175-王文涛-2025-07-28 15:41:15'
2025-07-28 15:41:24,224 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-28 15:41:24,224 - modules.friend_request_window - INFO -    🔤 内容编码: b'015825120175-\xe7\x8e\x8b\xe6\x96\x87\xe6\xb6\x9b-2025-07-28 15:41:15'
2025-07-28 15:41:24,224 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-28 15:41:25,147 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-28 15:41:30,391 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-28 15:41:30,391 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-28 15:41:30,391 - modules.friend_request_window - INFO -    📝 原始文本: '015825120175-王文涛-2025-07-28 15:41:15'
2025-07-28 15:41:30,392 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-28 15:41:30,392 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'modules\window_manager.py在调用modules\wechat_auto_ad...' (前50字符)
2025-07-28 15:41:30,702 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 15:41:30,702 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 15:41:31,605 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 15:41:31,615 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 15:41:31,616 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '015825120175-王文涛-2025-07-28 15:41:15'
2025-07-28 15:41:31,616 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-28 15:41:31,617 - modules.friend_request_window - INFO -    📝 已填写内容: '015825120175-王文涛-2025-07-28 15:41:15'
2025-07-28 15:41:31,617 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-28 15:41:32,118 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '015825120175-王文涛-2025-07-28 15:41:15'
2025-07-28 15:41:32,119 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-28 15:41:32,120 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-28 15:41:32,121 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-28 15:41:32,121 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-28 15:41:32,122 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-28 15:41:32,122 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-28 15:41:32,923 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-28 15:41:32,923 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-28 15:41:32,924 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-28 15:41:33,568 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:41:33,568 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-28 15:41:33,568 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-28 15:41:33,569 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-28 15:41:34,084 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:41:34,315 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:41:34,544 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:41:34,780 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:41:35,015 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:41:35,245 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:41:35,485 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:41:35,717 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:41:35,950 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:41:36,181 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:41:36,416 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:41:36,648 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:41:36,881 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:41:37,119 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:41:37,361 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:41:37,595 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:41:37,825 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:41:38,067 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:41:38,301 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:41:38,531 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:41:38,751 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-28 15:41:38,751 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-28 15:41:39,752 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 15:41:39,756 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-28 15:41:39,757 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-28 15:41:39,757 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-28 15:41:39,757 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-28 15:41:39,757 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 15:41:39,758 - modules.friend_request_window - INFO -    📝 备注信息: '015825120175-王文涛-2025-07-28 15:41:15'
2025-07-28 15:41:40,259 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-28 15:41:40,260 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:41:40,260 - modules.wechat_auto_add_simple - INFO - ✅ 15926256979 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-28 15:41:40,261 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 15926256979
2025-07-28 15:41:40,262 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:41:43,970 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 3/2988
2025-07-28 15:41:43,970 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13197011687 (余平平)
2025-07-28 15:41:43,970 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:41:50,538 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13197011687
2025-07-28 15:41:50,538 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 15:41:50,539 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13197011687 执行添加朋友操作...
2025-07-28 15:41:50,540 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 15:41:50,540 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 15:41:50,541 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 15:41:50,545 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 15:41:50,555 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 15:41:50,557 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 15:41:50,557 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 15:41:50,558 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 15:41:50,558 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 15:41:50,561 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 15:41:50,562 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 15:41:50,563 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 15:41:50,571 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-28 15:41:50,581 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 15:41:50,584 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 15:41:50,589 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 15:41:50,593 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-28 15:41:50,599 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-28 15:41:51,101 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-28 15:41:51,102 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-28 15:41:51,164 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差36.32, 边缘比例0.0425
2025-07-28 15:41:51,171 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_154151.png
2025-07-28 15:41:51,174 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 15:41:51,182 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 15:41:51,186 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-28 15:41:51,188 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-28 15:41:51,189 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-28 15:41:51,197 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_154151.png
2025-07-28 15:41:51,201 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-28 15:41:51,202 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,244), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-28 15:41:51,204 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-28 15:41:51,206 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-28 15:41:51,207 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-28 15:41:51,209 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-07-28 15:41:51,213 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 259), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-28 15:41:51,215 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 15:41:51,223 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_154151.png
2025-07-28 15:41:51,224 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 15:41:51,229 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 259)是否包含'添加到通讯录'文字
2025-07-28 15:41:51,235 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_154151.png
2025-07-28 15:41:51,262 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 15:41:51,265 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-28 15:41:51,268 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 15:41:51,271 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 15:41:51,573 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 259) -> 屏幕坐标(1364, 259)
2025-07-28 15:41:52,362 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 15:41:52,365 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 15:41:52,368 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:41:52,370 - modules.wechat_auto_add_simple - INFO - ✅ 13197011687 添加朋友操作执行成功
2025-07-28 15:41:52,370 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:41:52,372 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 15:41:54,374 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-28 15:41:54,375 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-28 15:41:54,376 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-28 15:41:54,376 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 15:41:54,377 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 15:41:54,377 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 15:41:54,377 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 15:41:54,378 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 15:41:54,378 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-28 15:41:54,378 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13197011687
2025-07-28 15:41:54,379 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-28 15:41:54,379 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 15:41:54,380 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:684 in _handle_friend_request_window
2025-07-28 15:41:54,380 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-28 15:41:54,382 - modules.friend_request_window - INFO -    📱 phone: '13197011687'
2025-07-28 15:41:54,384 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-28 15:41:54,386 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-28 15:41:54,923 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-28 15:41:54,923 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-28 15:41:54,924 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-28 15:41:54,924 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 15:41:54,925 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13197011687
2025-07-28 15:41:54,926 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-28 15:41:54,926 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 15:41:54,927 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-28 15:41:54,927 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-28 15:41:54,927 - modules.friend_request_window - INFO -    📱 手机号码: 13197011687
2025-07-28 15:41:54,927 - modules.friend_request_window - INFO -    🆔 准考证: 015825120183
2025-07-28 15:41:54,928 - modules.friend_request_window - INFO -    👤 姓名: 余平平
2025-07-28 15:41:54,928 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 15:41:54,928 - modules.friend_request_window - INFO -    📝 备注格式: '015825120183-余平平-2025-07-28 15:41:54'
2025-07-28 15:41:54,929 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 15:41:54,929 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '015825120183-余平平-2025-07-28 15:41:54'
2025-07-28 15:41:54,929 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 15:41:54,931 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 2821440, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-28 15:41:54,935 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 2821440)
2025-07-28 15:41:54,935 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-28 15:41:54,936 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-28 15:41:54,937 - modules.friend_request_window - INFO - 🔄 激活窗口: 2821440
2025-07-28 15:41:55,641 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-28 15:41:55,641 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-28 15:41:55,642 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-28 15:41:55,642 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:499 in _analyze_search_result
2025-07-28 15:41:55,642 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-28 15:41:55,643 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 15:41:55,643 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-28 15:41:55,643 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:684 in _handle_friend_request_window
2025-07-28 15:41:55,644 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-28 15:41:55,644 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1158 in execute_friend_request_flow
2025-07-28 15:41:55,644 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-28 15:41:55,645 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-28 15:41:55,645 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-28 15:41:55,645 - modules.friend_request_window - INFO -    📝 remark参数: '015825120183-余平平-2025-07-28 15:41:54' (类型: <class 'str'>, 长度: 36)
2025-07-28 15:41:55,646 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-28 15:41:55,646 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '015825120183-余平平-2025-07-28 15:41:54'
2025-07-28 15:41:55,647 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-28 15:41:55,648 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-28 15:41:55,648 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-28 15:41:55,648 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-28 15:41:55,649 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-28 15:41:55,650 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-28 15:41:55,652 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-28 15:41:56,595 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-28 15:42:01,840 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-28 15:42:01,840 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-28 15:42:01,841 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-28 15:42:01,841 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-28 15:42:01,842 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'modules\window_manager.py在调用modules\wechat_auto_ad...' (前50字符)
2025-07-28 15:42:02,151 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 15:42:02,152 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 15:42:03,054 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 15:42:03,062 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 15:42:03,064 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-28 15:42:03,065 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-28 15:42:03,066 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-28 15:42:03,068 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-28 15:42:03,569 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-28 15:42:03,569 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-28 15:42:03,570 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-28 15:42:03,570 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-28 15:42:03,570 - modules.friend_request_window - INFO -    📝 内容: '015825120183-余平平-2025-07-28 15:41:54'
2025-07-28 15:42:03,570 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-28 15:42:03,571 - modules.friend_request_window - INFO -    🔤 内容编码: b'015825120183-\xe4\xbd\x99\xe5\xb9\xb3\xe5\xb9\xb3-2025-07-28 15:41:54'
2025-07-28 15:42:03,571 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-28 15:42:04,478 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-28 15:42:09,723 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-28 15:42:09,726 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-28 15:42:09,729 - modules.friend_request_window - INFO -    📝 原始文本: '015825120183-余平平-2025-07-28 15:41:54'
2025-07-28 15:42:09,733 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-28 15:42:09,737 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'modules\window_manager.py在调用modules\wechat_auto_ad...' (前50字符)
2025-07-28 15:42:10,049 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 15:42:10,049 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 15:42:10,959 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 15:42:10,989 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 15:42:11,029 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '015825120183-余平平-2025-07-28 15:41:54'
2025-07-28 15:42:11,054 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-28 15:42:11,096 - modules.friend_request_window - INFO -    📝 已填写内容: '015825120183-余平平-2025-07-28 15:41:54'
2025-07-28 15:42:11,105 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-28 15:42:11,607 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '015825120183-余平平-2025-07-28 15:41:54'
2025-07-28 15:42:11,608 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-28 15:42:11,608 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-28 15:42:11,608 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-28 15:42:11,609 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-28 15:42:11,609 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-28 15:42:11,609 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-28 15:42:12,410 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-28 15:42:12,410 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-28 15:42:12,411 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-28 15:42:13,039 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:42:13,039 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-28 15:42:13,040 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-28 15:42:13,040 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-28 15:42:13,543 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-28 15:42:13,545 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-28 15:42:13,546 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-28 15:42:13,546 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-28 15:42:13,546 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-28 15:42:13,546 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-28 15:42:13,547 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-28 15:42:13,547 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-28 15:42:13,547 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-28 15:42:13,547 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-28 15:42:13,548 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-28 15:42:13,548 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-28 15:42:13,548 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-28 15:42:13,549 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-28 15:42:13,549 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-28 15:42:13,550 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-28 15:42:13,550 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-28 15:42:13,551 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-28 15:42:13,551 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-28 15:42:14,053 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-28 15:42:14,054 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-28 15:42:14,054 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-28 15:42:14,054 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-28 15:42:14,054 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-28 15:42:14,055 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-28 15:42:14,055 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-28 15:42:14,055 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-28 15:42:14,962 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-28 15:42:14,962 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-28 15:42:14,962 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-28 15:42:14,963 - modules.friend_request_window - INFO - 🔄 步骤2: 关闭添加朋友窗口...
2025-07-28 15:42:14,963 - modules.friend_request_window - INFO - 🎯 使用指定坐标 (1504, 13) 关闭添加朋友窗口...
2025-07-28 15:42:15,479 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui点击坐标 (1504, 13)...
2025-07-28 15:42:16,686 - modules.friend_request_window - INFO - ✅ pyautogui点击添加朋友窗口关闭按钮成功
2025-07-28 15:42:16,686 - modules.friend_request_window - INFO - 🔄 步骤3: 关闭当前微信窗口...
2025-07-28 15:42:16,686 - modules.friend_request_window - INFO - 🎯 使用指定坐标 (700, 16) 关闭当前微信窗口...
2025-07-28 15:42:17,187 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui点击坐标 (700, 16)...
2025-07-28 15:42:18,294 - modules.friend_request_window - INFO - ✅ pyautogui点击当前微信窗口关闭按钮成功
2025-07-28 15:42:18,294 - modules.friend_request_window - INFO - 🔄 关闭错误相关窗口...
2025-07-28 15:42:18,318 - modules.friend_request_window - INFO - ✅ 频率错误处理完成
2025-07-28 15:42:18,319 - modules.friend_request_window - INFO - 🔄 频率错误处理完成，需要重新开始流程
2025-07-28 15:42:18,319 - modules.friend_request_window - INFO - 🔄 已设置重新开始标志
2025-07-28 15:42:18,319 - modules.friend_request_window - INFO - ✅ 原有频率错误处理成功，已切换到微信2
2025-07-28 15:42:18,320 - modules.friend_request_window - WARNING - ⚠️ 检测到并处理了频率错误，已切换到微信2
2025-07-28 15:42:18,320 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-28 15:42:18,320 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-28 15:42:18,320 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 15:42:18,321 - modules.friend_request_window - INFO -    📝 备注信息: '015825120183-余平平-2025-07-28 15:41:54'
2025-07-28 15:42:18,821 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-28 15:42:18,822 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:42:18,822 - modules.wechat_auto_add_simple - INFO - ✅ 13197011687 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-28 15:42:18,823 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 13197011687
2025-07-28 15:42:18,824 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:42:22,444 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 4/2988
2025-07-28 15:42:22,445 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 16602728268 (董佳)
2025-07-28 15:42:22,445 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:42:29,220 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 16602728268
2025-07-28 15:42:29,220 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 15:42:29,220 - modules.wechat_auto_add_simple - INFO - 👥 开始为 16602728268 执行添加朋友操作...
2025-07-28 15:42:29,221 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 15:42:29,221 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 15:42:29,222 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 15:42:29,223 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 15:42:29,228 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 15:42:29,230 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 15:42:29,231 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 15:42:29,231 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 15:42:29,232 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 15:42:29,232 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 15:42:29,233 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 15:42:29,234 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 15:42:29,240 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 15:42:29,247 - WeChatAutoAdd - DEBUG - 找到微信窗口: ● main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 15:42:29,249 - WeChatAutoAdd - INFO - 共找到 2 个微信窗口
2025-07-28 15:42:29,251 - WeChatAutoAdd - INFO - 找到微信主窗口: 微信
2025-07-28 15:42:29,753 - WeChatAutoAdd - INFO - 目标窗口: 微信
2025-07-28 15:42:29,755 - WeChatAutoAdd - INFO - 窗口位置: (0, 0), 尺寸: 726x650
2025-07-28 15:42:29,844 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差37.27, 边缘比例0.0602
2025-07-28 15:42:29,864 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_154229.png
2025-07-28 15:42:29,866 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 15:42:29,869 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 15:42:29,881 - WeChatAutoAdd - INFO - 截图尺寸: 726x650
2025-07-28 15:42:29,885 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=405-650 (高度:245)
2025-07-28 15:42:29,886 - WeChatAutoAdd - INFO - 特别关注区域: Y=466-526 (距底部154像素)
2025-07-28 15:42:29,896 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_154229.png
2025-07-28 15:42:29,897 - WeChatAutoAdd - INFO - 底部区域原始检测到 92 个轮廓
2025-07-28 15:42:29,899 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,649), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:42:29,902 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,612), 尺寸17x1, 长宽比17.00, 面积17
2025-07-28 15:42:29,903 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,610), 尺寸18x1, 长宽比18.00, 面积18
2025-07-28 15:42:29,904 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,604), 尺寸18x3, 长宽比6.00, 面积54
2025-07-28 15:42:29,905 - WeChatAutoAdd - INFO - 重要轮廓: 位置(614,602), 尺寸96x32, 长宽比3.00, 已知特征:False
2025-07-28 15:42:29,910 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度96 (需要70-95)
2025-07-28 15:42:29,915 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,601), 尺寸18x1, 长宽比18.00, 面积18
2025-07-28 15:42:29,917 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,599), 尺寸17x1, 长宽比17.00, 面积17
2025-07-28 15:42:29,918 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,547), 尺寸16x22, 长宽比0.73, 面积352
2025-07-28 15:42:29,920 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(161,526), 尺寸4x1, 长宽比4.00, 面积4
2025-07-28 15:42:29,921 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,524), 尺寸4x2, 长宽比2.00, 面积8
2025-07-28 15:42:29,923 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,523), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:42:29,933 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,522), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 15:42:29,936 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(161,522), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 15:42:29,938 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,521), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 15:42:29,949 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(155,521), 尺寸5x7, 长宽比0.71, 面积35
2025-07-28 15:42:29,950 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(143,521), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:42:29,951 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(146,520), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:42:29,953 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(179,519), 尺寸10x7, 长宽比1.43, 面积70
2025-07-28 15:42:29,957 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(162,519), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 15:42:29,962 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(196,518), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:42:29,964 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,518), 尺寸1x5, 长宽比0.20, 面积5
2025-07-28 15:42:29,965 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,518), 尺寸4x2, 长宽比2.00, 面积8
2025-07-28 15:42:29,966 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(150,518), 尺寸1x9, 长宽比0.11, 面积9
2025-07-28 15:42:29,967 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(180,516), 尺寸10x2, 长宽比5.00, 面积20
2025-07-28 15:42:29,971 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(155,516), 尺寸29x12, 长宽比2.42, 面积348
2025-07-28 15:42:29,973 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=516 (距底部154像素区域)
2025-07-28 15:42:29,981 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 15:42:29,983 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,516), 尺寸4x13, 长宽比0.31, 面积52
2025-07-28 15:42:29,988 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(185,515), 尺寸42x13, 长宽比3.23, 面积546
2025-07-28 15:42:29,991 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=515 (距底部154像素区域)
2025-07-28 15:42:30,002 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 15:42:30,003 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(123,515), 尺寸26x14, 长宽比1.86, 面积364
2025-07-28 15:42:30,006 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=515 (距底部154像素区域)
2025-07-28 15:42:30,010 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 15:42:30,014 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,505), 尺寸8x1, 长宽比8.00, 面积8
2025-07-28 15:42:30,019 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,503), 尺寸9x1, 长宽比9.00, 面积9
2025-07-28 15:42:30,021 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,502), 尺寸8x4, 长宽比2.00, 面积32
2025-07-28 15:42:30,026 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(139,502), 尺寸6x2, 长宽比3.00, 面积12
2025-07-28 15:42:30,033 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,499), 尺寸8x3, 长宽比2.67, 面积24
2025-07-28 15:42:30,041 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(163,498), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:42:30,052 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(154,498), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:42:30,056 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,497), 尺寸1x4, 长宽比0.25, 面积4
2025-07-28 15:42:30,059 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(229,495), 尺寸29x11, 长宽比2.64, 面积319
2025-07-28 15:42:30,061 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=495 (距底部154像素区域)
2025-07-28 15:42:30,067 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 15:42:30,069 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(163,495), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 15:42:30,071 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,495), 尺寸10x10, 长宽比1.00, 面积100
2025-07-28 15:42:30,074 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=495 (距底部154像素区域)
2025-07-28 15:42:30,076 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 15:42:30,082 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(170,494), 尺寸5x11, 长宽比0.45, 面积55
2025-07-28 15:42:30,084 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(168,494), 尺寸1x7, 长宽比0.14, 面积7
2025-07-28 15:42:30,086 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,493), 尺寸7x13, 长宽比0.54, 面积91
2025-07-28 15:42:30,087 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,493), 尺寸10x1, 长宽比10.00, 面积10
2025-07-28 15:42:30,089 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,493), 尺寸41x14, 长宽比2.93, 面积574
2025-07-28 15:42:30,093 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=493 (距底部154像素区域)
2025-07-28 15:42:30,096 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 15:42:30,101 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,491), 尺寸37x37, 长宽比1.00, 面积1369
2025-07-28 15:42:30,103 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=491 (距底部154像素区域)
2025-07-28 15:42:30,105 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 15:42:30,108 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,456), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:42:30,115 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(247,456), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:42:30,118 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(244,456), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 15:42:30,120 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(203,456), 尺寸9x3, 长宽比3.00, 面积27
2025-07-28 15:42:30,122 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(208,454), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 15:42:30,128 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(203,454), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:42:30,132 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(175,454), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:42:30,134 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,454), 尺寸10x8, 长宽比1.25, 面积80
2025-07-28 15:42:30,136 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(201,453), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:42:30,138 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(158,453), 尺寸4x5, 长宽比0.80, 面积20
2025-07-28 15:42:30,143 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,452), 尺寸4x4, 长宽比1.00, 面积16
2025-07-28 15:42:30,147 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,452), 尺寸7x4, 长宽比1.75, 面积28
2025-07-28 15:42:30,149 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,452), 尺寸8x7, 长宽比1.14, 面积56
2025-07-28 15:42:30,151 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,452), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:42:30,152 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,452), 尺寸1x3, 长宽比0.33, 面积3
2025-07-28 15:42:30,155 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,452), 尺寸6x10, 长宽比0.60, 面积60
2025-07-28 15:42:30,156 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,451), 尺寸5x4, 长宽比1.25, 面积20
2025-07-28 15:42:30,161 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(159,451), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:42:30,166 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,451), 尺寸5x4, 长宽比1.25, 面积20
2025-07-28 15:42:30,167 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(132,451), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:42:30,168 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(123,451), 尺寸8x7, 长宽比1.14, 面积56
2025-07-28 15:42:30,170 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(171,450), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:42:30,172 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(230,449), 尺寸13x11, 长宽比1.18, 面积143
2025-07-28 15:42:30,187 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,449), 尺寸5x1, 长宽比5.00, 面积5
2025-07-28 15:42:30,210 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,449), 尺寸23x11, 长宽比2.09, 面积253
2025-07-28 15:42:30,231 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,448), 尺寸17x12, 长宽比1.42, 面积204
2025-07-28 15:42:30,239 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(173,448), 尺寸10x8, 长宽比1.25, 面积80
2025-07-28 15:42:30,247 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,447), 尺寸19x13, 长宽比1.46, 面积247
2025-07-28 15:42:30,251 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,447), 尺寸11x13, 长宽比0.85, 面积143
2025-07-28 15:42:30,253 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(159,447), 尺寸13x3, 长宽比4.33, 面积39
2025-07-28 15:42:30,255 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(218,435), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:42:30,260 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,435), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 15:42:30,261 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(152,435), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 15:42:30,267 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(253,431), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 15:42:30,269 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(186,429), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:42:30,270 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(229,427), 尺寸30x11, 长宽比2.73, 面积330
2025-07-28 15:42:30,279 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(186,427), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:42:30,284 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(188,426), 尺寸3x11, 长宽比0.27, 面积33
2025-07-28 15:42:30,286 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,426), 尺寸6x11, 长宽比0.55, 面积66
2025-07-28 15:42:30,287 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,426), 尺寸5x11, 长宽比0.45, 面积55
2025-07-28 15:42:30,292 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,426), 尺寸17x11, 长宽比1.55, 面积187
2025-07-28 15:42:30,304 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=87.1 (阈值:60)
2025-07-28 15:42:30,305 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(202,425), 尺寸9x13, 长宽比0.69, 面积117
2025-07-28 15:42:30,308 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,425), 尺寸7x13, 长宽比0.54, 面积91
2025-07-28 15:42:30,311 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(153,425), 尺寸15x13, 长宽比1.15, 面积195
2025-07-28 15:42:30,317 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=88.5 (阈值:60)
2025-07-28 15:42:30,321 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,425), 尺寸14x13, 长宽比1.08, 面积182
2025-07-28 15:42:30,326 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=78.1 (阈值:60)
2025-07-28 15:42:30,330 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,423), 尺寸38x38, 长宽比1.00, 面积1444
2025-07-28 15:42:30,333 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=71.2 (阈值:60)
2025-07-28 15:42:30,334 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,405), 尺寸725x245, 长宽比2.96, 面积177625
2025-07-28 15:42:30,335 - WeChatAutoAdd - INFO - 底部区域找到 15 个按钮候选
2025-07-28 15:42:30,338 - WeChatAutoAdd - INFO - 选择特殊位置按钮: Y=491 (距底部154像素)
2025-07-28 15:42:30,339 - WeChatAutoAdd - INFO - 在底部找到按钮: (90, 509), 尺寸: 37x37, 位置得分: 3.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-07-28 15:42:30,344 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 15:42:30,371 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_154230.png
2025-07-28 15:42:30,373 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 15:42:30,379 - WeChatAutoAdd - INFO - 开始验证按钮区域(90, 509)是否包含'添加到通讯录'文字
2025-07-28 15:42:30,385 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_154230.png
2025-07-28 15:42:30,409 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 15:42:30,411 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0509, 平均亮度=215.2, 亮度标准差=45.9
2025-07-28 15:42:30,416 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 15:42:30,419 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 15:42:30,726 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(90, 509) -> 屏幕坐标(90, 509)
2025-07-28 15:42:31,511 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 15:42:31,513 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 15:42:31,515 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:42:31,515 - modules.wechat_auto_add_simple - INFO - ✅ 16602728268 添加朋友操作执行成功
2025-07-28 15:42:31,515 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:42:31,516 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 15:42:33,518 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:42:33,519 - modules.wechat_auto_add_simple - INFO - ℹ️ 16602728268 未发现申请添加朋友窗口，可能直接添加成功
2025-07-28 15:42:33,519 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 16602728268
2025-07-28 15:42:33,520 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:42:37,354 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 5/2988
2025-07-28 15:42:37,355 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18271431115 (柳敏兰)
2025-07-28 15:42:37,355 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:42:43,940 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18271431115
2025-07-28 15:42:43,941 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 15:42:43,942 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18271431115 执行添加朋友操作...
2025-07-28 15:42:43,942 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 15:42:43,943 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 15:42:43,944 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 15:42:43,948 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 15:42:43,953 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 15:42:43,958 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 15:42:43,959 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 15:42:43,959 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 15:42:43,960 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 15:42:43,961 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 15:42:43,962 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 15:42:43,962 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 15:42:43,980 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 15:42:43,987 - WeChatAutoAdd - DEBUG - 找到微信窗口: ● main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 15:42:43,995 - WeChatAutoAdd - INFO - 共找到 2 个微信窗口
2025-07-28 15:42:44,002 - WeChatAutoAdd - INFO - 找到微信主窗口: 微信
2025-07-28 15:42:44,511 - WeChatAutoAdd - INFO - 目标窗口: 微信
2025-07-28 15:42:44,513 - WeChatAutoAdd - INFO - 窗口位置: (0, 0), 尺寸: 726x650
2025-07-28 15:42:44,600 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差30.61, 边缘比例0.0392
2025-07-28 15:42:44,621 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_154244.png
2025-07-28 15:42:44,629 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 15:42:44,631 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 15:42:44,635 - WeChatAutoAdd - INFO - 截图尺寸: 726x650
2025-07-28 15:42:44,638 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=405-650 (高度:245)
2025-07-28 15:42:44,654 - WeChatAutoAdd - INFO - 特别关注区域: Y=466-526 (距底部154像素)
2025-07-28 15:42:44,679 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_154244.png
2025-07-28 15:42:44,687 - WeChatAutoAdd - INFO - 底部区域原始检测到 161 个轮廓
2025-07-28 15:42:44,699 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,649), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:42:44,704 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(75,642), 尺寸3x5, 长宽比0.60, 面积15
2025-07-28 15:42:44,713 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(83,641), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:42:44,716 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(78,640), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:42:44,720 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(218,639), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:42:44,729 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,639), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 15:42:44,731 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(210,639), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:42:44,735 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(106,638), 尺寸4x3, 长宽比1.33, 面积12
2025-07-28 15:42:44,738 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(97,638), 尺寸8x3, 长宽比2.67, 面积24
2025-07-28 15:42:44,748 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,638), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:42:44,752 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(91,637), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:42:44,757 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(83,636), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:42:44,763 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(247,635), 尺寸4x6, 长宽比0.67, 面积24
2025-07-28 15:42:44,768 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(242,631), 尺寸17x11, 长宽比1.55, 面积187
2025-07-28 15:42:44,771 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(233,631), 尺寸10x9, 长宽比1.11, 面积90
2025-07-28 15:42:44,781 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(231,631), 尺寸6x10, 长宽比0.60, 面积60
2025-07-28 15:42:44,784 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(80,631), 尺寸4x16, 长宽比0.25, 面积64
2025-07-28 15:42:44,787 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,630), 尺寸6x11, 长宽比0.55, 面积66
2025-07-28 15:42:44,799 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,630), 尺寸5x11, 长宽比0.45, 面积55
2025-07-28 15:42:44,802 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,630), 尺寸8x11, 长宽比0.73, 面积88
2025-07-28 15:42:44,811 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,630), 尺寸9x11, 长宽比0.82, 面积99
2025-07-28 15:42:44,820 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(185,629), 尺寸24x13, 长宽比1.85, 面积312
2025-07-28 15:42:44,829 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=84.3 (阈值:60)
2025-07-28 15:42:44,831 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,629), 尺寸15x13, 长宽比1.15, 面积195
2025-07-28 15:42:44,834 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=83.6 (阈值:60)
2025-07-28 15:42:44,836 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,629), 尺寸14x13, 长宽比1.08, 面积182
2025-07-28 15:42:44,840 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=78.1 (阈值:60)
2025-07-28 15:42:44,847 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,612), 尺寸17x1, 长宽比17.00, 面积17
2025-07-28 15:42:44,850 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,610), 尺寸18x1, 长宽比18.00, 面积18
2025-07-28 15:42:44,852 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,604), 尺寸18x3, 长宽比6.00, 面积54
2025-07-28 15:42:44,855 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,601), 尺寸18x1, 长宽比18.00, 面积18
2025-07-28 15:42:44,865 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,599), 尺寸17x1, 长宽比17.00, 面积17
2025-07-28 15:42:44,867 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(200,594), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:42:44,869 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(188,593), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:42:44,878 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,592), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:42:44,881 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(245,592), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 15:42:44,886 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(243,592), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:42:44,892 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(241,592), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:42:44,898 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(124,592), 尺寸4x4, 长宽比1.00, 面积16
2025-07-28 15:42:44,903 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,591), 尺寸4x3, 长宽比1.33, 面积12
2025-07-28 15:42:44,915 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,590), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 15:42:44,918 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(181,589), 尺寸1x5, 长宽比0.20, 面积5
2025-07-28 15:42:44,922 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(127,589), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:42:44,935 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(226,588), 尺寸4x6, 长宽比0.67, 面积24
2025-07-28 15:42:44,948 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(224,588), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 15:42:44,953 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,588), 尺寸3x1, 长宽比3.00, 面积3
2025-07-28 15:42:44,964 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(146,588), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 15:42:44,969 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(143,588), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:42:44,979 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(191,587), 尺寸6x8, 长宽比0.75, 面积48
2025-07-28 15:42:44,981 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(179,585), 尺寸11x9, 长宽比1.22, 面积99
2025-07-28 15:42:44,986 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(147,585), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 15:42:44,988 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(198,584), 尺寸27x11, 长宽比2.45, 面积297
2025-07-28 15:42:44,996 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(122,584), 尺寸45x12, 长宽比3.75, 面积540
2025-07-28 15:42:45,000 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,584), 尺寸4x11, 长宽比0.36, 面积44
2025-07-28 15:42:45,012 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,583), 尺寸11x13, 长宽比0.85, 面积143
2025-07-28 15:42:45,019 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(153,583), 尺寸8x11, 长宽比0.73, 面积88
2025-07-28 15:42:45,028 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(147,583), 尺寸7x3, 长宽比2.33, 面积21
2025-07-28 15:42:45,035 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(143,583), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 15:42:45,037 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,583), 尺寸13x11, 长宽比1.18, 面积143
2025-07-28 15:42:45,048 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(221,571), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:42:45,053 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(216,571), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 15:42:45,057 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,570), 尺寸7x4, 长宽比1.75, 面积28
2025-07-28 15:42:45,065 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(126,570), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 15:42:45,068 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(205,568), 尺寸2x5, 长宽比0.40, 面积10
2025-07-28 15:42:45,070 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(133,568), 尺寸2x5, 长宽比0.40, 面积10
2025-07-28 15:42:45,081 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(171,565), 尺寸4x4, 长宽比1.00, 面积16
2025-07-28 15:42:45,087 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(254,564), 尺寸4x8, 长宽比0.50, 面积32
2025-07-28 15:42:45,096 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(206,564), 尺寸8x10, 长宽比0.80, 面积80
2025-07-28 15:42:45,103 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,564), 尺寸6x10, 长宽比0.60, 面积60
2025-07-28 15:42:45,113 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(242,563), 尺寸11x11, 长宽比1.00, 面积121
2025-07-28 15:42:45,118 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(233,563), 尺寸10x9, 长宽比1.11, 面积90
2025-07-28 15:42:45,129 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(231,563), 尺寸6x10, 长宽比0.60, 面积60
2025-07-28 15:42:45,134 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(141,563), 尺寸10x11, 长宽比0.91, 面积110
2025-07-28 15:42:45,144 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(199,562), 尺寸6x11, 长宽比0.55, 面积66
2025-07-28 15:42:45,149 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(196,562), 尺寸5x6, 长宽比0.83, 面积30
2025-07-28 15:42:45,153 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(191,562), 尺寸7x11, 长宽比0.64, 面积77
2025-07-28 15:42:45,164 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(151,562), 尺寸28x11, 长宽比2.55, 面积308
2025-07-28 15:42:45,170 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=78.7 (阈值:60)
2025-07-28 15:42:45,193 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(120,561), 尺寸12x12, 长宽比1.00, 面积144
2025-07-28 15:42:45,204 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=78.6 (阈值:60)
2025-07-28 15:42:45,218 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,559), 尺寸38x38, 长宽比1.00, 面积1444
2025-07-28 15:42:45,227 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=71.2 (阈值:60)
2025-07-28 15:42:45,234 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,547), 尺寸16x22, 长宽比0.73, 面积352
2025-07-28 15:42:45,240 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(161,526), 尺寸4x1, 长宽比4.00, 面积4
2025-07-28 15:42:45,243 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,524), 尺寸4x2, 长宽比2.00, 面积8
2025-07-28 15:42:45,249 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,523), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:42:45,253 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,522), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 15:42:45,267 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(161,522), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 15:42:45,275 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,521), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 15:42:45,287 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(155,521), 尺寸5x7, 长宽比0.71, 面积35
2025-07-28 15:42:45,297 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(143,521), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:42:45,301 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(146,520), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:42:45,305 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(179,519), 尺寸10x7, 长宽比1.43, 面积70
2025-07-28 15:42:45,312 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(162,519), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 15:42:45,319 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(196,518), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:42:45,337 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,518), 尺寸1x5, 长宽比0.20, 面积5
2025-07-28 15:42:45,353 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,518), 尺寸4x2, 长宽比2.00, 面积8
2025-07-28 15:42:45,371 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(150,518), 尺寸1x9, 长宽比0.11, 面积9
2025-07-28 15:42:45,383 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(180,516), 尺寸10x2, 长宽比5.00, 面积20
2025-07-28 15:42:45,594 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(155,516), 尺寸29x12, 长宽比2.42, 面积348
2025-07-28 15:42:45,612 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=516 (距底部154像素区域)
2025-07-28 15:42:45,645 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 15:42:45,648 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,516), 尺寸4x13, 长宽比0.31, 面积52
2025-07-28 15:42:45,653 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(185,515), 尺寸42x13, 长宽比3.23, 面积546
2025-07-28 15:42:45,661 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=515 (距底部154像素区域)
2025-07-28 15:42:45,666 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 15:42:45,669 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(123,515), 尺寸26x14, 长宽比1.86, 面积364
2025-07-28 15:42:45,671 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=515 (距底部154像素区域)
2025-07-28 15:42:45,678 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 15:42:45,680 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,505), 尺寸8x1, 长宽比8.00, 面积8
2025-07-28 15:42:45,682 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,503), 尺寸9x1, 长宽比9.00, 面积9
2025-07-28 15:42:45,684 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,502), 尺寸8x4, 长宽比2.00, 面积32
2025-07-28 15:42:45,686 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(139,502), 尺寸6x2, 长宽比3.00, 面积12
2025-07-28 15:42:45,691 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,499), 尺寸8x3, 长宽比2.67, 面积24
2025-07-28 15:42:45,696 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(163,498), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:42:45,698 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(154,498), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:42:45,700 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,497), 尺寸1x4, 长宽比0.25, 面积4
2025-07-28 15:42:45,703 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(229,495), 尺寸29x11, 长宽比2.64, 面积319
2025-07-28 15:42:45,705 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=495 (距底部154像素区域)
2025-07-28 15:42:45,711 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 15:42:45,714 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(163,495), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 15:42:45,716 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,495), 尺寸10x10, 长宽比1.00, 面积100
2025-07-28 15:42:45,719 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=495 (距底部154像素区域)
2025-07-28 15:42:45,721 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 15:42:45,732 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(170,494), 尺寸5x11, 长宽比0.45, 面积55
2025-07-28 15:42:45,736 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(168,494), 尺寸1x7, 长宽比0.14, 面积7
2025-07-28 15:42:45,747 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(161,493), 尺寸6x13, 长宽比0.46, 面积78
2025-07-28 15:42:45,749 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,493), 尺寸10x1, 长宽比10.00, 面积10
2025-07-28 15:42:45,752 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,493), 尺寸41x14, 长宽比2.93, 面积574
2025-07-28 15:42:45,755 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=493 (距底部154像素区域)
2025-07-28 15:42:45,762 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 15:42:45,765 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,491), 尺寸37x37, 长宽比1.00, 面积1369
2025-07-28 15:42:45,767 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=491 (距底部154像素区域)
2025-07-28 15:42:45,769 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 15:42:45,771 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,456), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:42:45,780 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(247,456), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:42:45,783 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(244,456), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 15:42:45,785 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(203,456), 尺寸9x3, 长宽比3.00, 面积27
2025-07-28 15:42:45,790 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(208,454), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 15:42:45,796 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(203,454), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:42:45,801 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(175,454), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:42:45,804 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,454), 尺寸10x8, 长宽比1.25, 面积80
2025-07-28 15:42:45,812 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(201,453), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:42:45,817 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(158,453), 尺寸4x5, 长宽比0.80, 面积20
2025-07-28 15:42:45,837 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,452), 尺寸4x4, 长宽比1.00, 面积16
2025-07-28 15:42:45,867 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,452), 尺寸7x4, 长宽比1.75, 面积28
2025-07-28 15:42:45,901 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,452), 尺寸8x7, 长宽比1.14, 面积56
2025-07-28 15:42:45,907 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,452), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:42:45,915 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,452), 尺寸1x3, 长宽比0.33, 面积3
2025-07-28 15:42:45,918 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,452), 尺寸6x10, 长宽比0.60, 面积60
2025-07-28 15:42:45,920 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,451), 尺寸5x4, 长宽比1.25, 面积20
2025-07-28 15:42:45,922 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(159,451), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:42:45,928 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,451), 尺寸5x4, 长宽比1.25, 面积20
2025-07-28 15:42:45,932 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(132,451), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:42:45,935 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(123,451), 尺寸8x7, 长宽比1.14, 面积56
2025-07-28 15:42:45,936 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(171,450), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:42:45,940 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(230,449), 尺寸13x11, 长宽比1.18, 面积143
2025-07-28 15:42:45,946 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,449), 尺寸5x1, 长宽比5.00, 面积5
2025-07-28 15:42:45,949 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,449), 尺寸23x11, 长宽比2.09, 面积253
2025-07-28 15:42:45,951 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,448), 尺寸17x12, 长宽比1.42, 面积204
2025-07-28 15:42:45,954 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(173,448), 尺寸10x8, 长宽比1.25, 面积80
2025-07-28 15:42:45,962 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,447), 尺寸19x13, 长宽比1.46, 面积247
2025-07-28 15:42:45,964 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,447), 尺寸11x13, 长宽比0.85, 面积143
2025-07-28 15:42:45,967 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(159,447), 尺寸13x3, 长宽比4.33, 面积39
2025-07-28 15:42:45,969 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(218,435), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:42:45,972 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,435), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 15:42:45,978 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(152,435), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 15:42:45,979 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(253,431), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 15:42:45,982 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(186,429), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:42:45,984 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(229,427), 尺寸30x11, 长宽比2.73, 面积330
2025-07-28 15:42:45,986 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(186,427), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:42:45,991 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(188,426), 尺寸3x11, 长宽比0.27, 面积33
2025-07-28 15:42:45,996 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,426), 尺寸6x11, 长宽比0.55, 面积66
2025-07-28 15:42:45,998 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,426), 尺寸5x11, 长宽比0.45, 面积55
2025-07-28 15:42:46,001 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,426), 尺寸17x11, 长宽比1.55, 面积187
2025-07-28 15:42:46,003 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=87.1 (阈值:60)
2025-07-28 15:42:46,013 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(202,425), 尺寸9x13, 长宽比0.69, 面积117
2025-07-28 15:42:46,020 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,425), 尺寸7x13, 长宽比0.54, 面积91
2025-07-28 15:42:46,028 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(153,425), 尺寸15x13, 长宽比1.15, 面积195
2025-07-28 15:42:46,034 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=88.5 (阈值:60)
2025-07-28 15:42:46,056 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,425), 尺寸14x13, 长宽比1.08, 面积182
2025-07-28 15:42:46,076 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=78.1 (阈值:60)
2025-07-28 15:42:46,085 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,423), 尺寸38x38, 长宽比1.00, 面积1444
2025-07-28 15:42:46,092 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=71.2 (阈值:60)
2025-07-28 15:42:46,095 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,405), 尺寸725x245, 长宽比2.96, 面积177625
2025-07-28 15:42:46,098 - WeChatAutoAdd - INFO - 底部区域找到 22 个按钮候选
2025-07-28 15:42:46,101 - WeChatAutoAdd - INFO - 选择特殊位置按钮: Y=491 (距底部154像素)
2025-07-28 15:42:46,103 - WeChatAutoAdd - INFO - 在底部找到按钮: (90, 509), 尺寸: 37x37, 位置得分: 3.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-07-28 15:42:46,107 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 15:42:46,131 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_154246.png
2025-07-28 15:42:46,133 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 15:42:46,135 - WeChatAutoAdd - INFO - 开始验证按钮区域(90, 509)是否包含'添加到通讯录'文字
2025-07-28 15:42:46,140 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_154246.png
2025-07-28 15:42:46,168 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 15:42:46,170 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0508, 平均亮度=227.0, 亮度标准差=45.1
2025-07-28 15:42:46,174 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 15:42:46,176 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 15:42:46,479 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(90, 509) -> 屏幕坐标(90, 509)
2025-07-28 15:42:47,413 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 15:42:47,581 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 15:42:47,599 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:42:47,599 - modules.wechat_auto_add_simple - INFO - ✅ 18271431115 添加朋友操作执行成功
2025-07-28 15:42:47,601 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:42:47,602 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 15:42:49,604 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:42:49,607 - modules.wechat_auto_add_simple - INFO - ℹ️ 18271431115 未发现申请添加朋友窗口，可能直接添加成功
2025-07-28 15:42:49,608 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 18271431115
2025-07-28 15:42:49,609 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:42:54,410 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 6/2988
2025-07-28 15:42:54,411 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 17630939086 (徐菲)
2025-07-28 15:42:54,411 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
