2025-07-29 12:53:24,610 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 12:53:24,611 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 12:53:24,611 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-29 12:53:24,612 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 12:53:24,613 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-29 12:53:24,614 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 12:53:24,614 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 12:53:24,615 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 12:53:24,617 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 12:53:24,618 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 12:53:24,620 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:53:24,636 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 12:53:24,651 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250729_125324.log
2025-07-29 12:53:24,655 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-29 12:53:24,753 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-29 12:53:24,784 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-29 12:53:24,788 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-29 12:53:24,791 - __main__ - INFO - 📅 当前北京时间: 2025-07-29 20:53:24
2025-07-29 12:53:24,796 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-29 12:53:24,798 - __main__ - INFO - 📅 启动时间: 2025-07-29 20:53:24
2025-07-29 12:53:24,821 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-29 12:53:24,834 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 12:53:25,397 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 12:53:25,399 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 12:53:25,952 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 12:53:25,963 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 12:53:25,977 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-29 12:53:26,012 - __main__ - INFO - ✅ 找到 2 个微信窗口
2025-07-29 12:53:26,015 - __main__ - INFO -   窗口 1: 微信 (句柄: 197994)
2025-07-29 12:53:26,020 - __main__ - INFO -   窗口 2: 微信 (句柄: 525668)
2025-07-29 12:53:26,027 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-29 12:53:27,202 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-29 12:53:27,203 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-29 12:53:27,203 - __main__ - INFO - 📋 待处理联系人数: 2959
2025-07-29 12:53:27,204 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-29 12:53:27,204 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2959
2025-07-29 12:53:27,204 - __main__ - INFO - 
============================================================
2025-07-29 12:53:27,205 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口
2025-07-29 12:53:27,205 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-29 12:53:27,205 - __main__ - INFO - ============================================================
2025-07-29 12:53:27,209 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-29 12:53:27,210 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-29 12:53:27,210 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-29 12:53:27,211 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-29 12:53:27,211 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 12:53:27,529 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 12:53:27,529 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 12:53:27,529 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 12:53:27,530 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 12:53:27,530 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 12:53:27,530 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 12:53:27,531 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 12:53:27,531 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 12:53:27,531 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 12:53:27,532 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 12:53:27,734 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 12:53:27,734 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 12:53:27,734 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-29 12:53:27,735 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 12:53:28,038 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 12:53:28,038 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 12:53:28,041 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 12:53:28,042 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 12:53:28,043 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 12:53:28,043 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 12:53:28,044 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 12:53:28,045 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 12:53:28,047 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 12:53:28,048 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 12:53:28,253 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 12:53:28,254 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 12:53:28,255 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-29 12:53:28,558 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 12:53:28,558 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 12:53:28,558 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 12:53:28,559 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 12:53:28,559 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 12:53:28,559 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 12:53:28,560 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-29 12:53:28,560 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-29 12:53:29,561 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-29 12:53:29,561 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-29 12:53:29,561 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-29 12:53:29,562 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-29 12:53:29,562 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-29 12:53:29,562 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-29 12:53:29,563 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-29 12:53:29,563 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-29 12:53:29,764 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-29 12:53:29,765 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-29 12:53:32,146 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-29 12:53:32,147 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-29 12:53:32,147 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.5 秒...
2025-07-29 12:53:33,673 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-29 12:53:33,874 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-29 12:53:33,876 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-29 12:53:36,260 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-29 12:53:36,260 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-29 12:53:36,261 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-29 12:53:38,586 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-29 12:53:38,787 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-29 12:53:38,790 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-29 12:53:41,176 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-29 12:53:41,176 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-29 12:53:41,176 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.6 秒...
2025-07-29 12:53:43,823 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-29 12:53:44,024 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-29 12:53:44,024 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-29 12:53:46,425 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-29 12:53:46,425 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-29 12:53:46,425 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.5 秒...
2025-07-29 12:53:47,952 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-29 12:53:48,153 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-29 12:53:48,153 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-29 12:53:50,525 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-29 12:53:50,525 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-29 12:53:50,526 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 12:53:50,527 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 12:53:50,527 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 12:53:50,528 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 12:53:50,529 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 12:53:50,529 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 12:53:50,530 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 12:53:50,531 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1837934, 进程: Weixin.exe)
2025-07-29 12:53:50,533 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-29 12:53:50,534 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 1837934)
2025-07-29 12:53:50,534 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1837934) - 增强版
2025-07-29 12:53:50,839 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 12:53:50,840 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 12:53:50,840 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 12:53:50,841 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 12:53:50,841 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-29 12:53:50,842 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 12:53:51,046 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-29 12:53:51,047 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 12:53:51,249 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 12:53:51,249 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 12:53:51,250 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-29 12:53:51,250 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-29 12:53:51,250 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-29 12:53:51,250 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-29 12:53:51,251 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-29 12:53:52,251 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-29 12:53:52,252 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 12:53:52,254 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 12:53:52,259 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 12:53:52,261 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 12:53:52,263 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 12:53:52,266 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1837934, 进程: Weixin.exe)
2025-07-29 12:53:52,274 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-29 12:53:52,275 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-29 12:53:52,276 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-29 12:53:52,279 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-29 12:53:52,280 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-29 12:53:52,280 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 12:53:52,589 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 12:53:52,589 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 12:53:52,590 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 12:53:52,591 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 12:53:52,591 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 12:53:52,591 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 12:53:52,592 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 12:53:52,592 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 12:53:52,593 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 12:53:52,593 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 12:53:52,795 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 12:53:52,795 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 12:53:52,797 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-29 12:53:53,098 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 12:53:53,098 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-29 12:53:53,098 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-29 12:53:54,099 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-29 12:53:54,099 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-29 12:53:54,100 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-29 12:53:54,102 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250729_125354.log
2025-07-29 12:53:54,103 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-29 12:53:54,103 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-29 12:53:54,103 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-29 12:53:54,109 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-29 12:53:54,111 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-29 12:53:54,111 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-29 12:53:54,112 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-29 12:53:54,112 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-29 12:53:54,112 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-29 12:53:54,113 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-29 12:53:54,113 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-29 12:53:54,114 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 12:53:54,114 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 1837934
2025-07-29 12:53:54,115 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1837934) - 增强版
2025-07-29 12:53:54,427 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 12:53:54,427 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 12:53:54,428 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 12:53:54,429 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 12:53:54,430 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-29 12:53:54,430 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 12:53:54,430 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-29 12:53:54,431 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 12:53:54,633 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 12:53:54,633 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 12:53:54,636 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 1837934 (API返回: None)
2025-07-29 12:53:54,937 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 12:53:54,940 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-29 12:53:54,941 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-29 12:53:54,941 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-29 12:53:54,942 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 12:53:54,943 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-29 12:53:54,943 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-29 12:53:54,949 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-29 12:53:54,951 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-29 12:53:55,593 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-29 12:53:55,593 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 12:53:55,950 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2959 个
2025-07-29 12:53:55,951 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2959 个 (总计: 3135 个)
2025-07-29 12:53:55,951 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 10 个联系人后切换
2025-07-29 12:53:55,951 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {}
2025-07-29 12:53:55,952 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:53:55,952 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-29 12:53:55,952 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2959
2025-07-29 12:53:55,952 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13886107212 (黄欣)
2025-07-29 12:53:55,953 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:54:02,520 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13886107212
2025-07-29 12:54:02,521 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 12:54:02,522 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13886107212 执行添加朋友操作...
2025-07-29 12:54:02,522 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 12:54:02,523 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 12:54:02,524 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 12:54:02,525 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 12:54:02,536 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-29 12:54:02,541 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 12:54:02,542 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 12:54:02,542 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 12:54:02,543 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 12:54:02,543 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 12:54:02,544 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 12:54:02,545 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 12:54:02,552 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 12:54:02,560 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 12:54:02,563 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 12:54:02,565 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 12:54:02,566 - WeChatAutoAdd - DEBUG - 找到微信窗口: 申请添加朋友 - 360x660
2025-07-29 12:54:02,569 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-07-29 12:54:02,573 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 12:54:03,079 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 12:54:03,080 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 12:54:03,157 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差39.05, 边缘比例0.0422
2025-07-29 12:54:03,167 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_125403.png
2025-07-29 12:54:03,171 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 12:54:03,178 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 12:54:03,179 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 12:54:03,181 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 12:54:03,183 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 12:54:03,190 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_125403.png
2025-07-29 12:54:03,198 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-29 12:54:03,201 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,244), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-29 12:54:03,203 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-29 12:54:03,207 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 12:54:03,207 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-29 12:54:03,208 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-07-29 12:54:03,209 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 259), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-29 12:54:03,213 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-29 12:54:03,223 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_125403.png
2025-07-29 12:54:03,225 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-29 12:54:03,232 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 259)是否包含'添加到通讯录'文字
2025-07-29 12:54:03,237 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250729_125403.png
2025-07-29 12:54:03,316 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-29 12:54:03,318 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-29 12:54:03,319 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-29 12:54:03,320 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 12:54:03,624 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 259) -> 屏幕坐标(1364, 259)
2025-07-29 12:54:04,406 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 12:54:04,408 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 12:54:04,409 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:54:04,409 - modules.wechat_auto_add_simple - INFO - ✅ 13886107212 添加朋友操作执行成功
2025-07-29 12:54:04,410 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:54:04,411 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 12:54:06,414 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 12:54:06,414 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 12:54:06,414 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 12:54:06,415 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 12:54:06,415 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 12:54:06,415 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 12:54:06,416 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 12:54:06,416 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 12:54:06,417 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 12:54:06,417 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13886107212
2025-07-29 12:54:06,424 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 12:54:06,427 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-29 12:54:06,428 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:695 in _handle_friend_request_window
2025-07-29 12:54:06,429 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 12:54:06,429 - modules.friend_request_window - INFO -    📱 phone: '13886107212'
2025-07-29 12:54:06,430 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 12:54:06,431 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 12:54:06,884 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-29 12:54:06,885 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 12:54:06,885 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 12:54:06,885 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 12:54:06,888 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13886107212
2025-07-29 12:54:06,889 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 12:54:06,889 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 12:54:06,891 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 12:54:06,892 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 12:54:06,892 - modules.friend_request_window - INFO -    📱 手机号码: 13886107212
2025-07-29 12:54:06,893 - modules.friend_request_window - INFO -    🆔 准考证: 014325110088
2025-07-29 12:54:06,894 - modules.friend_request_window - INFO -    👤 姓名: 黄欣
2025-07-29 12:54:06,894 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 12:54:06,894 - modules.friend_request_window - INFO -    📝 备注格式: '014325110088-黄欣-2025-07-29 20:54:06'
2025-07-29 12:54:06,895 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 12:54:06,895 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110088-黄欣-2025-07-29 20:54:06'
2025-07-29 12:54:06,895 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 12:54:06,896 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 5245202, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-29 12:54:06,898 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 5245202)
2025-07-29 12:54:06,898 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-29 12:54:06,899 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-29 12:54:06,899 - modules.friend_request_window - INFO - 🔄 激活窗口: 5245202
2025-07-29 12:54:07,602 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-29 12:54:07,603 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-29 12:54:07,606 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-29 12:54:07,606 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:499 in _analyze_search_result
2025-07-29 12:54:07,607 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-29 12:54:07,607 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-29 12:54:07,607 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-29 12:54:07,608 - modules.friend_request_window - INFO -    3. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:695 in _handle_friend_request_window
2025-07-29 12:54:07,608 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-29 12:54:07,608 - modules.friend_request_window - INFO -    4. D:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-29 12:54:07,608 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-29 12:54:07,609 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-29 12:54:07,609 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-29 12:54:07,609 - modules.friend_request_window - INFO -    📝 remark参数: '014325110088-黄欣-2025-07-29 20:54:06' (类型: <class 'str'>, 长度: 35)
2025-07-29 12:54:07,610 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-29 12:54:07,611 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110088-黄欣-2025-07-29 20:54:06'
2025-07-29 12:54:07,611 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-29 12:54:07,612 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-29 12:54:07,612 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-29 12:54:07,612 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-29 12:54:07,612 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-29 12:54:07,613 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-29 12:54:07,613 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-29 12:54:08,525 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-29 12:54:13,797 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-29 12:54:13,797 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-29 12:54:13,798 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-29 12:54:13,798 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-29 12:54:13,799 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '<EMAIL>...' (前50字符)
2025-07-29 12:54:14,113 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 12:54:14,113 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 12:54:15,016 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 12:54:15,026 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 12:54:15,030 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-29 12:54:15,030 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-29 12:54:15,030 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-29 12:54:15,031 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-29 12:54:15,531 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-29 12:54:15,532 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-29 12:54:15,534 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-29 12:54:15,534 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-29 12:54:15,535 - modules.friend_request_window - INFO -    📝 内容: '014325110088-黄欣-2025-07-29 20:54:06'
2025-07-29 12:54:15,535 - modules.friend_request_window - INFO -    📏 内容长度: 35 字符
2025-07-29 12:54:15,539 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110088-\xe9\xbb\x84\xe6\xac\xa3-2025-07-29 20:54:06'
2025-07-29 12:54:15,540 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-29 12:54:16,457 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-29 12:54:21,728 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-29 12:54:21,728 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-29 12:54:21,729 - modules.friend_request_window - INFO -    📝 原始文本: '014325110088-黄欣-2025-07-29 20:54:06'
2025-07-29 12:54:21,729 - modules.friend_request_window - INFO -    📏 文本长度: 35 字符
2025-07-29 12:54:21,730 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '<EMAIL>...' (前50字符)
2025-07-29 12:54:22,043 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 12:54:22,043 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 12:54:22,945 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 12:54:22,957 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 12:54:22,958 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110088-黄欣-2025-07-29 20:54:06'
2025-07-29 12:54:22,958 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-29 12:54:22,959 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110088-黄欣-2025-07-29 20:54:06'
2025-07-29 12:54:22,959 - modules.friend_request_window - INFO -    � 内容长度: 35 字符
2025-07-29 12:54:23,460 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110088-黄欣-2025-07-29 20:54:06'
2025-07-29 12:54:23,460 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-29 12:54:23,461 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-29 12:54:23,461 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-29 12:54:23,462 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-29 12:54:23,462 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-29 12:54:23,462 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-29 12:54:24,263 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-29 12:54:24,264 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-29 12:54:24,264 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-29 12:54:24,877 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:54:24,877 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-29 12:54:24,878 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-29 12:54:24,878 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-29 12:54:25,380 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-29 12:54:25,382 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-29 12:54:25,382 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-29 12:54:25,383 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-29 12:54:25,383 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-29 12:54:25,383 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-29 12:54:25,383 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-29 12:54:25,384 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-29 12:54:25,384 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-29 12:54:25,384 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-29 12:54:25,385 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-29 12:54:25,387 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-29 12:54:25,389 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-29 12:54:25,390 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-29 12:54:25,390 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-29 12:54:25,391 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-29 12:54:25,392 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-29 12:54:25,392 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-29 12:54:25,392 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-29 12:54:25,895 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-29 12:54:25,895 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-29 12:54:25,896 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-29 12:54:25,896 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-29 12:54:25,897 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-29 12:54:25,897 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-29 12:54:25,897 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-29 12:54:25,898 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-29 12:54:26,807 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-29 12:54:26,807 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-29 12:54:26,808 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-29 12:54:26,808 - modules.friend_request_window - INFO - 🔄 步骤2: 关闭添加朋友窗口...
2025-07-29 12:54:26,808 - modules.friend_request_window - INFO - 🎯 使用指定坐标 (1504, 13) 关闭添加朋友窗口...
2025-07-29 12:54:27,309 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui点击坐标 (1504, 13)...
2025-07-29 12:54:28,423 - modules.friend_request_window - INFO - ✅ pyautogui点击添加朋友窗口关闭按钮成功
2025-07-29 12:54:28,424 - modules.friend_request_window - INFO - 🔄 步骤3: 关闭当前微信窗口...
2025-07-29 12:54:28,425 - modules.friend_request_window - INFO - 🎯 使用指定坐标 (700, 16) 关闭当前微信窗口...
2025-07-29 12:54:28,930 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui点击坐标 (700, 16)...
2025-07-29 12:54:30,041 - modules.friend_request_window - INFO - ✅ pyautogui点击当前微信窗口关闭按钮成功
2025-07-29 12:54:30,042 - modules.friend_request_window - INFO - 🔄 关闭错误相关窗口...
2025-07-29 12:54:30,066 - modules.friend_request_window - INFO - ✅ 频率错误处理完成
2025-07-29 12:54:30,066 - modules.friend_request_window - INFO - 🔄 频率错误处理完成，需要重新开始流程
2025-07-29 12:54:30,067 - modules.friend_request_window - INFO - 🔄 已设置重新开始标志
2025-07-29 12:54:30,067 - modules.friend_request_window - INFO - ✅ 原有频率错误处理成功，已切换到微信2
2025-07-29 12:54:30,067 - modules.friend_request_window - WARNING - ⚠️ 检测到并处理了频率错误，已切换到微信2
2025-07-29 12:54:30,068 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-29 12:54:30,070 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-29 12:54:30,071 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 12:54:30,072 - modules.friend_request_window - INFO -    📝 备注信息: '014325110088-黄欣-2025-07-29 20:54:06'
2025-07-29 12:54:30,575 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-29 12:54:30,579 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:54:30,580 - modules.wechat_auto_add_simple - INFO - ✅ 13886107212 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-29 12:54:30,582 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 13886107212
2025-07-29 12:54:30,588 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:54:34,631 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2959
2025-07-29 12:54:34,632 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 17543599907 (王雨晨)
2025-07-29 12:54:34,632 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:54:41,233 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 17543599907
2025-07-29 12:54:41,234 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 12:54:41,236 - modules.wechat_auto_add_simple - INFO - 👥 开始为 17543599907 执行添加朋友操作...
2025-07-29 12:54:41,236 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 12:54:41,237 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 12:54:41,237 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 12:54:41,238 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 12:54:41,243 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-29 12:54:41,245 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 12:54:41,245 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 12:54:41,245 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 12:54:41,246 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 12:54:41,246 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 12:54:41,248 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 12:54:41,248 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 12:54:41,259 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 12:54:41,264 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 12:54:41,267 - WeChatAutoAdd - INFO - 共找到 2 个微信窗口
2025-07-29 12:54:41,281 - WeChatAutoAdd - INFO - 找到微信主窗口: 微信
2025-07-29 12:54:41,784 - WeChatAutoAdd - INFO - 目标窗口: 微信
2025-07-29 12:54:41,788 - WeChatAutoAdd - INFO - 窗口位置: (0, 0), 尺寸: 726x650
2025-07-29 12:54:41,905 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差36.40, 边缘比例0.0573
2025-07-29 12:54:41,923 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_125441.png
2025-07-29 12:54:41,924 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 12:54:41,925 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 12:54:41,926 - WeChatAutoAdd - INFO - 截图尺寸: 726x650
2025-07-29 12:54:41,927 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=405-650 (高度:245)
2025-07-29 12:54:41,929 - WeChatAutoAdd - INFO - 特别关注区域: Y=466-526 (距底部154像素)
2025-07-29 12:54:41,937 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_125441.png
2025-07-29 12:54:41,941 - WeChatAutoAdd - INFO - 底部区域原始检测到 162 个轮廓
2025-07-29 12:54:41,942 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(723,646), 尺寸3x4, 长宽比0.75, 面积12
2025-07-29 12:54:41,943 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(75,642), 尺寸3x5, 长宽比0.60, 面积15
2025-07-29 12:54:41,945 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(83,641), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:54:41,946 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(78,640), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:54:41,947 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(218,639), 尺寸1x2, 长宽比0.50, 面积2
2025-07-29 12:54:41,948 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,639), 尺寸3x2, 长宽比1.50, 面积6
2025-07-29 12:54:41,949 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(210,639), 尺寸1x2, 长宽比0.50, 面积2
2025-07-29 12:54:41,962 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(106,638), 尺寸4x3, 长宽比1.33, 面积12
2025-07-29 12:54:41,975 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(97,638), 尺寸8x3, 长宽比2.67, 面积24
2025-07-29 12:54:41,977 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,638), 尺寸1x2, 长宽比0.50, 面积2
2025-07-29 12:54:41,978 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(91,637), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:54:41,980 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(83,636), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:54:41,981 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(247,635), 尺寸4x6, 长宽比0.67, 面积24
2025-07-29 12:54:41,983 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(242,631), 尺寸17x11, 长宽比1.55, 面积187
2025-07-29 12:54:41,985 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(233,631), 尺寸10x9, 长宽比1.11, 面积90
2025-07-29 12:54:42,001 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(231,631), 尺寸6x10, 长宽比0.60, 面积60
2025-07-29 12:54:42,011 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(80,631), 尺寸4x16, 长宽比0.25, 面积64
2025-07-29 12:54:42,013 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,630), 尺寸6x11, 长宽比0.55, 面积66
2025-07-29 12:54:42,014 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,630), 尺寸5x11, 长宽比0.45, 面积55
2025-07-29 12:54:42,016 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,630), 尺寸8x11, 长宽比0.73, 面积88
2025-07-29 12:54:42,021 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,630), 尺寸9x11, 长宽比0.82, 面积99
2025-07-29 12:54:42,029 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(185,629), 尺寸24x13, 长宽比1.85, 面积312
2025-07-29 12:54:42,036 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=84.3 (阈值:60)
2025-07-29 12:54:42,040 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,629), 尺寸15x13, 长宽比1.15, 面积195
2025-07-29 12:54:42,042 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=83.6 (阈值:60)
2025-07-29 12:54:42,051 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,629), 尺寸14x13, 长宽比1.08, 面积182
2025-07-29 12:54:42,059 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=78.1 (阈值:60)
2025-07-29 12:54:42,060 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,612), 尺寸17x1, 长宽比17.00, 面积17
2025-07-29 12:54:42,061 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,610), 尺寸18x1, 长宽比18.00, 面积18
2025-07-29 12:54:42,063 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,604), 尺寸18x3, 长宽比6.00, 面积54
2025-07-29 12:54:42,076 - WeChatAutoAdd - INFO - 重要轮廓: 位置(614,602), 尺寸96x32, 长宽比3.00, 已知特征:False
2025-07-29 12:54:42,080 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度96 (需要70-95)
2025-07-29 12:54:42,087 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,601), 尺寸18x1, 长宽比18.00, 面积18
2025-07-29 12:54:42,092 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,599), 尺寸17x1, 长宽比17.00, 面积17
2025-07-29 12:54:42,093 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(200,594), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 12:54:42,095 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(188,593), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 12:54:42,096 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,592), 尺寸1x2, 长宽比0.50, 面积2
2025-07-29 12:54:42,098 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(245,592), 尺寸2x2, 长宽比1.00, 面积4
2025-07-29 12:54:42,099 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(243,592), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:54:42,100 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(241,592), 尺寸1x2, 长宽比0.50, 面积2
2025-07-29 12:54:42,106 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(124,592), 尺寸4x4, 长宽比1.00, 面积16
2025-07-29 12:54:42,107 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,591), 尺寸4x3, 长宽比1.33, 面积12
2025-07-29 12:54:42,108 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,590), 尺寸3x3, 长宽比1.00, 面积9
2025-07-29 12:54:42,109 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(181,589), 尺寸1x5, 长宽比0.20, 面积5
2025-07-29 12:54:42,113 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(127,589), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:54:42,115 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(226,588), 尺寸4x6, 长宽比0.67, 面积24
2025-07-29 12:54:42,116 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(224,588), 尺寸3x2, 长宽比1.50, 面积6
2025-07-29 12:54:42,117 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,588), 尺寸3x1, 长宽比3.00, 面积3
2025-07-29 12:54:42,123 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(146,588), 尺寸2x2, 长宽比1.00, 面积4
2025-07-29 12:54:42,124 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(143,588), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:54:42,125 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(191,587), 尺寸6x8, 长宽比0.75, 面积48
2025-07-29 12:54:42,127 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(179,585), 尺寸11x9, 长宽比1.22, 面积99
2025-07-29 12:54:42,128 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(147,585), 尺寸2x2, 长宽比1.00, 面积4
2025-07-29 12:54:42,130 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(198,584), 尺寸27x11, 长宽比2.45, 面积297
2025-07-29 12:54:42,131 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(122,584), 尺寸45x12, 长宽比3.75, 面积540
2025-07-29 12:54:42,133 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,584), 尺寸4x11, 长宽比0.36, 面积44
2025-07-29 12:54:42,137 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,583), 尺寸11x13, 长宽比0.85, 面积143
2025-07-29 12:54:42,139 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(153,583), 尺寸8x11, 长宽比0.73, 面积88
2025-07-29 12:54:42,145 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(147,583), 尺寸7x3, 长宽比2.33, 面积21
2025-07-29 12:54:42,147 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(143,583), 尺寸3x3, 长宽比1.00, 面积9
2025-07-29 12:54:42,148 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,583), 尺寸13x11, 长宽比1.18, 面积143
2025-07-29 12:54:42,149 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(221,571), 尺寸1x2, 长宽比0.50, 面积2
2025-07-29 12:54:42,150 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(216,571), 尺寸3x2, 长宽比1.50, 面积6
2025-07-29 12:54:42,156 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,570), 尺寸7x4, 长宽比1.75, 面积28
2025-07-29 12:54:42,158 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(126,570), 尺寸2x3, 长宽比0.67, 面积6
2025-07-29 12:54:42,160 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(205,568), 尺寸2x5, 长宽比0.40, 面积10
2025-07-29 12:54:42,161 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(133,568), 尺寸2x5, 长宽比0.40, 面积10
2025-07-29 12:54:42,163 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(171,565), 尺寸4x4, 长宽比1.00, 面积16
2025-07-29 12:54:42,165 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(254,564), 尺寸4x8, 长宽比0.50, 面积32
2025-07-29 12:54:42,166 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(206,564), 尺寸8x10, 长宽比0.80, 面积80
2025-07-29 12:54:42,170 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,564), 尺寸6x10, 长宽比0.60, 面积60
2025-07-29 12:54:42,175 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(242,563), 尺寸11x11, 长宽比1.00, 面积121
2025-07-29 12:54:42,179 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(233,563), 尺寸10x9, 长宽比1.11, 面积90
2025-07-29 12:54:42,182 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(231,563), 尺寸6x10, 长宽比0.60, 面积60
2025-07-29 12:54:42,186 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(141,563), 尺寸10x11, 长宽比0.91, 面积110
2025-07-29 12:54:42,193 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(199,562), 尺寸6x11, 长宽比0.55, 面积66
2025-07-29 12:54:42,196 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(196,562), 尺寸5x6, 长宽比0.83, 面积30
2025-07-29 12:54:42,198 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(191,562), 尺寸7x11, 长宽比0.64, 面积77
2025-07-29 12:54:42,199 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(151,562), 尺寸28x11, 长宽比2.55, 面积308
2025-07-29 12:54:42,200 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=78.7 (阈值:60)
2025-07-29 12:54:42,208 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(120,561), 尺寸12x12, 长宽比1.00, 面积144
2025-07-29 12:54:42,212 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=78.6 (阈值:60)
2025-07-29 12:54:42,216 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,559), 尺寸38x38, 长宽比1.00, 面积1444
2025-07-29 12:54:42,218 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=71.2 (阈值:60)
2025-07-29 12:54:42,225 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,547), 尺寸16x22, 长宽比0.73, 面积352
2025-07-29 12:54:42,230 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(161,526), 尺寸4x1, 长宽比4.00, 面积4
2025-07-29 12:54:42,231 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,524), 尺寸4x2, 长宽比2.00, 面积8
2025-07-29 12:54:42,232 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,523), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 12:54:42,234 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,522), 尺寸3x2, 长宽比1.50, 面积6
2025-07-29 12:54:42,248 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(161,522), 尺寸3x2, 长宽比1.50, 面积6
2025-07-29 12:54:42,250 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,521), 尺寸2x3, 长宽比0.67, 面积6
2025-07-29 12:54:42,262 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(155,521), 尺寸5x7, 长宽比0.71, 面积35
2025-07-29 12:54:42,263 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(143,521), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:54:42,265 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(146,520), 尺寸1x2, 长宽比0.50, 面积2
2025-07-29 12:54:42,267 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(179,519), 尺寸10x7, 长宽比1.43, 面积70
2025-07-29 12:54:42,272 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(162,519), 尺寸3x2, 长宽比1.50, 面积6
2025-07-29 12:54:42,276 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(196,518), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 12:54:42,278 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,518), 尺寸1x5, 长宽比0.20, 面积5
2025-07-29 12:54:42,281 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,518), 尺寸4x2, 长宽比2.00, 面积8
2025-07-29 12:54:42,283 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(150,518), 尺寸1x9, 长宽比0.11, 面积9
2025-07-29 12:54:42,285 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(180,516), 尺寸10x2, 长宽比5.00, 面积20
2025-07-29 12:54:42,293 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(155,516), 尺寸29x12, 长宽比2.42, 面积348
2025-07-29 12:54:42,294 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=516 (距底部154像素区域)
2025-07-29 12:54:42,296 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-29 12:54:42,298 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,516), 尺寸4x13, 长宽比0.31, 面积52
2025-07-29 12:54:42,299 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(185,515), 尺寸42x13, 长宽比3.23, 面积546
2025-07-29 12:54:42,301 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=515 (距底部154像素区域)
2025-07-29 12:54:42,307 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-29 12:54:42,308 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(123,515), 尺寸26x14, 长宽比1.86, 面积364
2025-07-29 12:54:42,309 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=515 (距底部154像素区域)
2025-07-29 12:54:42,310 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-29 12:54:42,312 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,505), 尺寸8x1, 长宽比8.00, 面积8
2025-07-29 12:54:42,313 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,503), 尺寸9x1, 长宽比9.00, 面积9
2025-07-29 12:54:42,314 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,502), 尺寸8x4, 长宽比2.00, 面积32
2025-07-29 12:54:42,315 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(139,502), 尺寸6x2, 长宽比3.00, 面积12
2025-07-29 12:54:42,316 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,499), 尺寸8x3, 长宽比2.67, 面积24
2025-07-29 12:54:42,325 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(163,498), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:54:42,332 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(154,498), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:54:42,333 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,497), 尺寸1x4, 长宽比0.25, 面积4
2025-07-29 12:54:42,338 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(229,495), 尺寸29x11, 长宽比2.64, 面积319
2025-07-29 12:54:42,343 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=495 (距底部154像素区域)
2025-07-29 12:54:42,344 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-29 12:54:42,347 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(163,495), 尺寸2x2, 长宽比1.00, 面积4
2025-07-29 12:54:42,348 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,495), 尺寸10x10, 长宽比1.00, 面积100
2025-07-29 12:54:42,349 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=495 (距底部154像素区域)
2025-07-29 12:54:42,352 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-29 12:54:42,355 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(170,494), 尺寸5x11, 长宽比0.45, 面积55
2025-07-29 12:54:42,366 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(168,494), 尺寸1x7, 长宽比0.14, 面积7
2025-07-29 12:54:42,377 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(161,493), 尺寸6x13, 长宽比0.46, 面积78
2025-07-29 12:54:42,379 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,493), 尺寸10x1, 长宽比10.00, 面积10
2025-07-29 12:54:42,382 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,493), 尺寸41x14, 长宽比2.93, 面积574
2025-07-29 12:54:42,383 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=493 (距底部154像素区域)
2025-07-29 12:54:42,385 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-29 12:54:42,393 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,491), 尺寸37x37, 长宽比1.00, 面积1369
2025-07-29 12:54:42,394 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=491 (距底部154像素区域)
2025-07-29 12:54:42,395 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-29 12:54:42,397 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,456), 尺寸1x2, 长宽比0.50, 面积2
2025-07-29 12:54:42,398 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(247,456), 尺寸1x2, 长宽比0.50, 面积2
2025-07-29 12:54:42,399 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(244,456), 尺寸2x2, 长宽比1.00, 面积4
2025-07-29 12:54:42,400 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(203,456), 尺寸9x3, 长宽比3.00, 面积27
2025-07-29 12:54:42,412 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(208,454), 尺寸3x3, 长宽比1.00, 面积9
2025-07-29 12:54:42,421 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(203,454), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:54:42,432 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(175,454), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:54:42,444 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,454), 尺寸10x8, 长宽比1.25, 面积80
2025-07-29 12:54:42,449 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(201,453), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:54:42,459 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(158,453), 尺寸4x5, 长宽比0.80, 面积20
2025-07-29 12:54:42,467 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,452), 尺寸4x4, 长宽比1.00, 面积16
2025-07-29 12:54:42,473 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,452), 尺寸7x4, 长宽比1.75, 面积28
2025-07-29 12:54:42,475 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,452), 尺寸8x7, 长宽比1.14, 面积56
2025-07-29 12:54:42,478 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,452), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:54:42,482 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,452), 尺寸1x3, 长宽比0.33, 面积3
2025-07-29 12:54:42,488 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,452), 尺寸6x10, 长宽比0.60, 面积60
2025-07-29 12:54:42,490 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,451), 尺寸5x4, 长宽比1.25, 面积20
2025-07-29 12:54:42,496 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(159,451), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 12:54:42,504 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,451), 尺寸5x4, 长宽比1.25, 面积20
2025-07-29 12:54:42,506 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(132,451), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:54:42,508 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(123,451), 尺寸8x7, 长宽比1.14, 面积56
2025-07-29 12:54:42,510 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(171,450), 尺寸1x2, 长宽比0.50, 面积2
2025-07-29 12:54:42,511 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(230,449), 尺寸13x11, 长宽比1.18, 面积143
2025-07-29 12:54:42,513 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,449), 尺寸5x1, 长宽比5.00, 面积5
2025-07-29 12:54:42,515 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,449), 尺寸23x11, 长宽比2.09, 面积253
2025-07-29 12:54:42,521 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,448), 尺寸17x12, 长宽比1.42, 面积204
2025-07-29 12:54:42,523 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(173,448), 尺寸10x8, 长宽比1.25, 面积80
2025-07-29 12:54:42,525 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,447), 尺寸19x13, 长宽比1.46, 面积247
2025-07-29 12:54:42,538 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,447), 尺寸11x13, 长宽比0.85, 面积143
2025-07-29 12:54:42,577 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(159,447), 尺寸13x3, 长宽比4.33, 面积39
2025-07-29 12:54:42,581 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(218,435), 尺寸1x2, 长宽比0.50, 面积2
2025-07-29 12:54:42,583 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,435), 尺寸3x2, 长宽比1.50, 面积6
2025-07-29 12:54:42,588 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(152,435), 尺寸2x2, 长宽比1.00, 面积4
2025-07-29 12:54:42,594 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(253,431), 尺寸2x2, 长宽比1.00, 面积4
2025-07-29 12:54:42,595 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(186,429), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:54:42,596 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(229,427), 尺寸30x11, 长宽比2.73, 面积330
2025-07-29 12:54:42,599 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(186,427), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:54:42,600 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(188,426), 尺寸3x11, 长宽比0.27, 面积33
2025-07-29 12:54:42,614 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,426), 尺寸6x11, 长宽比0.55, 面积66
2025-07-29 12:54:42,616 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,426), 尺寸5x11, 长宽比0.45, 面积55
2025-07-29 12:54:42,622 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,426), 尺寸17x11, 长宽比1.55, 面积187
2025-07-29 12:54:42,624 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=87.1 (阈值:60)
2025-07-29 12:54:42,625 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(202,425), 尺寸9x13, 长宽比0.69, 面积117
2025-07-29 12:54:42,626 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,425), 尺寸7x13, 长宽比0.54, 面积91
2025-07-29 12:54:42,629 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(153,425), 尺寸15x13, 长宽比1.15, 面积195
2025-07-29 12:54:42,630 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=88.5 (阈值:60)
2025-07-29 12:54:42,632 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,425), 尺寸14x13, 长宽比1.08, 面积182
2025-07-29 12:54:42,638 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=78.1 (阈值:60)
2025-07-29 12:54:42,645 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,423), 尺寸38x38, 长宽比1.00, 面积1444
2025-07-29 12:54:42,648 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=71.2 (阈值:60)
2025-07-29 12:54:42,656 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,405), 尺寸726x245, 长宽比2.96, 面积177870
2025-07-29 12:54:42,658 - WeChatAutoAdd - INFO - 底部区域找到 23 个按钮候选
2025-07-29 12:54:42,664 - WeChatAutoAdd - INFO - 选择特殊位置按钮: Y=491 (距底部154像素)
2025-07-29 12:54:42,665 - WeChatAutoAdd - INFO - 在底部找到按钮: (90, 509), 尺寸: 37x37, 位置得分: 3.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-07-29 12:54:42,666 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-29 12:54:42,698 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_125442.png
2025-07-29 12:54:42,699 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-29 12:54:42,701 - WeChatAutoAdd - INFO - 开始验证按钮区域(90, 509)是否包含'添加到通讯录'文字
2025-07-29 12:54:42,709 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250729_125442.png
2025-07-29 12:54:42,738 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-29 12:54:42,740 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0508, 平均亮度=227.0, 亮度标准差=45.1
2025-07-29 12:54:42,742 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-29 12:54:42,743 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 12:54:43,045 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(90, 509) -> 屏幕坐标(90, 509)
2025-07-29 12:54:43,822 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 12:54:43,823 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 12:54:43,825 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:54:43,825 - modules.wechat_auto_add_simple - INFO - ✅ 17543599907 添加朋友操作执行成功
2025-07-29 12:54:43,826 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:54:43,826 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 12:54:45,828 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:54:45,828 - modules.wechat_auto_add_simple - INFO - ℹ️ 17543599907 未发现申请添加朋友窗口，可能直接添加成功
2025-07-29 12:54:45,829 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 17543599907
2025-07-29 12:54:45,830 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:54:49,316 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 3/2959
2025-07-29 12:54:49,319 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18151680519 (邓僚僚)
2025-07-29 12:54:49,320 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:54:55,883 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18151680519
2025-07-29 12:54:55,885 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 12:54:55,885 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18151680519 执行添加朋友操作...
2025-07-29 12:54:55,886 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 12:54:55,886 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 12:54:55,887 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 12:54:55,889 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 12:54:55,897 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-29 12:54:55,903 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 12:54:55,903 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 12:54:55,903 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 12:54:55,904 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 12:54:55,907 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 12:54:55,908 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 12:54:55,908 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 12:54:55,912 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 12:54:55,914 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 12:54:55,919 - WeChatAutoAdd - INFO - 共找到 2 个微信窗口
2025-07-29 12:54:55,922 - WeChatAutoAdd - INFO - 找到微信主窗口: 微信
2025-07-29 12:54:56,426 - WeChatAutoAdd - INFO - 目标窗口: 微信
2025-07-29 12:54:56,427 - WeChatAutoAdd - INFO - 窗口位置: (0, 0), 尺寸: 726x650
2025-07-29 12:54:56,509 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差36.68, 边缘比例0.0599
2025-07-29 12:54:56,543 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_125456.png
2025-07-29 12:54:56,547 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 12:54:56,553 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 12:54:56,559 - WeChatAutoAdd - INFO - 截图尺寸: 726x650
2025-07-29 12:54:56,561 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=405-650 (高度:245)
2025-07-29 12:54:56,562 - WeChatAutoAdd - INFO - 特别关注区域: Y=466-526 (距底部154像素)
2025-07-29 12:54:56,571 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_125456.png
2025-07-29 12:54:56,575 - WeChatAutoAdd - INFO - 底部区域原始检测到 91 个轮廓
2025-07-29 12:54:56,576 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,612), 尺寸17x1, 长宽比17.00, 面积17
2025-07-29 12:54:56,577 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,610), 尺寸18x1, 长宽比18.00, 面积18
2025-07-29 12:54:56,579 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,604), 尺寸18x3, 长宽比6.00, 面积54
2025-07-29 12:54:56,581 - WeChatAutoAdd - INFO - 重要轮廓: 位置(614,602), 尺寸96x32, 长宽比3.00, 已知特征:False
2025-07-29 12:54:56,585 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度96 (需要70-95)
2025-07-29 12:54:56,590 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,601), 尺寸18x1, 长宽比18.00, 面积18
2025-07-29 12:54:56,592 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,599), 尺寸17x1, 长宽比17.00, 面积17
2025-07-29 12:54:56,595 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,547), 尺寸16x22, 长宽比0.73, 面积352
2025-07-29 12:54:56,596 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(161,526), 尺寸4x1, 长宽比4.00, 面积4
2025-07-29 12:54:56,598 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,524), 尺寸4x2, 长宽比2.00, 面积8
2025-07-29 12:54:56,599 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,523), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 12:54:56,606 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,522), 尺寸3x2, 长宽比1.50, 面积6
2025-07-29 12:54:56,782 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(161,522), 尺寸3x2, 长宽比1.50, 面积6
2025-07-29 12:54:56,811 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,521), 尺寸2x3, 长宽比0.67, 面积6
2025-07-29 12:54:56,813 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(155,521), 尺寸5x7, 长宽比0.71, 面积35
2025-07-29 12:54:56,814 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(143,521), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:54:56,821 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(146,520), 尺寸1x2, 长宽比0.50, 面积2
2025-07-29 12:54:56,824 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(179,519), 尺寸10x7, 长宽比1.43, 面积70
2025-07-29 12:54:56,826 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(162,519), 尺寸3x2, 长宽比1.50, 面积6
2025-07-29 12:54:56,830 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(196,518), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 12:54:56,837 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,518), 尺寸1x5, 长宽比0.20, 面积5
2025-07-29 12:54:56,841 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,518), 尺寸4x2, 长宽比2.00, 面积8
2025-07-29 12:54:56,846 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(150,518), 尺寸1x9, 长宽比0.11, 面积9
2025-07-29 12:54:56,848 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(180,516), 尺寸10x2, 长宽比5.00, 面积20
2025-07-29 12:54:56,855 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(155,516), 尺寸29x12, 长宽比2.42, 面积348
2025-07-29 12:54:56,857 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=516 (距底部154像素区域)
2025-07-29 12:54:56,861 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-29 12:54:56,863 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,516), 尺寸4x13, 长宽比0.31, 面积52
2025-07-29 12:54:56,865 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(185,515), 尺寸42x13, 长宽比3.23, 面积546
2025-07-29 12:54:56,875 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=515 (距底部154像素区域)
2025-07-29 12:54:56,880 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-29 12:54:56,881 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(123,515), 尺寸26x14, 长宽比1.86, 面积364
2025-07-29 12:54:56,882 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=515 (距底部154像素区域)
2025-07-29 12:54:56,893 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-29 12:54:56,895 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,505), 尺寸8x1, 长宽比8.00, 面积8
2025-07-29 12:54:56,900 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,503), 尺寸9x1, 长宽比9.00, 面积9
2025-07-29 12:54:56,906 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,502), 尺寸8x4, 长宽比2.00, 面积32
2025-07-29 12:54:56,907 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(139,502), 尺寸6x2, 长宽比3.00, 面积12
2025-07-29 12:54:56,909 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,499), 尺寸8x3, 长宽比2.67, 面积24
2025-07-29 12:54:56,911 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(163,498), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:54:56,913 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(154,498), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:54:56,915 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,497), 尺寸1x4, 长宽比0.25, 面积4
2025-07-29 12:54:56,922 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(229,495), 尺寸29x11, 长宽比2.64, 面积319
2025-07-29 12:54:56,925 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=495 (距底部154像素区域)
2025-07-29 12:54:56,928 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-29 12:54:56,929 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(163,495), 尺寸2x2, 长宽比1.00, 面积4
2025-07-29 12:54:56,930 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,495), 尺寸10x10, 长宽比1.00, 面积100
2025-07-29 12:54:56,932 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=495 (距底部154像素区域)
2025-07-29 12:54:56,937 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-29 12:54:56,939 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(170,494), 尺寸5x11, 长宽比0.45, 面积55
2025-07-29 12:54:56,940 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(168,494), 尺寸1x7, 长宽比0.14, 面积7
2025-07-29 12:54:56,941 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,493), 尺寸7x13, 长宽比0.54, 面积91
2025-07-29 12:54:56,943 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,493), 尺寸10x1, 长宽比10.00, 面积10
2025-07-29 12:54:56,944 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,493), 尺寸41x14, 长宽比2.93, 面积574
2025-07-29 12:54:56,945 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=493 (距底部154像素区域)
2025-07-29 12:54:56,946 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-29 12:54:56,948 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,491), 尺寸37x37, 长宽比1.00, 面积1369
2025-07-29 12:54:56,955 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=491 (距底部154像素区域)
2025-07-29 12:54:56,957 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-29 12:54:56,958 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,456), 尺寸1x2, 长宽比0.50, 面积2
2025-07-29 12:54:56,959 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(247,456), 尺寸1x2, 长宽比0.50, 面积2
2025-07-29 12:54:56,961 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(244,456), 尺寸2x2, 长宽比1.00, 面积4
2025-07-29 12:54:56,962 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(203,456), 尺寸9x3, 长宽比3.00, 面积27
2025-07-29 12:54:56,963 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(208,454), 尺寸3x3, 长宽比1.00, 面积9
2025-07-29 12:54:56,964 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(203,454), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:54:56,972 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(175,454), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:54:56,974 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,454), 尺寸10x8, 长宽比1.25, 面积80
2025-07-29 12:54:56,976 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(201,453), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:54:56,980 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(158,453), 尺寸4x5, 长宽比0.80, 面积20
2025-07-29 12:54:56,982 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,452), 尺寸4x4, 长宽比1.00, 面积16
2025-07-29 12:54:56,983 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,452), 尺寸7x4, 长宽比1.75, 面积28
2025-07-29 12:54:56,988 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,452), 尺寸8x7, 长宽比1.14, 面积56
2025-07-29 12:54:56,991 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,452), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:54:56,994 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,452), 尺寸1x3, 长宽比0.33, 面积3
2025-07-29 12:54:56,998 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,452), 尺寸6x10, 长宽比0.60, 面积60
2025-07-29 12:54:57,004 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,451), 尺寸5x4, 长宽比1.25, 面积20
2025-07-29 12:54:57,006 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(159,451), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 12:54:57,008 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,451), 尺寸5x4, 长宽比1.25, 面积20
2025-07-29 12:54:57,010 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(132,451), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:54:57,013 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(123,451), 尺寸8x7, 长宽比1.14, 面积56
2025-07-29 12:54:57,016 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(171,450), 尺寸1x2, 长宽比0.50, 面积2
2025-07-29 12:54:57,023 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(230,449), 尺寸13x11, 长宽比1.18, 面积143
2025-07-29 12:54:57,025 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,449), 尺寸5x1, 长宽比5.00, 面积5
2025-07-29 12:54:57,026 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,449), 尺寸23x11, 长宽比2.09, 面积253
2025-07-29 12:54:57,033 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,448), 尺寸17x12, 长宽比1.42, 面积204
2025-07-29 12:54:57,040 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(173,448), 尺寸10x8, 长宽比1.25, 面积80
2025-07-29 12:54:57,043 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,447), 尺寸19x13, 长宽比1.46, 面积247
2025-07-29 12:54:57,047 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,447), 尺寸11x13, 长宽比0.85, 面积143
2025-07-29 12:54:57,056 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(159,447), 尺寸13x3, 长宽比4.33, 面积39
2025-07-29 12:54:57,058 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(218,435), 尺寸1x2, 长宽比0.50, 面积2
2025-07-29 12:54:57,060 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,435), 尺寸3x2, 长宽比1.50, 面积6
2025-07-29 12:54:57,064 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(152,435), 尺寸2x2, 长宽比1.00, 面积4
2025-07-29 12:54:57,065 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(253,431), 尺寸2x2, 长宽比1.00, 面积4
2025-07-29 12:54:57,066 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(186,429), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:54:57,072 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(229,427), 尺寸30x11, 长宽比2.73, 面积330
2025-07-29 12:54:57,075 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(186,427), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:54:57,077 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(188,426), 尺寸3x11, 长宽比0.27, 面积33
2025-07-29 12:54:57,079 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,426), 尺寸6x11, 长宽比0.55, 面积66
2025-07-29 12:54:57,080 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,426), 尺寸5x11, 长宽比0.45, 面积55
2025-07-29 12:54:57,082 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,426), 尺寸17x11, 长宽比1.55, 面积187
2025-07-29 12:54:57,086 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=87.1 (阈值:60)
2025-07-29 12:54:57,090 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(202,425), 尺寸9x13, 长宽比0.69, 面积117
2025-07-29 12:54:57,092 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,425), 尺寸7x13, 长宽比0.54, 面积91
2025-07-29 12:54:57,093 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(153,425), 尺寸15x13, 长宽比1.15, 面积195
2025-07-29 12:54:57,094 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=88.5 (阈值:60)
2025-07-29 12:54:57,096 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,425), 尺寸14x13, 长宽比1.08, 面积182
2025-07-29 12:54:57,097 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=78.1 (阈值:60)
2025-07-29 12:54:57,099 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,423), 尺寸38x38, 长宽比1.00, 面积1444
2025-07-29 12:54:57,103 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=71.2 (阈值:60)
2025-07-29 12:54:57,105 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,405), 尺寸726x245, 长宽比2.96, 面积177870
2025-07-29 12:54:57,106 - WeChatAutoAdd - INFO - 底部区域找到 15 个按钮候选
2025-07-29 12:54:57,107 - WeChatAutoAdd - INFO - 选择特殊位置按钮: Y=491 (距底部154像素)
2025-07-29 12:54:57,108 - WeChatAutoAdd - INFO - 在底部找到按钮: (90, 509), 尺寸: 37x37, 位置得分: 3.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-07-29 12:54:57,111 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-29 12:54:57,136 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_125457.png
2025-07-29 12:54:57,139 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-29 12:54:57,140 - WeChatAutoAdd - INFO - 开始验证按钮区域(90, 509)是否包含'添加到通讯录'文字
2025-07-29 12:54:57,144 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250729_125457.png
2025-07-29 12:54:57,171 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-29 12:54:57,180 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0509, 平均亮度=215.2, 亮度标准差=45.9
2025-07-29 12:54:57,182 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-29 12:54:57,192 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 12:54:57,495 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(90, 509) -> 屏幕坐标(90, 509)
2025-07-29 12:54:58,271 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 12:54:58,272 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 12:54:58,274 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:54:58,274 - modules.wechat_auto_add_simple - INFO - ✅ 18151680519 添加朋友操作执行成功
2025-07-29 12:54:58,274 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:54:58,274 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 12:55:00,276 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:55:00,276 - modules.wechat_auto_add_simple - INFO - ℹ️ 18151680519 未发现申请添加朋友窗口，可能直接添加成功
2025-07-29 12:55:00,277 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 18151680519
2025-07-29 12:55:00,278 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
