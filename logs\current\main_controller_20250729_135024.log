2025-07-29 13:50:24,737 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 13:50:24,738 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 13:50:24,739 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-29 13:50:24,739 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 13:50:24,741 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-29 13:50:24,741 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 13:50:24,742 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 13:50:24,743 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 13:50:24,744 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 13:50:24,744 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 13:50:24,745 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:50:24,749 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 13:50:24,755 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250729_135024.log
2025-07-29 13:50:24,756 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 13:50:24,757 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-29 13:50:24,757 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-29 13:50:24,758 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-29 13:50:24,758 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-29 13:50:24,758 - __main__ - INFO - 📅 当前北京时间: 2025-07-29 21:50:24
2025-07-29 13:50:24,759 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-29 13:50:24,759 - __main__ - INFO - 📅 启动时间: 2025-07-29 21:50:24
2025-07-29 13:50:24,760 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-29 13:50:24,760 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 13:50:25,295 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 13:50:25,296 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 13:50:25,836 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 13:50:25,837 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 13:50:25,839 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-29 13:50:25,839 - __main__ - INFO - ✅ 找到 2 个微信窗口
2025-07-29 13:50:25,840 - __main__ - INFO -   窗口 1: 微信 (句柄: 197994)
2025-07-29 13:50:25,840 - __main__ - INFO -   窗口 2: 微信 (句柄: 525668)
2025-07-29 13:50:25,840 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-29 13:50:26,836 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-29 13:50:26,837 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-29 13:50:26,837 - __main__ - INFO - 📋 待处理联系人数: 2960
2025-07-29 13:50:26,838 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-29 13:50:26,838 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2960
2025-07-29 13:50:26,839 - __main__ - INFO - 
============================================================
2025-07-29 13:50:26,839 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口
2025-07-29 13:50:26,840 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-29 13:50:26,840 - __main__ - INFO - ============================================================
2025-07-29 13:50:26,840 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-29 13:50:26,841 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-29 13:50:26,841 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-29 13:50:26,842 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-29 13:50:26,842 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 13:50:27,167 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 13:50:27,208 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 13:50:27,232 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 13:50:27,240 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 13:50:27,269 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 13:50:27,274 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 13:50:27,275 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 13:50:27,276 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 13:50:27,276 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 13:50:27,277 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 13:50:27,485 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 13:50:27,485 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 13:50:27,486 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-29 13:50:27,486 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 13:50:27,790 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 13:50:27,790 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 13:50:27,791 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 13:50:27,791 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 13:50:27,792 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 13:50:27,792 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 13:50:27,793 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 13:50:27,793 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 13:50:27,794 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 13:50:27,794 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 13:50:27,996 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 13:50:27,996 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 13:50:27,997 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-29 13:50:28,298 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 13:50:28,299 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 13:50:28,300 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 13:50:28,301 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 13:50:28,301 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 13:50:28,301 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 13:50:28,302 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-29 13:50:28,303 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-29 13:50:29,304 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-29 13:50:29,304 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-29 13:50:29,304 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-29 13:50:29,305 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-29 13:50:29,305 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-29 13:50:29,306 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-29 13:50:29,306 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-29 13:50:29,306 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-29 13:50:29,507 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-29 13:50:29,516 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-29 13:50:31,898 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-29 13:50:31,899 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-29 13:50:31,900 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.5 秒...
2025-07-29 13:50:34,395 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-29 13:50:34,596 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-29 13:50:34,597 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-29 13:50:37,006 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-29 13:50:37,006 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-29 13:50:37,007 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.7 秒...
2025-07-29 13:50:39,671 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-29 13:50:39,872 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-29 13:50:39,872 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-29 13:50:42,249 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-29 13:50:42,250 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-29 13:50:42,251 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.4 秒...
2025-07-29 13:50:44,659 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-29 13:50:44,861 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-29 13:50:44,861 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-29 13:50:47,231 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-29 13:50:47,231 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-29 13:50:47,232 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.9 秒...
2025-07-29 13:50:50,168 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-29 13:50:50,368 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-29 13:50:50,369 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-29 13:50:52,780 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-29 13:50:52,781 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-29 13:50:52,781 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 13:50:52,781 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 13:50:52,782 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 13:50:52,784 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 13:50:52,784 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 13:50:52,786 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 13:50:52,786 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 13:50:52,788 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 13437356, 进程: Weixin.exe)
2025-07-29 13:50:52,791 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-29 13:50:52,793 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 13437356)
2025-07-29 13:50:52,796 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 13437356) - 增强版
2025-07-29 13:50:53,106 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 13:50:53,106 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 13:50:53,106 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 13:50:53,107 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 13:50:53,107 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-29 13:50:53,107 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 13:50:53,313 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-29 13:50:53,313 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 13:50:53,515 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 13:50:53,515 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 13:50:53,516 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-29 13:50:53,516 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-29 13:50:53,516 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-29 13:50:53,516 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-29 13:50:53,517 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-29 13:50:54,518 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-29 13:50:54,518 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 13:50:54,521 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 13:50:54,522 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 13:50:54,523 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 13:50:54,523 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 13:50:54,525 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 13437356, 进程: Weixin.exe)
2025-07-29 13:50:54,529 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-29 13:50:54,535 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-29 13:50:54,539 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-29 13:50:54,540 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-29 13:50:54,541 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-29 13:50:54,542 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 13:50:54,850 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 13:50:54,850 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 13:50:54,851 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 13:50:54,851 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 13:50:54,852 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 13:50:54,852 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 13:50:54,852 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 13:50:54,853 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 13:50:54,853 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 13:50:54,853 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 13:50:55,055 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 13:50:55,056 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 13:50:55,058 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-29 13:50:55,359 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 13:50:55,359 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-29 13:50:55,360 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-29 13:50:56,361 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-29 13:50:56,362 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-29 13:50:56,362 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-29 13:50:56,365 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250729_135056.log
2025-07-29 13:50:56,366 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 13:50:56,366 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-29 13:50:56,366 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-29 13:50:56,367 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-29 13:50:56,367 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-29 13:50:56,369 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-29 13:50:56,370 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-29 13:50:56,370 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-29 13:50:56,370 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-29 13:50:56,371 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-29 13:50:56,371 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-29 13:50:56,371 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-29 13:50:56,372 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 13:50:56,373 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 13437356
2025-07-29 13:50:56,374 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 13437356) - 增强版
2025-07-29 13:50:56,683 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 13:50:56,683 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 13:50:56,684 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 13:50:56,685 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 13:50:56,685 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-29 13:50:56,685 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 13:50:56,686 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-29 13:50:56,686 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 13:50:56,888 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 13:50:56,888 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 13:50:56,891 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 13437356 (API返回: None)
2025-07-29 13:50:57,192 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 13:50:57,192 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-29 13:50:57,192 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-29 13:50:57,193 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-29 13:50:57,194 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 13:50:57,194 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-29 13:50:57,195 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-29 13:50:57,199 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-29 13:50:57,199 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-29 13:50:57,525 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-29 13:50:57,525 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 13:50:57,815 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2960 个
2025-07-29 13:50:57,816 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2960 个 (总计: 3135 个)
2025-07-29 13:50:57,816 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-29 13:50:57,816 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-29 13:50:57,817 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:50:57,817 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-29 13:50:57,817 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2960
2025-07-29 13:50:57,817 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 17704006423 (刘林蓓)
2025-07-29 13:50:57,818 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:51:04,393 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 17704006423
2025-07-29 13:51:04,393 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 13:51:04,394 - modules.wechat_auto_add_simple - INFO - 👥 开始为 17704006423 执行添加朋友操作...
2025-07-29 13:51:04,394 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 13:51:04,394 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 13:51:04,395 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 13:51:04,396 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 13:51:04,404 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 8 个图片文件
2025-07-29 13:51:04,405 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 13:51:04,405 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 13:51:04,406 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 13:51:04,407 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 13:51:04,408 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 13:51:04,411 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 13:51:04,412 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 13:51:04,415 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 13:51:04,417 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 13:51:04,418 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 13:51:04,424 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 13:51:04,427 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-29 13:51:04,434 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 13:51:04,938 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 13:51:04,939 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 13:51:05,109 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差28.66, 边缘比例0.0350
2025-07-29 13:51:05,159 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_135105.png
2025-07-29 13:51:05,162 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 13:51:05,163 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 13:51:05,165 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 13:51:05,167 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 13:51:05,171 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 13:51:05,190 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_135105.png
2025-07-29 13:51:05,191 - WeChatAutoAdd - INFO - 底部区域原始检测到 38 个轮廓
2025-07-29 13:51:05,195 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 13:51:05,198 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 13:51:05,200 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 13:51:05,201 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-07-29 13:51:05,202 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-07-29 13:51:05,204 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-07-29 13:51:05,206 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-07-29 13:51:05,207 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 13:51:05,208 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-07-29 13:51:05,210 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-07-29 13:51:05,213 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-07-29 13:51:05,216 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 13:51:05,217 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-07-29 13:51:05,218 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 13:51:05,235 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 13:51:05,299 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-29 13:51:05,346 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-29 13:51:05,349 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 13:51:05,353 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 13:51:05,381 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-07-29 13:51:05,400 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-07-29 13:51:05,402 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-07-29 13:51:05,404 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 13:51:05,405 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-07-29 13:51:05,407 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-07-29 13:51:05,411 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-07-29 13:51:05,415 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-07-29 13:51:05,416 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-07-29 13:51:05,417 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-07-29 13:51:05,418 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-07-29 13:51:05,420 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-07-29 13:51:05,422 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-07-29 13:51:05,423 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-07-29 13:51:05,427 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-07-29 13:51:05,428 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-07-29 13:51:05,432 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-07-29 13:51:05,433 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-07-29 13:51:05,436 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 13:51:05,439 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-07-29 13:51:05,445 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-07-29 13:51:05,446 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-29 13:51:05,447 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-29 13:51:05,469 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_135105.png
2025-07-29 13:51:05,470 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-29 13:51:05,471 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-07-29 13:51:05,477 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250729_135105.png
2025-07-29 13:51:05,606 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-29 13:51:05,607 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-07-29 13:51:05,608 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-29 13:51:05,609 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 13:51:05,910 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-07-29 13:51:06,680 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 13:51:06,681 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 13:51:06,682 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:51:06,682 - modules.wechat_auto_add_simple - INFO - ✅ 17704006423 添加朋友操作执行成功
2025-07-29 13:51:06,682 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:51:06,683 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 13:51:08,684 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 13:51:08,685 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 13:51:08,685 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 13:51:08,685 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 13:51:08,685 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 13:51:08,686 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 13:51:08,686 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 13:51:08,686 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 13:51:08,687 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 13:51:08,687 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 17704006423
2025-07-29 13:51:08,691 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 13:51:08,691 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 13:51:08,692 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 13:51:08,693 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 13:51:08,693 - modules.friend_request_window - INFO -    📱 phone: '17704006423'
2025-07-29 13:51:08,694 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 13:51:08,694 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 13:51:09,029 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-29 13:51:09,030 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 13:51:09,031 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 13:51:09,031 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 13:51:09,045 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 17704006423
2025-07-29 13:51:09,051 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 13:51:09,053 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 13:51:09,054 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 13:51:09,054 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 13:51:09,055 - modules.friend_request_window - INFO -    📱 手机号码: 17704006423
2025-07-29 13:51:09,055 - modules.friend_request_window - INFO -    🆔 准考证: 014325110087
2025-07-29 13:51:09,056 - modules.friend_request_window - INFO -    👤 姓名: 刘林蓓
2025-07-29 13:51:09,056 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 13:51:09,057 - modules.friend_request_window - INFO -    📝 备注格式: '014325110087-刘林蓓-2025-07-29 21:51:09'
2025-07-29 13:51:09,057 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 13:51:09,057 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110087-刘林蓓-2025-07-29 21:51:09'
2025-07-29 13:51:09,057 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 13:51:09,063 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-29 13:51:09,064 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-29 13:51:09,068 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:51:09,069 - modules.wechat_auto_add_simple - INFO - ✅ 17704006423 申请添加朋友窗口处理完成: 申请添加朋友窗口处理失败: 未找到好友申请窗口
2025-07-29 13:51:09,070 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 17704006423
2025-07-29 13:51:09,071 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:51:12,632 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2960
2025-07-29 13:51:12,633 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13886107212 (黄欣)
2025-07-29 13:51:12,633 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:51:19,239 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13886107212
2025-07-29 13:51:19,240 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 13:51:19,240 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13886107212 执行添加朋友操作...
2025-07-29 13:51:19,240 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 13:51:19,240 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 13:51:19,241 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 13:51:19,243 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 13:51:19,248 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-29 13:51:19,250 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 13:51:19,250 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 13:51:19,252 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 13:51:19,253 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 13:51:19,253 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 13:51:19,255 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 13:51:19,256 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 13:51:19,259 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 13:51:19,262 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 13:51:19,264 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 13:51:19,270 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 13:51:19,272 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-29 13:51:19,273 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 13:51:19,778 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 13:51:19,779 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 13:51:19,883 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差39.14, 边缘比例0.0422
2025-07-29 13:51:19,891 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_135119.png
2025-07-29 13:51:19,893 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 13:51:19,898 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 13:51:19,902 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 13:51:19,905 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 13:51:19,907 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 13:51:19,913 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_135119.png
2025-07-29 13:51:19,916 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-29 13:51:19,918 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,244), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-29 13:51:19,922 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-29 13:51:19,923 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 13:51:19,924 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-29 13:51:19,927 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-07-29 13:51:19,930 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 259), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-29 13:51:19,933 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-29 13:51:19,941 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_135119.png
2025-07-29 13:51:19,944 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-29 13:51:19,947 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 259)是否包含'添加到通讯录'文字
2025-07-29 13:51:19,952 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250729_135119.png
2025-07-29 13:51:19,989 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-29 13:51:19,999 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-29 13:51:20,000 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-29 13:51:20,001 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 13:51:20,303 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 259) -> 屏幕坐标(1364, 259)
2025-07-29 13:51:21,078 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 13:51:21,079 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 13:51:21,080 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:51:21,081 - modules.wechat_auto_add_simple - INFO - ✅ 13886107212 添加朋友操作执行成功
2025-07-29 13:51:21,081 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:51:21,082 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 13:51:23,083 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 13:51:23,084 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 13:51:23,084 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 13:51:23,084 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 13:51:23,085 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 13:51:23,085 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 13:51:23,085 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 13:51:23,086 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 13:51:23,086 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 13:51:23,086 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13886107212
2025-07-29 13:51:23,087 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 13:51:23,087 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 13:51:23,087 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 13:51:23,088 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 13:51:23,088 - modules.friend_request_window - INFO -    📱 phone: '13886107212'
2025-07-29 13:51:23,088 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 13:51:23,089 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 13:51:23,784 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-29 13:51:23,784 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 13:51:23,784 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 13:51:23,785 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 13:51:23,786 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13886107212
2025-07-29 13:51:23,787 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 13:51:23,787 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 13:51:23,788 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 13:51:23,788 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 13:51:23,788 - modules.friend_request_window - INFO -    📱 手机号码: 13886107212
2025-07-29 13:51:23,789 - modules.friend_request_window - INFO -    🆔 准考证: 014325110088
2025-07-29 13:51:23,790 - modules.friend_request_window - INFO -    👤 姓名: 黄欣
2025-07-29 13:51:23,790 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 13:51:23,791 - modules.friend_request_window - INFO -    📝 备注格式: '014325110088-黄欣-2025-07-29 21:51:23'
2025-07-29 13:51:23,793 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 13:51:23,794 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110088-黄欣-2025-07-29 21:51:23'
2025-07-29 13:51:23,794 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 13:51:23,796 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 461956, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-29 13:51:23,798 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 461956)
2025-07-29 13:51:23,798 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-29 13:51:23,801 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-29 13:51:23,804 - modules.friend_request_window - INFO - 🔄 激活窗口: 461956
2025-07-29 13:51:24,507 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-29 13:51:24,507 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-29 13:51:24,509 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-29 13:51:24,510 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-29 13:51:24,510 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-29 13:51:24,510 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 13:51:24,511 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-29 13:51:24,511 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 13:51:24,511 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-29 13:51:24,512 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-29 13:51:24,512 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-29 13:51:24,512 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-29 13:51:24,512 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-29 13:51:24,513 - modules.friend_request_window - INFO -    📝 remark参数: '014325110088-黄欣-2025-07-29 21:51:23' (类型: <class 'str'>, 长度: 35)
2025-07-29 13:51:24,513 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-29 13:51:24,513 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110088-黄欣-2025-07-29 21:51:23'
2025-07-29 13:51:24,513 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-29 13:51:24,514 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-29 13:51:24,514 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-29 13:51:24,514 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-29 13:51:24,515 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-29 13:51:24,515 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-29 13:51:24,516 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-29 13:51:25,430 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-29 13:51:30,676 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-29 13:51:30,676 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-29 13:51:30,677 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-29 13:51:30,677 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-29 13:51:30,678 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '...' (前50字符)
2025-07-29 13:51:30,991 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 13:51:30,992 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 13:51:31,894 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 13:51:31,895 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-29 13:51:31,895 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-29 13:51:31,895 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-29 13:51:31,896 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-29 13:51:32,397 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-29 13:51:32,397 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-29 13:51:32,397 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-29 13:51:32,398 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-29 13:51:32,398 - modules.friend_request_window - INFO -    📝 内容: '014325110088-黄欣-2025-07-29 21:51:23'
2025-07-29 13:51:32,398 - modules.friend_request_window - INFO -    📏 内容长度: 35 字符
2025-07-29 13:51:32,398 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110088-\xe9\xbb\x84\xe6\xac\xa3-2025-07-29 21:51:23'
2025-07-29 13:51:32,399 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-29 13:51:33,359 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-29 13:51:38,604 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-29 13:51:38,605 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-29 13:51:38,605 - modules.friend_request_window - INFO -    📝 原始文本: '014325110088-黄欣-2025-07-29 21:51:23'
2025-07-29 13:51:38,605 - modules.friend_request_window - INFO -    📏 文本长度: 35 字符
2025-07-29 13:51:38,606 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '学习指导【视频作业】...' (前50字符)
2025-07-29 13:51:38,919 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 13:51:38,919 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 13:51:39,823 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 13:51:39,834 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 13:51:39,835 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110088-黄欣-2025-07-29 21:51:23'
2025-07-29 13:51:39,836 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-29 13:51:39,837 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110088-黄欣-2025-07-29 21:51:23'
2025-07-29 13:51:39,837 - modules.friend_request_window - INFO -    � 内容长度: 35 字符
2025-07-29 13:51:40,338 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110088-黄欣-2025-07-29 21:51:23'
2025-07-29 13:51:40,338 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-29 13:51:40,338 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-29 13:51:40,339 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-29 13:51:40,339 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-29 13:51:40,339 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-29 13:51:40,339 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-29 13:51:41,141 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-29 13:51:41,141 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-29 13:51:41,142 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-29 13:51:41,761 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:51:41,762 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-29 13:51:41,763 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-29 13:51:41,763 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-29 13:51:42,282 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:51:42,518 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:51:42,752 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:51:42,987 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:51:43,224 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:51:43,458 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:51:43,694 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:51:43,930 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:51:44,166 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:51:44,403 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:51:44,641 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:51:44,877 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:51:45,112 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:51:45,346 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:51:45,594 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:51:45,828 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:51:46,064 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:51:46,298 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:51:46,535 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:51:46,771 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:51:46,988 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-29 13:51:46,989 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-29 13:51:47,990 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 13:51:47,994 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-29 13:51:47,994 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-29 13:51:47,995 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-29 13:51:47,995 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-29 13:51:47,997 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 13:51:47,998 - modules.friend_request_window - INFO -    📝 备注信息: '014325110088-黄欣-2025-07-29 21:51:23'
2025-07-29 13:51:48,499 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-29 13:51:48,500 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:51:48,500 - modules.wechat_auto_add_simple - INFO - ✅ 13886107212 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-29 13:51:48,501 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 13886107212
2025-07-29 13:51:48,502 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:51:50,053 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-29 13:51:50,053 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-29 13:51:50,053 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-29 13:51:50,054 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-29 13:51:50,055 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-29 13:51:50,055 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-29 13:51:50,055 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-29 13:51:50,055 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-29 13:51:50,056 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-29 13:51:50,056 - __main__ - INFO - 🔄 第 1 个微信窗口达到联系人处理数量限制
2025-07-29 13:51:50,057 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-29 13:51:50,057 - __main__ - INFO - 🔄 准备激活第 2 个微信窗口
2025-07-29 13:51:50,057 - __main__ - INFO - 🔄 将在新窗口上从第一步（窗口管理）重新开始完整的6步骤流程
2025-07-29 13:51:50,058 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-29 13:51:53,058 - __main__ - INFO - 
============================================================
2025-07-29 13:51:53,059 - __main__ - INFO - 🎯 开始处理第 2/2 个微信窗口
2025-07-29 13:51:53,059 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 525668)
2025-07-29 13:51:53,059 - __main__ - INFO - ============================================================
2025-07-29 13:51:53,060 - __main__ - INFO - 🚀 开始处理微信窗口 2
2025-07-29 13:51:53,060 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 525668)
2025-07-29 13:51:53,060 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-29 13:51:53,061 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 2
2025-07-29 13:51:53,061 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-29 13:51:53,388 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 13:51:53,389 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 13:51:53,389 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 13:51:53,390 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 13:51:53,390 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 13:51:53,390 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 13:51:53,391 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 13:51:53,391 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 13:51:53,391 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 13:51:53,392 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 13:51:53,593 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 13:51:53,593 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 13:51:53,593 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 525668
2025-07-29 13:51:53,594 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-29 13:51:53,897 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 13:51:53,898 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 13:51:53,898 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 13:51:53,899 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 13:51:53,899 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 13:51:53,899 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 13:51:53,900 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 13:51:53,900 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 13:51:53,900 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 13:51:53,900 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 13:51:54,102 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 13:51:54,103 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 13:51:54,104 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 525668 (API返回: None)
2025-07-29 13:51:54,405 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 13:51:54,406 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 13:51:54,406 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 13:51:54,407 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 13:51:54,407 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 13:51:54,407 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 13:51:54,408 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-29 13:51:54,408 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-29 13:51:55,408 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-29 13:51:55,409 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 2
2025-07-29 13:51:55,409 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-29 13:51:55,409 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-29 13:51:55,410 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-29 13:51:55,410 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-29 13:51:55,410 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-29 13:51:55,410 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-29 13:51:55,611 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-29 13:51:55,612 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-29 13:51:57,993 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-29 13:51:57,994 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-29 13:51:57,994 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.6 秒...
2025-07-29 13:52:00,591 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-29 13:52:00,792 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-29 13:52:00,793 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-29 13:52:03,177 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-29 13:52:03,177 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-29 13:52:03,177 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-29 13:52:05,509 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-29 13:52:05,711 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-29 13:52:05,711 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-29 13:52:08,092 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-29 13:52:08,092 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-29 13:52:08,093 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.9 秒...
2025-07-29 13:52:10,985 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-29 13:52:11,186 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-29 13:52:11,187 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-29 13:52:13,558 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-29 13:52:13,559 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-29 13:52:13,559 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.7 秒...
2025-07-29 13:52:15,278 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-29 13:52:15,480 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-29 13:52:15,481 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-29 13:52:17,891 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-29 13:52:17,892 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-29 13:52:17,892 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 13:52:17,893 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 13:52:17,893 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 13:52:17,895 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 13:52:17,896 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 13:52:17,897 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 13437356, 进程: Weixin.exe)
2025-07-29 13:52:17,897 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 13:52:17,898 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 13:52:17,900 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 3277558, 进程: Weixin.exe)
2025-07-29 13:52:17,904 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-29 13:52:17,906 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 3277558)
2025-07-29 13:52:17,909 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 3277558) - 增强版
2025-07-29 13:52:18,212 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 13:52:18,212 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 13:52:18,213 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 13:52:18,214 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 13:52:18,215 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-29 13:52:18,215 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 13:52:18,420 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-29 13:52:18,420 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 13:52:18,621 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 13:52:18,622 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 13:52:18,622 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-29 13:52:18,623 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-29 13:52:18,623 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-29 13:52:18,623 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-29 13:52:18,623 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-29 13:52:19,624 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-29 13:52:19,624 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 13:52:19,626 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 13:52:19,626 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 13:52:19,627 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 13437356, 进程: Weixin.exe)
2025-07-29 13:52:19,628 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 13:52:19,628 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 13:52:19,629 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 3277558, 进程: Weixin.exe)
2025-07-29 13:52:19,632 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-29 13:52:19,634 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-29 13:52:19,636 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-29 13:52:19,636 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-29 13:52:19,636 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 525668
2025-07-29 13:52:19,637 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-29 13:52:19,950 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 13:52:19,950 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 13:52:19,951 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 13:52:19,951 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 13:52:19,951 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 13:52:19,952 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 13:52:19,952 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 13:52:19,953 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 13:52:19,953 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 13:52:19,953 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 13:52:20,155 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 13:52:20,156 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 13:52:20,157 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 525668 (API返回: None)
2025-07-29 13:52:20,458 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 13:52:20,458 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-29 13:52:20,459 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-29 13:52:21,459 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-29 13:52:21,460 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 2
2025-07-29 13:52:21,460 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-29 13:52:21,463 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250729_135221.log
2025-07-29 13:52:21,464 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 13:52:21,464 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-29 13:52:21,464 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-29 13:52:21,465 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-29 13:52:21,466 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-29 13:52:21,468 - modules.wechat_auto_add_simple - INFO - 🎯 找到 4 个微信窗口:
2025-07-29 13:52:21,469 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 2 个
2025-07-29 13:52:21,470 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-29 13:52:21,472 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-29 13:52:21,476 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-29 13:52:21,477 - modules.wechat_auto_add_simple - INFO -   3. 微信 (726x650) - main
2025-07-29 13:52:21,478 - modules.wechat_auto_add_simple - INFO -   4. 添加朋友 (328x454) - add_friend
2025-07-29 13:52:21,479 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-29 13:52:21,480 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 13:52:21,480 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 3277558
2025-07-29 13:52:21,481 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 3277558) - 增强版
2025-07-29 13:52:21,790 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 13:52:21,790 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 13:52:21,791 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 13:52:21,791 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 13:52:21,791 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-29 13:52:21,792 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 13:52:21,792 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-29 13:52:21,792 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 13:52:21,996 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 13:52:21,996 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 13:52:22,000 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 3277558 (API返回: None)
2025-07-29 13:52:22,301 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 13:52:22,301 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-29 13:52:22,302 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-29 13:52:22,302 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-29 13:52:22,303 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 13:52:22,304 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-29 13:52:22,304 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-29 13:52:22,310 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-29 13:52:22,311 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-29 13:52:22,902 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-29 13:52:22,902 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 13:52:23,207 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2958 个
2025-07-29 13:52:23,209 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2958 个 (总计: 3135 个)
2025-07-29 13:52:23,209 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-29 13:52:23,209 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-29 13:52:23,210 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:52:23,210 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-29 13:52:23,211 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2958
2025-07-29 13:52:23,211 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 17543599907 (王雨晨)
2025-07-29 13:52:23,212 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:52:29,818 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 17543599907
2025-07-29 13:52:29,819 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 13:52:29,819 - modules.wechat_auto_add_simple - INFO - 👥 开始为 17543599907 执行添加朋友操作...
2025-07-29 13:52:29,819 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 13:52:29,819 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 13:52:29,820 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 13:52:29,822 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 13:52:29,827 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-29 13:52:29,829 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 13:52:29,829 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 13:52:29,829 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 13:52:29,832 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 13:52:29,833 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 13:52:29,833 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 13:52:29,835 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 13:52:29,839 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 13:52:29,843 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 13:52:29,845 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 13:52:29,850 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 13:52:29,852 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 13:52:29,861 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-07-29 13:52:29,867 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 13:52:30,372 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 13:52:30,520 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 13:52:30,635 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差27.94, 边缘比例0.0350
2025-07-29 13:52:30,648 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_135230.png
2025-07-29 13:52:30,651 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 13:52:30,652 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 13:52:30,653 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 13:52:30,660 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 13:52:30,667 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 13:52:30,672 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_135230.png
2025-07-29 13:52:30,677 - WeChatAutoAdd - INFO - 底部区域原始检测到 38 个轮廓
2025-07-29 13:52:30,680 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 13:52:30,681 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 13:52:30,684 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 13:52:30,685 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-07-29 13:52:30,687 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-07-29 13:52:30,693 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-07-29 13:52:30,695 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-07-29 13:52:30,697 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 13:52:30,700 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-07-29 13:52:30,701 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-07-29 13:52:30,702 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-07-29 13:52:30,709 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 13:52:30,712 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-07-29 13:52:30,713 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 13:52:30,715 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 13:52:30,722 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-29 13:52:30,733 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-29 13:52:30,743 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 13:52:30,746 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 13:52:30,747 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-07-29 13:52:30,752 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-07-29 13:52:30,755 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-07-29 13:52:30,759 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 13:52:30,761 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-07-29 13:52:30,763 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-07-29 13:52:30,765 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-07-29 13:52:30,766 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-07-29 13:52:30,768 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-07-29 13:52:30,769 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-07-29 13:52:30,775 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-07-29 13:52:30,777 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-07-29 13:52:30,779 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-07-29 13:52:30,781 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-07-29 13:52:30,783 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-07-29 13:52:30,784 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-07-29 13:52:30,786 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-07-29 13:52:30,791 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-07-29 13:52:30,794 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 13:52:30,796 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-07-29 13:52:30,797 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-07-29 13:52:30,799 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-29 13:52:30,800 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-29 13:52:30,809 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_135230.png
2025-07-29 13:52:30,811 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-29 13:52:30,813 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-07-29 13:52:30,818 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250729_135230.png
2025-07-29 13:52:30,850 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-29 13:52:30,852 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-07-29 13:52:30,853 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-29 13:52:30,855 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 13:52:31,160 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-07-29 13:52:31,956 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 13:52:31,957 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 13:52:31,959 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:52:31,959 - modules.wechat_auto_add_simple - INFO - ✅ 17543599907 添加朋友操作执行成功
2025-07-29 13:52:31,959 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:52:31,959 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 13:52:33,961 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 13:52:33,961 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 13:52:33,962 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 13:52:33,962 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 13:52:33,962 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 13:52:33,962 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 13:52:33,963 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 13:52:33,963 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 13:52:33,963 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 13:52:33,964 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 17543599907
2025-07-29 13:52:33,964 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 13:52:33,964 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 13:52:33,965 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 13:52:33,965 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 13:52:33,965 - modules.friend_request_window - INFO -    📱 phone: '17543599907'
2025-07-29 13:52:33,966 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 13:52:33,966 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 13:52:34,519 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-29 13:52:34,519 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 13:52:34,520 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 13:52:34,520 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 13:52:34,522 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 17543599907
2025-07-29 13:52:34,522 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 13:52:34,522 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 13:52:34,523 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 13:52:34,523 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 13:52:34,523 - modules.friend_request_window - INFO -    📱 手机号码: 17543599907
2025-07-29 13:52:34,524 - modules.friend_request_window - INFO -    🆔 准考证: 014325110089
2025-07-29 13:52:34,524 - modules.friend_request_window - INFO -    👤 姓名: 王雨晨
2025-07-29 13:52:34,524 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 13:52:34,524 - modules.friend_request_window - INFO -    📝 备注格式: '014325110089-王雨晨-2025-07-29 21:52:34'
2025-07-29 13:52:34,525 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 13:52:34,526 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110089-王雨晨-2025-07-29 21:52:34'
2025-07-29 13:52:34,527 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 13:52:34,530 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-29 13:52:34,531 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-29 13:52:34,533 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:52:34,533 - modules.wechat_auto_add_simple - INFO - ✅ 17543599907 申请添加朋友窗口处理完成: 申请添加朋友窗口处理失败: 未找到好友申请窗口
2025-07-29 13:52:34,533 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 17543599907
2025-07-29 13:52:34,534 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:52:38,641 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2958
2025-07-29 13:52:38,643 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18151680519 (邓僚僚)
2025-07-29 13:52:38,644 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:52:45,238 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18151680519
2025-07-29 13:52:45,239 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 13:52:45,240 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18151680519 执行添加朋友操作...
2025-07-29 13:52:45,243 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 13:52:45,244 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 13:52:45,246 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 13:52:45,260 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 13:52:45,267 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-29 13:52:45,270 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 13:52:45,271 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 13:52:45,273 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 13:52:45,274 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 13:52:45,276 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 13:52:45,276 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 13:52:45,277 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 13:52:45,295 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 13:52:45,299 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 13:52:45,309 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 13:52:45,315 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 13:52:45,328 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 13:52:45,340 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-07-29 13:52:45,344 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 13:52:45,851 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 13:52:45,895 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 13:52:46,026 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-07-29 13:52:46,082 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-07-29 13:52:46,144 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_135246.png
2025-07-29 13:52:46,150 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 13:52:46,157 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 13:52:46,163 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 13:52:46,168 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 13:52:46,180 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 13:52:46,190 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_135246.png
2025-07-29 13:52:46,198 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-29 13:52:46,202 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-29 13:52:46,217 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-29 13:52:46,229 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 13:52:46,235 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-29 13:52:46,243 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-29 13:52:46,259 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-29 13:52:46,269 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-29 13:52:46,303 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_135246.png
2025-07-29 13:52:46,315 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-29 13:52:46,328 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-29 13:52:46,339 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250729_135246.png
2025-07-29 13:52:46,381 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-29 13:52:46,394 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-29 13:52:46,401 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-29 13:52:46,425 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 13:52:46,745 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-29 13:52:47,523 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 13:52:47,524 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 13:52:47,526 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:52:47,526 - modules.wechat_auto_add_simple - INFO - ✅ 18151680519 添加朋友操作执行成功
2025-07-29 13:52:47,527 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:52:47,527 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 13:52:49,529 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 13:52:49,529 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 13:52:49,529 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 13:52:49,530 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 13:52:49,530 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 13:52:49,530 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 13:52:49,531 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 13:52:49,531 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 13:52:49,531 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 13:52:49,531 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18151680519
2025-07-29 13:52:49,532 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 13:52:49,532 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 13:52:49,533 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 13:52:49,533 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 13:52:49,533 - modules.friend_request_window - INFO -    📱 phone: '18151680519'
2025-07-29 13:52:49,533 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 13:52:49,534 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 13:52:50,074 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-29 13:52:50,074 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 13:52:50,075 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 13:52:50,075 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 13:52:50,076 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18151680519
2025-07-29 13:52:50,076 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 13:52:50,077 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 13:52:50,077 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 13:52:50,077 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 13:52:50,078 - modules.friend_request_window - INFO -    📱 手机号码: 18151680519
2025-07-29 13:52:50,078 - modules.friend_request_window - INFO -    🆔 准考证: 014325110090
2025-07-29 13:52:50,078 - modules.friend_request_window - INFO -    👤 姓名: 邓僚僚
2025-07-29 13:52:50,078 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 13:52:50,079 - modules.friend_request_window - INFO -    📝 备注格式: '014325110090-邓僚僚-2025-07-29 21:52:50'
2025-07-29 13:52:50,079 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 13:52:50,080 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110090-邓僚僚-2025-07-29 21:52:50'
2025-07-29 13:52:50,080 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 13:52:50,081 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 4197620, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-29 13:52:50,083 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 4197620)
2025-07-29 13:52:50,083 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-29 13:52:50,083 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-29 13:52:50,083 - modules.friend_request_window - INFO - 🔄 激活窗口: 4197620
2025-07-29 13:52:50,786 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-29 13:52:50,786 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-29 13:52:50,787 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-29 13:52:50,787 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-29 13:52:50,787 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-29 13:52:50,788 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 13:52:50,788 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-29 13:52:50,788 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 13:52:50,788 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-29 13:52:50,789 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-29 13:52:50,789 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-29 13:52:50,789 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-29 13:52:50,790 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-29 13:52:50,790 - modules.friend_request_window - INFO -    📝 remark参数: '014325110090-邓僚僚-2025-07-29 21:52:50' (类型: <class 'str'>, 长度: 36)
2025-07-29 13:52:50,790 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-29 13:52:50,791 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110090-邓僚僚-2025-07-29 21:52:50'
2025-07-29 13:52:50,791 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-29 13:52:50,791 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-29 13:52:50,792 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-29 13:52:50,792 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-29 13:52:50,793 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-29 13:52:50,793 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-29 13:52:50,796 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-29 13:52:51,742 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-29 13:52:56,998 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-29 13:52:56,998 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-29 13:52:56,999 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-29 13:52:56,999 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-29 13:52:56,999 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '学习指导【视频作业】...' (前50字符)
2025-07-29 13:52:57,308 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 13:52:57,308 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 13:52:58,224 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 13:52:58,236 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 13:52:58,239 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-29 13:52:58,240 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-29 13:52:58,241 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-29 13:52:58,241 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-29 13:52:58,742 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-29 13:52:58,742 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-29 13:52:58,743 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-29 13:52:58,743 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-29 13:52:58,744 - modules.friend_request_window - INFO -    📝 内容: '014325110090-邓僚僚-2025-07-29 21:52:50'
2025-07-29 13:52:58,745 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-29 13:52:58,745 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110090-\xe9\x82\x93\xe5\x83\x9a\xe5\x83\x9a-2025-07-29 21:52:50'
2025-07-29 13:52:58,745 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-29 13:52:59,674 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-29 13:53:04,930 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-29 13:53:04,931 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-29 13:53:04,931 - modules.friend_request_window - INFO -    📝 原始文本: '014325110090-邓僚僚-2025-07-29 21:52:50'
2025-07-29 13:53:04,931 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-29 13:53:04,932 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '学习指导【视频作业】...' (前50字符)
2025-07-29 13:53:05,241 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 13:53:05,242 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 13:53:06,145 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 13:53:06,154 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 13:53:06,155 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110090-邓僚僚-2025-07-29 21:52:50'
2025-07-29 13:53:06,156 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-29 13:53:06,157 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110090-邓僚僚-2025-07-29 21:52:50'
2025-07-29 13:53:06,158 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-29 13:53:06,661 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110090-邓僚僚-2025-07-29 21:52:50'
2025-07-29 13:53:06,661 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-29 13:53:06,662 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-29 13:53:06,662 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-29 13:53:06,662 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-29 13:53:06,663 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-29 13:53:06,663 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-29 13:53:07,464 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-29 13:53:07,464 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-29 13:53:07,464 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-29 13:53:08,073 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:53:08,073 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-29 13:53:08,074 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-29 13:53:08,074 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-29 13:53:08,593 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:08,593 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:08,830 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:08,830 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:09,069 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:09,070 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:09,312 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:09,312 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:09,547 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:09,547 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:09,792 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:09,793 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:10,027 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:10,027 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:10,263 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:10,264 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:10,499 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:10,499 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:10,736 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:10,737 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:10,979 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:10,980 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:11,215 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:11,216 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:11,452 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:11,453 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:11,692 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:11,693 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:11,931 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:11,933 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:12,170 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:12,171 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:12,436 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:12,437 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:12,677 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:12,678 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:12,917 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:12,917 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:53:13,147 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-29 13:53:13,148 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-29 13:53:14,148 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 13:53:14,151 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-29 13:53:14,152 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-29 13:53:14,152 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-29 13:53:14,152 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-29 13:53:14,153 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 13:53:14,153 - modules.friend_request_window - INFO -    📝 备注信息: '014325110090-邓僚僚-2025-07-29 21:52:50'
2025-07-29 13:53:14,654 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-29 13:53:14,655 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:53:14,655 - modules.wechat_auto_add_simple - INFO - ✅ 18151680519 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-29 13:53:14,656 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 18151680519
2025-07-29 13:53:14,657 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:53:16,265 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-29 13:53:16,265 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-29 13:53:16,265 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-29 13:53:16,267 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-29 13:53:16,267 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-29 13:53:16,267 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-29 13:53:16,268 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-29 13:53:16,269 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-29 13:53:16,269 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-29 13:53:16,269 - __main__ - INFO - 🔄 第 2 个微信窗口达到联系人处理数量限制
2025-07-29 13:53:16,269 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-29 13:53:16,270 - __main__ - INFO - ✅ 已是最后一个窗口，所有微信窗口处理完成
2025-07-29 13:53:16,271 - __main__ - INFO - 
============================================================
2025-07-29 13:53:16,272 - __main__ - INFO - 📊 多窗口处理结果统计:
2025-07-29 13:53:16,274 - __main__ - INFO -   ✅ 成功处理窗口: 2/2
2025-07-29 13:53:16,275 - __main__ - INFO -   ❌ 失败处理窗口: 0/2
2025-07-29 13:53:16,276 - __main__ - INFO -   📈 成功率: 100.0%
2025-07-29 13:53:16,278 - __main__ - INFO - ============================================================
