2025-07-28 16:04:44,706 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 16:04:44,708 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 16:04:44,709 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-28 16:04:44,711 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 16:04:44,714 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-28 16:04:44,714 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 16:04:44,715 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 16:04:44,716 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 16:04:44,716 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 16:04:44,717 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 16:04:44,718 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 16:04:44,721 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 16:04:44,724 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250728_160444.log
2025-07-28 16:04:44,726 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-28 16:04:44,727 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-28 16:04:44,728 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-28 16:04:44,731 - main_controller - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-28 16:04:44,733 - main_controller - INFO - 📅 当前北京时间: 2025-07-29 00:04:44
2025-07-28 16:04:44,734 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-28 16:04:44,735 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 16:04:44,735 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 16:04:44,736 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 16:04:44,738 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 16:04:44,739 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
