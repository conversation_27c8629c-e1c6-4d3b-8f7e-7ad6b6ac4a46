2025-07-28 15:57:38,399 - __main__ - INFO - 🚀 开始测试微信窗口切换修复
2025-07-28 15:57:38,403 - __main__ - INFO - ============================================================
2025-07-28 15:57:38,403 - __main__ - INFO - 📋 步骤1: 创建主控制器实例...
2025-07-28 15:57:40,949 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 15:57:40,952 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 15:57:40,953 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-28 15:57:40,954 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 15:57:40,957 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-28 15:57:40,957 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 15:57:40,958 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 15:57:40,959 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 15:57:40,961 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 15:57:40,963 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 15:57:40,964 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:57:40,976 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 15:57:40,981 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250728_155740.log
2025-07-28 15:57:40,981 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-28 15:57:40,982 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-28 15:57:40,988 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-28 15:57:40,993 - main_controller - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-28 15:57:40,998 - main_controller - INFO - 📅 当前北京时间: 2025-07-28 15:57:40
2025-07-28 15:57:40,999 - __main__ - INFO - 📋 步骤2: 模拟微信窗口数据...
2025-07-28 15:57:41,005 - __main__ - INFO -    - 模拟窗口数量: 2
2025-07-28 15:57:41,009 - __main__ - INFO -    - 模拟联系人数量: 2
2025-07-28 15:57:41,010 - __main__ - INFO - 📋 步骤3: 模拟步骤3返回频率错误...
2025-07-28 15:57:41,023 - __main__ - INFO - 📋 步骤4: 模拟其他步骤...
2025-07-28 15:57:41,026 - __main__ - INFO - 📋 步骤5: 测试单个窗口流程...
2025-07-28 15:57:41,029 - main_controller - INFO - 🚀 开始处理微信窗口 1
2025-07-28 15:57:41,032 - main_controller - INFO - 📋 窗口信息: 微信 (句柄: 12345)
2025-07-28 15:57:41,036 - main_controller - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-28 15:57:41,040 - main_controller - INFO - ✅ 步骤 1 执行成功
2025-07-28 15:57:42,041 - main_controller - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-28 15:57:42,051 - main_controller - INFO - ✅ 步骤 2 执行成功
2025-07-28 15:57:43,053 - main_controller - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-28 15:57:43,054 - __main__ - INFO - 🔧 模拟执行步骤3 - 窗口: 12345
2025-07-28 15:57:43,056 - __main__ - INFO - ⚠️ 模拟检测到频率错误
2025-07-28 15:57:43,056 - main_controller - WARNING - 🔄 步骤 3 检测到频率错误，需要立即切换窗口
2025-07-28 15:57:43,057 - main_controller - INFO - � 频率错误处理完成，当前微信窗口已关闭
2025-07-28 15:57:43,058 - main_controller - INFO - �🔄 立即终止当前窗口的所有后续步骤
2025-07-28 15:57:43,058 - main_controller - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-28 15:57:43,061 - __main__ - INFO - ✅ 第一个窗口正确返回RESTART_REQUIRED
2025-07-28 15:57:43,061 - __main__ - ERROR - ❌ 测试执行异常: 'WeChatMainController' object has no attribute 'WindowStatus'
2025-07-28 15:57:43,064 - __main__ - ERROR - Traceback (most recent call last):
  File "D:\程序-测试\微信7.28 - 副本 (2)\test_window_switch_fix.py", line 134, in test_frequency_error_window_switch
    if controller.window_statuses.get(0) == controller.WindowStatus.ERROR:
                                            ^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'WeChatMainController' object has no attribute 'WindowStatus'. Did you mean: 'window_statuses'?

