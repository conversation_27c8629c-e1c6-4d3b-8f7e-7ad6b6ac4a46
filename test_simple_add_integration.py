#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 main_controller.py 调用 wechat_auto_add_simple.py 的集成效果

作者：AI助手
创建时间：2025-01-28
"""

import sys
import logging
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

def test_simple_add_integration():
    """测试简单添加好友集成"""
    logger = setup_logging()
    logger.info("🧪 开始测试 main_controller.py 调用 wechat_auto_add_simple.py 集成...")
    
    try:
        # 1. 测试导入
        logger.info("📦 测试模块导入...")
        from main_controller import WeChatMainController
        from modules.wechat_auto_add_simple import WeChatAutoAddFriend
        logger.info("✅ 所有模块导入成功")
        
        # 2. 测试 wechat_auto_add_simple 独立功能
        logger.info("🔧 测试 wechat_auto_add_simple 独立功能...")
        simple_add = WeChatAutoAddFriend()
        
        # 检查关键方法是否存在
        if hasattr(simple_add, 'run_automation'):
            logger.info("✅ run_automation 方法存在")
        else:
            logger.error("❌ run_automation 方法不存在")
            return False
            
        if hasattr(simple_add, 'move_window_to_position'):
            logger.info("✅ move_window_to_position 方法存在")
        else:
            logger.error("❌ move_window_to_position 方法不存在")
            return False
            
        if hasattr(simple_add, 'activate_window'):
            logger.info("✅ activate_window 方法存在")
        else:
            logger.error("❌ activate_window 方法不存在")
            return False
        
        # 3. 测试主控制器初始化
        logger.info("🎮 测试主控制器初始化...")
        controller = WeChatMainController()
        logger.info("✅ 主控制器初始化成功")
        
        # 4. 测试步骤3方法是否正确修改
        logger.info("🔍 检查步骤3方法修改...")
        if hasattr(controller, 'execute_step_3_simple_add'):
            logger.info("✅ execute_step_3_simple_add 方法存在")
            
            # 检查方法源码是否包含对 wechat_auto_add_simple 的调用
            import inspect
            source = inspect.getsource(controller.execute_step_3_simple_add)
            
            if 'wechat_auto_add_simple' in source:
                logger.info("✅ 步骤3方法包含对 wechat_auto_add_simple 的调用")
            else:
                logger.error("❌ 步骤3方法未包含对 wechat_auto_add_simple 的调用")
                return False
                
            if 'run_automation' in source:
                logger.info("✅ 步骤3方法包含 run_automation 调用")
            else:
                logger.error("❌ 步骤3方法未包含 run_automation 调用")
                return False
                
        else:
            logger.error("❌ execute_step_3_simple_add 方法不存在")
            return False
        
        # 5. 模拟测试步骤3执行（不实际运行）
        logger.info("🧪 模拟测试步骤3执行...")
        
        # 创建模拟窗口数据
        mock_window = {
            "hwnd": 12345,
            "title": "微信",
            "type": "main"
        }
        
        # 创建模拟联系人数据
        mock_contacts = [
            {"phone": "13800138000", "name": "测试联系人1"},
            {"phone": "13800138001", "name": "测试联系人2"}
        ]
        
        logger.info("📋 模拟数据准备完成")
        logger.info(f"  - 窗口: {mock_window['title']} (句柄: {mock_window['hwnd']})")
        logger.info(f"  - 联系人数量: {len(mock_contacts)}")
        
        # 注意：这里不实际调用 execute_step_3_simple_add，因为需要真实的微信窗口
        logger.info("⚠️ 跳过实际执行（需要真实微信窗口）")
        
        logger.info("🎉 集成测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生异常: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """主函数"""
    logger = setup_logging()
    logger.info("=" * 60)
    logger.info("main_controller.py 调用 wechat_auto_add_simple.py 集成测试")
    logger.info("=" * 60)
    
    success = test_simple_add_integration()
    
    if success:
        logger.info("✅ 测试通过：集成修改成功")
        logger.info("📋 修改说明：")
        logger.info("  - main_controller.py 的步骤3现在直接调用 wechat_auto_add_simple.py")
        logger.info("  - 包含完整的窗口移动功能 (move_window_to_position)")
        logger.info("  - 包含窗口激活功能 (activate_window)")
        logger.info("  - 执行完整的自动化流程 (run_automation)")
    else:
        logger.error("❌ 测试失败：集成修改有问题")
    
    return success

if __name__ == "__main__":
    main()
