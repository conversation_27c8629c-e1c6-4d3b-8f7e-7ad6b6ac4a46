2025-07-28 23:28:11,194 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 23:28:11,195 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 23:28:11,196 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-28 23:28:11,196 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 23:28:11,198 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-28 23:28:11,198 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 23:28:11,198 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 23:28:11,200 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 23:28:11,200 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 23:28:11,201 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 23:28:11,204 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:28:11,213 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 23:28:11,254 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250728_232811.log
2025-07-28 23:28:11,266 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-28 23:28:11,272 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-28 23:28:11,276 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-28 23:28:11,282 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-28 23:28:11,292 - __main__ - INFO - 📅 当前北京时间: 2025-07-29 07:28:11
2025-07-28 23:28:11,294 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-28 23:28:11,295 - __main__ - INFO - 📅 启动时间: 2025-07-29 07:28:11
2025-07-28 23:28:11,296 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-28 23:28:11,299 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 23:28:11,302 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 23:28:11,315 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 23:28:11,342 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 23:28:11,572 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 23:28:11,744 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-28 23:28:11,783 - __main__ - INFO - ✅ 找到 2 个微信窗口
2025-07-28 23:28:11,796 - __main__ - INFO -   窗口 1: 微信 (句柄: 197994)
2025-07-28 23:28:11,818 - __main__ - INFO -   窗口 2: 微信 (句柄: 525668)
2025-07-28 23:28:11,821 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-28 23:28:13,207 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-28 23:28:13,207 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-28 23:28:13,208 - __main__ - INFO - 📋 待处理联系人数: 2971
2025-07-28 23:28:13,208 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-28 23:28:13,209 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2971
2025-07-28 23:28:13,209 - __main__ - INFO - 
============================================================
2025-07-28 23:28:13,209 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口
2025-07-28 23:28:13,210 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-28 23:28:13,210 - __main__ - INFO - ============================================================
2025-07-28 23:28:13,210 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-28 23:28:13,211 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-28 23:28:13,211 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-28 23:28:13,212 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-28 23:28:13,212 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 23:28:13,515 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 23:28:13,515 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 23:28:13,516 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 23:28:13,517 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 23:28:13,517 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 23:28:13,517 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 23:28:13,521 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 23:28:13,521 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 23:28:13,523 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 23:28:13,524 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 23:28:13,726 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 23:28:13,726 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 23:28:13,726 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-28 23:28:13,727 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 23:28:14,029 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 23:28:14,030 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 23:28:14,030 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 23:28:14,031 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 23:28:14,031 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 23:28:14,031 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 23:28:14,032 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 23:28:14,032 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 23:28:14,032 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 23:28:14,032 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 23:28:14,234 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 23:28:14,236 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 23:28:14,237 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-28 23:28:14,538 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 23:28:14,539 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 23:28:14,539 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 23:28:14,540 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 23:28:14,541 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 23:28:14,541 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 23:28:14,541 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-28 23:28:14,542 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-28 23:28:15,542 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-28 23:28:15,543 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-28 23:28:15,543 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-28 23:28:15,544 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-28 23:28:15,544 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-28 23:28:15,544 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-28 23:28:15,545 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-28 23:28:15,545 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-28 23:28:15,746 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-28 23:28:15,747 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-28 23:28:18,121 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-28 23:28:18,121 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-28 23:28:18,122 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 3.0 秒...
2025-07-28 23:28:21,081 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-28 23:28:21,282 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-28 23:28:21,282 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-28 23:28:23,655 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-28 23:28:23,656 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-28 23:28:23,656 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-28 23:28:25,982 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-28 23:28:26,188 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-28 23:28:26,210 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-28 23:28:28,605 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-28 23:28:28,606 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-28 23:28:28,607 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.5 秒...
2025-07-28 23:28:30,155 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-28 23:28:30,358 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-28 23:28:30,360 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-28 23:28:32,799 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-28 23:28:32,832 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-28 23:28:32,862 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-28 23:28:35,223 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-28 23:28:35,425 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-28 23:28:35,426 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-28 23:28:37,804 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-28 23:28:37,804 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-28 23:28:37,805 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-28 23:28:37,805 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-28 23:28:37,805 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 23:28:37,807 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 23:28:37,807 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 23:28:37,808 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 23:28:37,808 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 23:28:37,809 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 3345636, 进程: Weixin.exe)
2025-07-28 23:28:37,810 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-28 23:28:37,811 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 3345636)
2025-07-28 23:28:37,811 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 3345636) - 增强版
2025-07-28 23:28:38,115 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 23:28:38,115 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 23:28:38,115 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-28 23:28:38,116 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-28 23:28:38,118 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-28 23:28:38,118 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-28 23:28:38,323 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-28 23:28:38,323 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-28 23:28:38,525 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 23:28:38,525 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 23:28:38,525 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-28 23:28:38,526 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-28 23:28:38,526 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-28 23:28:38,526 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-28 23:28:38,526 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-28 23:28:39,527 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-28 23:28:39,527 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 23:28:39,528 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 23:28:39,529 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 23:28:39,530 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 23:28:39,530 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 23:28:39,531 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 3345636, 进程: Weixin.exe)
2025-07-28 23:28:39,535 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-28 23:28:39,537 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-28 23:28:39,537 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-28 23:28:39,537 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-28 23:28:39,538 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-28 23:28:39,538 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 23:28:39,846 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 23:28:39,846 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 23:28:39,847 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 23:28:39,847 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 23:28:39,847 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 23:28:39,847 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 23:28:39,848 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 23:28:39,848 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 23:28:39,848 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 23:28:39,849 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 23:28:40,051 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 23:28:40,053 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 23:28:40,054 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-28 23:28:40,355 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 23:28:40,355 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-28 23:28:40,355 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-28 23:28:41,356 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-28 23:28:41,356 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-28 23:28:41,357 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-28 23:28:41,359 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250728_232841.log
2025-07-28 23:28:41,359 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-28 23:28:41,359 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-28 23:28:41,360 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-28 23:28:41,360 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-28 23:28:41,362 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-28 23:28:41,362 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-28 23:28:41,362 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-28 23:28:41,363 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-28 23:28:41,364 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-28 23:28:41,364 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-28 23:28:41,364 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-28 23:28:41,365 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 23:28:41,366 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 3345636
2025-07-28 23:28:41,370 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 3345636) - 增强版
2025-07-28 23:28:41,678 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 23:28:41,678 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 23:28:41,678 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-28 23:28:41,679 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-28 23:28:41,679 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-28 23:28:41,679 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-28 23:28:41,680 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-28 23:28:41,680 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-28 23:28:41,882 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 23:28:41,888 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 23:28:41,891 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 3345636 (API返回: None)
2025-07-28 23:28:42,192 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 23:28:42,193 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-28 23:28:42,193 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-28 23:28:42,193 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-28 23:28:42,195 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 23:28:42,195 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-28 23:28:42,195 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-28 23:28:42,199 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-28 23:28:42,203 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-28 23:28:42,644 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-28 23:28:42,645 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 23:28:42,977 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2971 个
2025-07-28 23:28:42,978 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2971 个 (总计: 3135 个)
2025-07-28 23:28:42,978 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 10 个联系人后切换
2025-07-28 23:28:42,979 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:28:42,979 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-28 23:28:42,979 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2971
2025-07-28 23:28:42,980 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13092760008 (石刚)
2025-07-28 23:28:42,980 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:28:49,614 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13092760008
2025-07-28 23:28:49,615 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 23:28:49,615 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13092760008 执行添加朋友操作...
2025-07-28 23:28:49,617 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 23:28:49,618 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 23:28:49,619 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 23:28:49,619 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 23:28:49,623 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 23:28:49,624 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 23:28:49,625 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 23:28:49,626 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 23:28:49,626 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 23:28:49,627 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 23:28:49,627 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 23:28:49,628 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 23:28:49,634 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-28 23:28:49,637 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 23:28:49,638 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 23:28:49,640 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 23:28:49,642 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-28 23:28:49,668 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-28 23:28:50,193 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-28 23:28:50,194 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-28 23:28:50,262 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-07-28 23:28:50,263 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-07-28 23:28:50,272 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_232850.png
2025-07-28 23:28:50,274 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 23:28:50,275 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 23:28:50,276 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-28 23:28:50,277 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-28 23:28:50,279 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-28 23:28:50,284 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_232850.png
2025-07-28 23:28:50,286 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-28 23:28:50,287 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-28 23:28:50,293 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-28 23:28:50,294 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-28 23:28:50,295 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-28 23:28:50,296 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-28 23:28:50,297 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-28 23:28:50,298 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 23:28:50,309 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_232850.png
2025-07-28 23:28:50,310 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 23:28:50,311 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-28 23:28:50,315 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_232850.png
2025-07-28 23:28:50,367 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 23:28:50,369 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-28 23:28:50,370 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 23:28:50,371 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 23:28:50,673 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-28 23:28:51,453 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 23:28:51,454 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 23:28:51,456 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:28:51,457 - modules.wechat_auto_add_simple - INFO - ✅ 13092760008 添加朋友操作执行成功
2025-07-28 23:28:51,458 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:28:51,460 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 23:28:53,461 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-28 23:28:53,462 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-28 23:28:53,462 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-28 23:28:53,463 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 23:28:53,463 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 23:28:53,464 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 23:28:53,464 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 23:28:53,464 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 23:28:53,465 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-28 23:28:53,467 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13092760008
2025-07-28 23:28:53,475 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-28 23:28:53,475 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 23:28:53,476 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:695 in _handle_friend_request_window
2025-07-28 23:28:53,476 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-28 23:28:53,477 - modules.friend_request_window - INFO -    📱 phone: '13092760008'
2025-07-28 23:28:53,478 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-28 23:28:53,478 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-28 23:28:53,914 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-28 23:28:53,915 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-28 23:28:53,917 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-28 23:28:53,918 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 23:28:53,919 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13092760008
2025-07-28 23:28:53,920 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-28 23:28:53,920 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 23:28:53,922 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-28 23:28:53,922 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-28 23:28:53,923 - modules.friend_request_window - INFO -    📱 手机号码: 13092760008
2025-07-28 23:28:53,924 - modules.friend_request_window - INFO -    🆔 准考证: 014325110076
2025-07-28 23:28:53,924 - modules.friend_request_window - INFO -    👤 姓名: 石刚
2025-07-28 23:28:53,925 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 23:28:53,925 - modules.friend_request_window - INFO -    📝 备注格式: '014325110076-石刚-2025-07-29 07:28:53'
2025-07-28 23:28:53,925 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 23:28:53,925 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110076-石刚-2025-07-29 07:28:53'
2025-07-28 23:28:53,926 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 23:28:53,927 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 4917948, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-28 23:28:53,928 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 4917948)
2025-07-28 23:28:53,928 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-28 23:28:53,929 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-28 23:28:53,930 - modules.friend_request_window - INFO - 🔄 激活窗口: 4917948
2025-07-28 23:28:54,634 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-28 23:28:54,635 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-28 23:28:54,636 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-28 23:28:54,636 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:499 in _analyze_search_result
2025-07-28 23:28:54,637 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-28 23:28:54,637 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 23:28:54,638 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-28 23:28:54,638 - modules.friend_request_window - INFO -    3. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:695 in _handle_friend_request_window
2025-07-28 23:28:54,638 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-28 23:28:54,639 - modules.friend_request_window - INFO -    4. D:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-28 23:28:54,639 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-28 23:28:54,639 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-28 23:28:54,640 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-28 23:28:54,641 - modules.friend_request_window - INFO -    📝 remark参数: '014325110076-石刚-2025-07-29 07:28:53' (类型: <class 'str'>, 长度: 35)
2025-07-28 23:28:54,642 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-28 23:28:54,642 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110076-石刚-2025-07-29 07:28:53'
2025-07-28 23:28:54,643 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-28 23:28:54,643 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-28 23:28:54,643 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-28 23:28:54,644 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-28 23:28:54,644 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-28 23:28:54,645 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-28 23:28:54,646 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-28 23:28:55,553 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-28 23:29:00,847 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-28 23:29:00,850 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-28 23:29:00,851 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-28 23:29:00,852 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-28 23:29:00,854 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'PS D:\程序-测试\微信7.28 - 副本 (2)> python main_controlle...' (前50字符)
2025-07-28 23:29:01,170 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 23:29:01,170 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 23:29:02,074 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 23:29:02,087 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 23:29:02,088 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-28 23:29:02,088 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-28 23:29:02,089 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-28 23:29:02,089 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-28 23:29:02,590 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-28 23:29:02,591 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-28 23:29:02,591 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-28 23:29:02,591 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-28 23:29:02,591 - modules.friend_request_window - INFO -    📝 内容: '014325110076-石刚-2025-07-29 07:28:53'
2025-07-28 23:29:02,592 - modules.friend_request_window - INFO -    📏 内容长度: 35 字符
2025-07-28 23:29:02,593 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110076-\xe7\x9f\xb3\xe5\x88\x9a-2025-07-29 07:28:53'
2025-07-28 23:29:02,593 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-28 23:29:03,502 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-28 23:29:08,770 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-28 23:29:08,770 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-28 23:29:08,771 - modules.friend_request_window - INFO -    📝 原始文本: '014325110076-石刚-2025-07-29 07:28:53'
2025-07-28 23:29:08,771 - modules.friend_request_window - INFO -    📏 文本长度: 35 字符
2025-07-28 23:29:08,772 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'PS D:\程序-测试\微信7.28 - 副本 (2)> python main_controlle...' (前50字符)
2025-07-28 23:29:09,081 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 23:29:09,083 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 23:29:09,986 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 23:29:09,997 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 23:29:10,002 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110076-石刚-2025-07-29 07:28:53'
2025-07-28 23:29:10,002 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-28 23:29:10,003 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110076-石刚-2025-07-29 07:28:53'
2025-07-28 23:29:10,003 - modules.friend_request_window - INFO -    � 内容长度: 35 字符
2025-07-28 23:29:10,504 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110076-石刚-2025-07-29 07:28:53'
2025-07-28 23:29:10,504 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-28 23:29:10,504 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-28 23:29:10,504 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-28 23:29:10,505 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-28 23:29:10,505 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-28 23:29:10,505 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-28 23:29:11,306 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-28 23:29:11,306 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-28 23:29:11,306 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-28 23:29:11,957 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:29:11,957 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-28 23:29:11,958 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-28 23:29:11,958 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-28 23:29:12,478 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:12,710 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:12,945 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:13,190 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:13,427 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:13,663 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:13,899 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:14,136 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:14,369 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:14,611 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:14,848 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:15,087 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:15,323 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:15,558 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:15,793 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:16,036 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:16,271 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:16,508 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:16,763 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:17,012 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-28 23:29:17,012 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-28 23:29:18,013 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 23:29:18,018 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-28 23:29:18,019 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-28 23:29:18,019 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-28 23:29:18,019 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-28 23:29:18,020 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 23:29:18,020 - modules.friend_request_window - INFO -    📝 备注信息: '014325110076-石刚-2025-07-29 07:28:53'
2025-07-28 23:29:18,521 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-28 23:29:18,522 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:29:18,522 - modules.wechat_auto_add_simple - INFO - ✅ 13092760008 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-28 23:29:18,522 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 13092760008
2025-07-28 23:29:18,523 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:29:22,044 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2971
2025-07-28 23:29:22,045 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15167321838 (金鸣)
2025-07-28 23:29:22,046 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:29:28,727 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15167321838
2025-07-28 23:29:28,728 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 23:29:28,728 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15167321838 执行添加朋友操作...
2025-07-28 23:29:28,729 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 23:29:28,730 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 23:29:28,731 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 23:29:28,734 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 23:29:28,742 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 23:29:28,744 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 23:29:28,745 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 23:29:28,745 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 23:29:28,748 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 23:29:28,750 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 23:29:28,751 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 23:29:28,751 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 23:29:28,762 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-28 23:29:28,769 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 23:29:28,770 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 23:29:28,773 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 23:29:28,775 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-28 23:29:28,776 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-28 23:29:29,285 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-28 23:29:29,287 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-28 23:29:29,353 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差39.31, 边缘比例0.0411
2025-07-28 23:29:29,361 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_232929.png
2025-07-28 23:29:29,362 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 23:29:29,367 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 23:29:29,369 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-28 23:29:29,371 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-28 23:29:29,371 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-28 23:29:29,376 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_232929.png
2025-07-28 23:29:29,377 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-28 23:29:29,377 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-28 23:29:29,378 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-28 23:29:29,380 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-28 23:29:29,387 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-28 23:29:29,388 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-28 23:29:29,389 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-28 23:29:29,391 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 23:29:29,400 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_232929.png
2025-07-28 23:29:29,403 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 23:29:29,406 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-28 23:29:29,412 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_232929.png
2025-07-28 23:29:29,442 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 23:29:29,445 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-28 23:29:29,446 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 23:29:29,454 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 23:29:29,755 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-28 23:29:30,551 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 23:29:30,553 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 23:29:30,556 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:29:30,559 - modules.wechat_auto_add_simple - INFO - ✅ 15167321838 添加朋友操作执行成功
2025-07-28 23:29:30,560 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:29:30,560 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 23:29:32,562 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-28 23:29:32,565 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-28 23:29:32,565 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-28 23:29:32,565 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 23:29:32,566 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 23:29:32,566 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 23:29:32,566 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 23:29:32,566 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 23:29:32,567 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-28 23:29:32,567 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 15167321838
2025-07-28 23:29:32,567 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-28 23:29:32,568 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 23:29:32,572 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:695 in _handle_friend_request_window
2025-07-28 23:29:32,573 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-28 23:29:32,574 - modules.friend_request_window - INFO -    📱 phone: '15167321838'
2025-07-28 23:29:32,574 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-28 23:29:32,575 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-28 23:29:33,153 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-28 23:29:33,153 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-28 23:29:33,153 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-28 23:29:33,154 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 23:29:33,155 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 15167321838
2025-07-28 23:29:33,155 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-28 23:29:33,156 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 23:29:33,156 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-28 23:29:33,156 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-28 23:29:33,157 - modules.friend_request_window - INFO -    📱 手机号码: 15167321838
2025-07-28 23:29:33,157 - modules.friend_request_window - INFO -    🆔 准考证: 014325110077
2025-07-28 23:29:33,157 - modules.friend_request_window - INFO -    👤 姓名: 金鸣
2025-07-28 23:29:33,157 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 23:29:33,158 - modules.friend_request_window - INFO -    📝 备注格式: '014325110077-金鸣-2025-07-29 07:29:33'
2025-07-28 23:29:33,158 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 23:29:33,158 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110077-金鸣-2025-07-29 07:29:33'
2025-07-28 23:29:33,159 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 23:29:33,160 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 4983710, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-28 23:29:33,161 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 4983710)
2025-07-28 23:29:33,162 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-28 23:29:33,162 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-28 23:29:33,162 - modules.friend_request_window - INFO - 🔄 激活窗口: 4983710
2025-07-28 23:29:33,869 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-28 23:29:33,869 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-28 23:29:33,870 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-28 23:29:33,870 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:499 in _analyze_search_result
2025-07-28 23:29:33,870 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-28 23:29:33,871 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 23:29:33,871 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-28 23:29:33,871 - modules.friend_request_window - INFO -    3. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:695 in _handle_friend_request_window
2025-07-28 23:29:33,871 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-28 23:29:33,872 - modules.friend_request_window - INFO -    4. D:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-28 23:29:33,872 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-28 23:29:33,872 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-28 23:29:33,872 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-28 23:29:33,873 - modules.friend_request_window - INFO -    📝 remark参数: '014325110077-金鸣-2025-07-29 07:29:33' (类型: <class 'str'>, 长度: 35)
2025-07-28 23:29:33,873 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-28 23:29:33,873 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110077-金鸣-2025-07-29 07:29:33'
2025-07-28 23:29:33,873 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-28 23:29:33,874 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-28 23:29:33,874 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-28 23:29:33,875 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-28 23:29:33,875 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-28 23:29:33,875 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-28 23:29:33,876 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-28 23:29:34,784 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-28 23:29:40,035 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-28 23:29:40,035 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-28 23:29:40,036 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-28 23:29:40,036 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-28 23:29:40,037 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'PS D:\程序-测试\微信7.28 - 副本 (2)> python main_controlle...' (前50字符)
2025-07-28 23:29:40,350 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 23:29:40,350 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 23:29:41,253 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 23:29:41,261 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 23:29:41,261 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-28 23:29:41,262 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-28 23:29:41,265 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-28 23:29:41,266 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-28 23:29:41,767 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-28 23:29:41,767 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-28 23:29:41,767 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-28 23:29:41,768 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-28 23:29:41,768 - modules.friend_request_window - INFO -    📝 内容: '014325110077-金鸣-2025-07-29 07:29:33'
2025-07-28 23:29:41,768 - modules.friend_request_window - INFO -    📏 内容长度: 35 字符
2025-07-28 23:29:41,768 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110077-\xe9\x87\x91\xe9\xb8\xa3-2025-07-29 07:29:33'
2025-07-28 23:29:41,769 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-28 23:29:42,683 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-28 23:29:47,933 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-28 23:29:47,933 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-28 23:29:47,934 - modules.friend_request_window - INFO -    📝 原始文本: '014325110077-金鸣-2025-07-29 07:29:33'
2025-07-28 23:29:47,934 - modules.friend_request_window - INFO -    📏 文本长度: 35 字符
2025-07-28 23:29:47,935 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'PS D:\程序-测试\微信7.28 - 副本 (2)> python main_controlle...' (前50字符)
2025-07-28 23:29:48,247 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 23:29:48,248 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 23:29:49,150 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 23:29:49,163 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 23:29:49,165 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110077-金鸣-2025-07-29 07:29:33'
2025-07-28 23:29:49,166 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-28 23:29:49,167 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110077-金鸣-2025-07-29 07:29:33'
2025-07-28 23:29:49,168 - modules.friend_request_window - INFO -    � 内容长度: 35 字符
2025-07-28 23:29:49,669 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110077-金鸣-2025-07-29 07:29:33'
2025-07-28 23:29:49,669 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-28 23:29:49,669 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-28 23:29:49,670 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-28 23:29:49,670 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-28 23:29:49,670 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-28 23:29:49,671 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-28 23:29:50,471 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-28 23:29:50,472 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-28 23:29:50,472 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-28 23:29:51,090 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:29:51,091 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-28 23:29:51,091 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-28 23:29:51,092 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-28 23:29:51,614 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:51,860 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:52,118 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:52,360 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:52,594 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:52,826 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:53,069 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:53,305 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:53,566 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:53,802 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:54,040 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:54,276 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:54,511 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:54,749 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:54,988 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:55,243 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:55,477 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:55,718 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:55,954 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:29:56,172 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-28 23:29:56,172 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-28 23:29:57,173 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 23:29:57,176 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-28 23:29:57,176 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-28 23:29:57,177 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-28 23:29:57,177 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-28 23:29:57,177 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 23:29:57,177 - modules.friend_request_window - INFO -    📝 备注信息: '014325110077-金鸣-2025-07-29 07:29:33'
2025-07-28 23:29:57,679 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-28 23:29:57,680 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:29:57,681 - modules.wechat_auto_add_simple - INFO - ✅ 15167321838 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-28 23:29:57,682 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 15167321838
2025-07-28 23:29:57,683 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:30:01,099 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 3/2971
2025-07-28 23:30:01,099 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13907130670 (刘敬星)
2025-07-28 23:30:01,100 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:30:07,771 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13907130670
2025-07-28 23:30:07,772 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 23:30:07,772 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13907130670 执行添加朋友操作...
2025-07-28 23:30:07,773 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 23:30:07,773 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 23:30:07,774 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 23:30:07,776 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 23:30:07,785 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 23:30:07,790 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 23:30:07,792 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 23:30:07,793 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 23:30:07,793 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 23:30:07,793 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 23:30:07,794 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 23:30:07,795 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 23:30:07,802 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-28 23:30:07,808 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 23:30:07,819 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 23:30:07,830 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 23:30:07,836 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-28 23:30:07,840 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-28 23:30:08,345 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-28 23:30:08,349 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-28 23:30:08,476 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差36.37, 边缘比例0.0378
2025-07-28 23:30:08,490 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_233008.png
2025-07-28 23:30:08,492 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 23:30:08,498 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 23:30:08,502 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-28 23:30:08,504 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-28 23:30:08,506 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-28 23:30:08,514 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_233008.png
2025-07-28 23:30:08,519 - WeChatAutoAdd - INFO - 底部区域原始检测到 3 个轮廓
2025-07-28 23:30:08,521 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-28 23:30:08,524 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-28 23:30:08,534 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(326,209), 尺寸2x240, 长宽比0.01, 面积480
2025-07-28 23:30:08,541 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-28 23:30:08,543 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-28 23:30:08,557 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-28 23:30:08,559 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-28 23:30:08,566 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 23:30:08,608 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_233008.png
2025-07-28 23:30:08,610 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 23:30:08,621 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-28 23:30:08,627 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_233008.png
2025-07-28 23:30:08,683 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 23:30:08,687 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-28 23:30:08,689 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 23:30:08,692 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 23:30:09,000 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-28 23:30:09,783 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 23:30:09,804 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 23:30:09,821 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:30:09,823 - modules.wechat_auto_add_simple - INFO - ✅ 13907130670 添加朋友操作执行成功
2025-07-28 23:30:09,824 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:30:09,826 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 23:30:11,841 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-28 23:30:11,842 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-28 23:30:11,842 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-28 23:30:11,842 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 23:30:11,843 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 23:30:11,843 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 23:30:11,843 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 23:30:11,846 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 23:30:11,846 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-28 23:30:11,848 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13907130670
2025-07-28 23:30:11,849 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-28 23:30:11,849 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 23:30:11,850 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:695 in _handle_friend_request_window
2025-07-28 23:30:11,851 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-28 23:30:11,851 - modules.friend_request_window - INFO -    📱 phone: '13907130670'
2025-07-28 23:30:11,852 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-28 23:30:11,853 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-28 23:30:12,514 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-28 23:30:12,514 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-28 23:30:12,515 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-28 23:30:12,517 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 23:30:12,520 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13907130670
2025-07-28 23:30:12,520 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-28 23:30:12,523 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 23:30:12,531 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-28 23:30:12,534 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-28 23:30:12,535 - modules.friend_request_window - INFO -    📱 手机号码: 13907130670
2025-07-28 23:30:12,536 - modules.friend_request_window - INFO -    🆔 准考证: 014325110078
2025-07-28 23:30:12,537 - modules.friend_request_window - INFO -    👤 姓名: 刘敬星
2025-07-28 23:30:12,539 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 23:30:12,539 - modules.friend_request_window - INFO -    📝 备注格式: '014325110078-刘敬星-2025-07-29 07:30:12'
2025-07-28 23:30:12,540 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 23:30:12,540 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110078-刘敬星-2025-07-29 07:30:12'
2025-07-28 23:30:12,541 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 23:30:12,542 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 8325408, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-28 23:30:12,554 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 8325408)
2025-07-28 23:30:12,555 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-28 23:30:12,555 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-28 23:30:12,556 - modules.friend_request_window - INFO - 🔄 激活窗口: 8325408
2025-07-28 23:30:13,259 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-28 23:30:13,260 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-28 23:30:13,264 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-28 23:30:13,266 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:499 in _analyze_search_result
2025-07-28 23:30:13,271 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-28 23:30:13,271 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 23:30:13,272 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-28 23:30:13,272 - modules.friend_request_window - INFO -    3. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:695 in _handle_friend_request_window
2025-07-28 23:30:13,273 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-28 23:30:13,275 - modules.friend_request_window - INFO -    4. D:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-28 23:30:13,276 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-28 23:30:13,287 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-28 23:30:13,288 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-28 23:30:13,288 - modules.friend_request_window - INFO -    📝 remark参数: '014325110078-刘敬星-2025-07-29 07:30:12' (类型: <class 'str'>, 长度: 36)
2025-07-28 23:30:13,289 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-28 23:30:13,289 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110078-刘敬星-2025-07-29 07:30:12'
2025-07-28 23:30:13,290 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-28 23:30:13,290 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-28 23:30:13,290 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-28 23:30:13,291 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-28 23:30:13,291 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-28 23:30:13,293 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-28 23:30:13,297 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-28 23:30:14,217 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-28 23:30:19,467 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-28 23:30:19,468 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-28 23:30:19,468 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-28 23:30:19,469 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-28 23:30:19,470 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'PS D:\程序-测试\微信7.28 - 副本 (2)> python main_controlle...' (前50字符)
2025-07-28 23:30:19,784 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 23:30:19,785 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 23:30:20,687 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 23:30:20,703 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 23:30:20,705 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-28 23:30:20,706 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-28 23:30:20,706 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-28 23:30:20,707 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-28 23:30:21,208 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-28 23:30:21,208 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-28 23:30:21,208 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-28 23:30:21,209 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-28 23:30:21,209 - modules.friend_request_window - INFO -    📝 内容: '014325110078-刘敬星-2025-07-29 07:30:12'
2025-07-28 23:30:21,209 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-28 23:30:21,209 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110078-\xe5\x88\x98\xe6\x95\xac\xe6\x98\x9f-2025-07-29 07:30:12'
2025-07-28 23:30:21,213 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-28 23:30:22,132 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-28 23:30:27,439 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-28 23:30:27,440 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-28 23:30:27,440 - modules.friend_request_window - INFO -    📝 原始文本: '014325110078-刘敬星-2025-07-29 07:30:12'
2025-07-28 23:30:27,440 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-28 23:30:27,441 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'PS D:\程序-测试\微信7.28 - 副本 (2)> python main_controlle...' (前50字符)
2025-07-28 23:30:27,754 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 23:30:27,755 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 23:30:28,661 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 23:30:28,673 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 23:30:28,674 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110078-刘敬星-2025-07-29 07:30:12'
2025-07-28 23:30:28,674 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-28 23:30:28,675 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110078-刘敬星-2025-07-29 07:30:12'
2025-07-28 23:30:28,676 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-28 23:30:29,179 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110078-刘敬星-2025-07-29 07:30:12'
2025-07-28 23:30:29,180 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-28 23:30:29,181 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-28 23:30:29,181 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-28 23:30:29,182 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-28 23:30:29,182 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-28 23:30:29,182 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-28 23:30:29,983 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-28 23:30:29,984 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-28 23:30:29,984 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-28 23:30:30,637 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:30:30,638 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-28 23:30:30,638 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-28 23:30:30,639 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-28 23:30:31,168 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:30:31,415 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:30:31,655 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:30:31,906 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:30:32,229 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:30:32,471 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:30:32,716 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:30:32,967 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:30:33,203 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:30:33,452 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:30:33,690 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:30:33,939 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:30:34,189 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:30:34,432 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:30:34,683 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:30:34,922 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:30:35,166 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:30:35,408 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:30:35,652 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:30:35,884 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-28 23:30:35,884 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-28 23:30:36,885 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 23:30:36,888 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-28 23:30:36,889 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-28 23:30:36,889 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-28 23:30:36,890 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-28 23:30:36,890 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 23:30:36,891 - modules.friend_request_window - INFO -    📝 备注信息: '014325110078-刘敬星-2025-07-29 07:30:12'
2025-07-28 23:30:37,396 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-28 23:30:37,397 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:30:37,398 - modules.wechat_auto_add_simple - INFO - ✅ 13907130670 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-28 23:30:37,398 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 13907130670
2025-07-28 23:30:37,399 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:30:41,137 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 4/2971
2025-07-28 23:30:41,137 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18680683915 (陈浪)
2025-07-28 23:30:41,137 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:30:47,735 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18680683915
2025-07-28 23:30:47,735 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 23:30:47,736 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18680683915 执行添加朋友操作...
2025-07-28 23:30:47,736 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 23:30:47,736 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 23:30:47,737 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 23:30:47,739 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 23:30:47,746 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 23:30:47,749 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 23:30:47,750 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 23:30:47,750 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 23:30:47,751 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 23:30:47,752 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 23:30:47,753 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 23:30:47,755 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 23:30:47,767 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-28 23:30:47,772 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 23:30:47,774 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 23:30:47,787 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 23:30:47,790 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-28 23:30:47,804 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-28 23:30:48,311 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-28 23:30:48,313 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-28 23:30:48,397 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差42.78, 边缘比例0.0428
2025-07-28 23:30:48,409 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_233048.png
2025-07-28 23:30:48,414 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 23:30:48,417 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 23:30:48,420 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-28 23:30:48,423 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-28 23:30:48,428 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-28 23:30:48,437 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_233048.png
2025-07-28 23:30:48,440 - WeChatAutoAdd - INFO - 底部区域原始检测到 27 个轮廓
2025-07-28 23:30:48,553 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,323), 尺寸128x30, 长宽比4.27, 已知特征:True
2025-07-28 23:30:48,609 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=323 (距底部154像素区域)
2025-07-28 23:30:48,623 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 23:30:48,671 - WeChatAutoAdd - INFO - 发现已知按钮特征候选: 位置(100,323), 尺寸128x30
2025-07-28 23:30:48,684 - WeChatAutoAdd - INFO - 发现已知按钮特征: 位置(100,323), 尺寸128x30
2025-07-28 23:30:48,690 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(81,259), 尺寸8x4, 长宽比2.00, 面积32
2025-07-28 23:30:48,701 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,258), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 23:30:48,705 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(49,257), 尺寸1x5, 长宽比0.20, 面积5
2025-07-28 23:30:48,717 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(47,257), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:30:48,719 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(71,256), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 23:30:48,721 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,255), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 23:30:48,724 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(73,254), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:30:48,732 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:30:48,736 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,253), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 23:30:48,740 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(55,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:30:48,749 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(120,249), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-28 23:30:48,755 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=63.1 (阈值:60)
2025-07-28 23:30:48,765 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(52,249), 尺寸39x14, 长宽比2.79, 面积546
2025-07-28 23:30:48,768 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(51,249), 尺寸25x12, 长宽比2.08, 面积300
2025-07-28 23:30:48,770 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(48,249), 尺寸13x7, 长宽比1.86, 面积91
2025-07-28 23:30:48,773 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(200,236), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:30:48,782 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(186,236), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:30:48,785 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(196,234), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 23:30:48,788 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(182,234), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 23:30:48,792 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(199,233), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 23:30:48,799 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(185,233), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 23:30:48,801 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(156,233), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:30:48,804 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(158,231), 尺寸61x13, 长宽比4.69, 面积793
2025-07-28 23:30:48,808 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=89.9 (阈值:60)
2025-07-28 23:30:48,817 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,231), 尺寸13x12, 长宽比1.08, 面积156
2025-07-28 23:30:48,819 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=68.6 (阈值:60)
2025-07-28 23:30:48,824 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,230), 尺寸28x14, 长宽比2.00, 面积392
2025-07-28 23:30:48,849 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=95.2 (阈值:60)
2025-07-28 23:30:48,884 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(326,209), 尺寸2x240, 长宽比0.01, 面积480
2025-07-28 23:30:48,902 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-28 23:30:48,916 - WeChatAutoAdd - INFO - 底部区域找到 3 个按钮候选
2025-07-28 23:30:48,933 - WeChatAutoAdd - INFO - 选择已知按钮特征: Y=323, 很可能是'添加到通讯录'按钮
2025-07-28 23:30:48,936 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 338), 尺寸: 128x30, 位置得分: 5.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-07-28 23:30:48,940 - WeChatAutoAdd - INFO - 检测到高置信度按钮，跳过文字验证
2025-07-28 23:30:48,959 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_233048.png
2025-07-28 23:30:48,965 - WeChatAutoAdd - INFO - 检测到高置信度按钮（已知特征/绿色按钮），跳过文字验证
2025-07-28 23:30:48,968 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 23:30:49,271 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 338) -> 屏幕坐标(1364, 338)
2025-07-28 23:30:50,129 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 23:30:50,139 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 23:30:50,151 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:30:50,151 - modules.wechat_auto_add_simple - INFO - ✅ 18680683915 添加朋友操作执行成功
2025-07-28 23:30:50,152 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:30:50,152 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 23:30:52,154 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-28 23:30:52,155 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-28 23:30:52,155 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-28 23:30:52,156 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 23:30:52,156 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 23:30:52,157 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 23:30:52,157 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 23:30:52,157 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 23:30:52,162 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-28 23:30:52,163 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18680683915
2025-07-28 23:30:52,165 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-28 23:30:52,166 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 23:30:52,166 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:695 in _handle_friend_request_window
2025-07-28 23:30:52,167 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-28 23:30:52,168 - modules.friend_request_window - INFO -    📱 phone: '18680683915'
2025-07-28 23:30:52,168 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-28 23:30:52,169 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-28 23:30:52,690 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-28 23:30:52,690 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-28 23:30:52,691 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-28 23:30:52,693 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 23:30:52,695 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18680683915
2025-07-28 23:30:52,695 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-28 23:30:52,696 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 23:30:52,697 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-28 23:30:52,698 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-28 23:30:52,698 - modules.friend_request_window - INFO -    📱 手机号码: 18680683915
2025-07-28 23:30:52,698 - modules.friend_request_window - INFO -    🆔 准考证: 014325110079
2025-07-28 23:30:52,699 - modules.friend_request_window - INFO -    👤 姓名: 陈浪
2025-07-28 23:30:52,699 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 23:30:52,699 - modules.friend_request_window - INFO -    📝 备注格式: '014325110079-陈浪-2025-07-29 07:30:52'
2025-07-28 23:30:52,700 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 23:30:52,700 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110079-陈浪-2025-07-29 07:30:52'
2025-07-28 23:30:52,701 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 23:30:52,702 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 8587954, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-28 23:30:52,704 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 8587954)
2025-07-28 23:30:52,705 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-28 23:30:52,705 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-28 23:30:52,705 - modules.friend_request_window - INFO - 🔄 激活窗口: 8587954
2025-07-28 23:30:53,407 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-28 23:30:53,431 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-28 23:30:53,467 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-28 23:30:53,505 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:499 in _analyze_search_result
2025-07-28 23:30:53,551 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-28 23:30:53,571 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 23:30:53,591 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-28 23:30:53,602 - modules.friend_request_window - INFO -    3. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:695 in _handle_friend_request_window
2025-07-28 23:30:53,603 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-28 23:30:53,604 - modules.friend_request_window - INFO -    4. D:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-28 23:30:53,606 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-28 23:30:53,610 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-28 23:30:53,614 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-28 23:30:53,616 - modules.friend_request_window - INFO -    📝 remark参数: '014325110079-陈浪-2025-07-29 07:30:52' (类型: <class 'str'>, 长度: 35)
2025-07-28 23:30:53,618 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-28 23:30:53,619 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110079-陈浪-2025-07-29 07:30:52'
2025-07-28 23:30:53,619 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-28 23:30:53,620 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-28 23:30:53,620 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-28 23:30:53,622 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-28 23:30:53,623 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-28 23:30:53,624 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-28 23:30:53,629 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-28 23:30:54,547 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-28 23:30:59,848 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-28 23:30:59,849 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-28 23:30:59,849 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-28 23:30:59,849 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-28 23:30:59,850 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'PS D:\程序-测试\微信7.28 - 副本 (2)> python main_controlle...' (前50字符)
2025-07-28 23:31:00,161 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 23:31:00,161 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 23:31:01,067 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 23:31:01,079 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 23:31:01,080 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-28 23:31:01,081 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-28 23:31:01,081 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-28 23:31:01,081 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-28 23:31:01,582 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-28 23:31:01,582 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-28 23:31:01,583 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-28 23:31:01,583 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-28 23:31:01,583 - modules.friend_request_window - INFO -    📝 内容: '014325110079-陈浪-2025-07-29 07:30:52'
2025-07-28 23:31:01,584 - modules.friend_request_window - INFO -    📏 内容长度: 35 字符
2025-07-28 23:31:01,584 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110079-\xe9\x99\x88\xe6\xb5\xaa-2025-07-29 07:30:52'
2025-07-28 23:31:01,584 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-28 23:31:02,495 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-28 23:31:05,793 - __main__ - INFO - ⏹️ 用户中断程序执行
