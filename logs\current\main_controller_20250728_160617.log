2025-07-28 16:06:17,226 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 16:06:17,228 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 16:06:17,228 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-28 16:06:17,228 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 16:06:17,229 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-28 16:06:17,232 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 16:06:17,233 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 16:06:17,234 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 16:06:17,235 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 16:06:17,235 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 16:06:17,236 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 16:06:17,239 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 16:06:17,244 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250728_160617.log
2025-07-28 16:06:17,245 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-28 16:06:17,247 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-28 16:06:17,248 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-28 16:06:17,248 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-28 16:06:17,248 - __main__ - INFO - 📅 当前北京时间: 2025-07-29 00:06:17
2025-07-28 16:06:17,249 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-28 16:06:17,249 - __main__ - INFO - 📅 启动时间: 2025-07-29 00:06:17
2025-07-28 16:06:17,249 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-28 16:06:17,250 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 16:06:17,790 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 16:06:17,791 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 16:06:18,333 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 16:06:18,333 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 16:06:18,337 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-28 16:06:18,337 - __main__ - INFO - ✅ 找到 2 个微信窗口
2025-07-28 16:06:18,338 - __main__ - INFO -   窗口 1: 微信 (句柄: 197994)
2025-07-28 16:06:18,338 - __main__ - INFO -   窗口 2: 微信 (句柄: 525668)
2025-07-28 16:06:18,338 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-28 16:06:19,284 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-28 16:06:19,286 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-28 16:06:19,286 - __main__ - INFO - 📋 待处理联系人数: 2981
2025-07-28 16:06:19,287 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-28 16:06:19,287 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2981
2025-07-28 16:06:19,287 - __main__ - INFO - 
============================================================
2025-07-28 16:06:19,288 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口
2025-07-28 16:06:19,288 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-28 16:06:19,288 - __main__ - INFO - ============================================================
2025-07-28 16:06:19,289 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-28 16:06:19,289 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-28 16:06:19,289 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-28 16:06:19,290 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-28 16:06:19,290 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 16:06:19,593 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 16:06:19,594 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 16:06:19,594 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 16:06:19,595 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 16:06:19,595 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 16:06:19,596 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 16:06:19,596 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 16:06:19,596 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 16:06:19,597 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 16:06:19,597 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 16:06:19,799 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 16:06:19,799 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 16:06:19,799 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-28 16:06:19,800 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 16:06:20,103 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 16:06:20,104 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 16:06:20,104 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 16:06:20,105 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 16:06:20,105 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 16:06:20,105 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 16:06:20,105 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 16:06:20,106 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 16:06:20,106 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 16:06:20,106 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 16:06:20,308 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 16:06:20,308 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 16:06:20,310 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-28 16:06:20,611 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 16:06:20,612 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 16:06:20,612 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 16:06:20,613 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 16:06:20,613 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 16:06:20,614 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 16:06:20,615 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-28 16:06:20,615 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-28 16:06:21,615 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-28 16:06:21,616 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-28 16:06:21,616 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-28 16:06:21,617 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-28 16:06:21,617 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-28 16:06:21,617 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-28 16:06:21,617 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-28 16:06:21,618 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-28 16:06:21,820 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-28 16:06:21,821 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-28 16:06:24,205 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-28 16:06:24,205 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-28 16:06:24,205 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-28 16:06:26,512 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-28 16:06:26,714 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-28 16:06:26,714 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-28 16:06:29,087 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-28 16:06:29,088 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-28 16:06:29,088 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.0 秒...
2025-07-28 16:06:31,065 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-28 16:06:31,266 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-28 16:06:31,266 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-28 16:06:33,638 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-28 16:06:33,638 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-28 16:06:33,638 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.4 秒...
2025-07-28 16:06:36,060 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-28 16:06:36,261 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-28 16:06:36,262 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-28 16:06:38,636 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-28 16:06:38,637 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-28 16:06:38,637 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.8 秒...
2025-07-28 16:06:41,400 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-28 16:06:41,601 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-28 16:06:41,601 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-28 16:06:43,988 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-28 16:06:43,988 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-28 16:06:43,989 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-28 16:06:43,989 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-28 16:06:43,990 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 16:06:43,992 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 16:06:43,992 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 16:06:43,993 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 16:06:43,993 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 16:06:43,994 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 2886688, 进程: Weixin.exe)
2025-07-28 16:06:43,997 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-28 16:06:43,997 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 2886688)
2025-07-28 16:06:43,998 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 2886688) - 增强版
2025-07-28 16:06:44,301 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 16:06:44,302 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 16:06:44,302 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-28 16:06:44,302 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-28 16:06:44,303 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-28 16:06:44,303 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-28 16:06:44,508 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-28 16:06:44,508 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-28 16:06:44,711 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 16:06:44,711 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 16:06:44,712 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-28 16:06:44,712 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-28 16:06:44,712 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-28 16:06:44,712 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-28 16:06:44,713 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-28 16:06:45,713 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-28 16:06:45,714 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 16:06:45,715 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 16:06:45,715 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 16:06:45,717 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 16:06:45,718 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 16:06:45,720 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 2886688, 进程: Weixin.exe)
2025-07-28 16:06:45,722 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-28 16:06:45,723 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-28 16:06:45,723 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-28 16:06:45,724 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-28 16:06:45,725 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-28 16:06:45,725 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 16:06:46,035 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 16:06:46,035 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 16:06:46,035 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 16:06:46,036 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 16:06:46,036 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 16:06:46,036 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 16:06:46,037 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 16:06:46,037 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 16:06:46,037 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 16:06:46,038 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 16:06:46,240 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 16:06:46,240 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 16:06:46,242 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-28 16:06:46,542 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 16:06:46,543 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-28 16:06:46,543 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-28 16:06:47,544 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-28 16:06:47,545 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-28 16:06:47,545 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-28 16:06:47,548 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250728_160647.log
2025-07-28 16:06:47,548 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-28 16:06:47,548 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-28 16:06:47,549 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-28 16:06:47,550 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-28 16:06:47,555 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-28 16:06:47,555 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-28 16:06:47,556 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-28 16:06:47,556 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-28 16:06:47,556 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-28 16:06:47,557 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-28 16:06:47,558 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-28 16:06:47,560 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 16:06:47,560 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 2886688
2025-07-28 16:06:47,561 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 2886688) - 增强版
2025-07-28 16:06:47,869 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 16:06:47,870 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 16:06:47,871 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-28 16:06:47,871 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-28 16:06:47,872 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-28 16:06:47,872 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-28 16:06:47,873 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-28 16:06:47,873 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-28 16:06:48,075 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 16:06:48,076 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 16:06:48,079 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 2886688 (API返回: None)
2025-07-28 16:06:48,380 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 16:06:48,380 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-28 16:06:48,380 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-28 16:06:48,381 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-28 16:06:48,381 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 16:06:48,382 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-28 16:06:48,382 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-28 16:06:48,388 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-28 16:06:48,388 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-28 16:06:48,915 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-28 16:06:48,915 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 16:06:49,207 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2981 个
2025-07-28 16:06:49,208 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2981 个 (总计: 3135 个)
2025-07-28 16:06:49,209 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 16:06:49,209 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-28 16:06:49,210 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2981
2025-07-28 16:06:49,211 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13755762006 (李宇晴)
2025-07-28 16:06:49,211 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 16:06:55,777 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13755762006
2025-07-28 16:06:55,777 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 16:06:55,777 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13755762006 执行添加朋友操作...
2025-07-28 16:06:55,777 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 16:06:55,778 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 16:06:55,779 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 16:06:55,779 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 16:06:55,782 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 2 个图片文件
2025-07-28 16:06:55,784 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 16:06:55,787 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 16:06:55,789 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 16:06:55,790 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 16:06:55,791 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 16:06:55,792 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 16:06:55,793 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 16:06:55,800 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-28 16:06:55,804 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 16:06:55,810 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 16:06:55,815 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 16:06:55,824 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-28 16:06:55,826 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-28 16:06:56,327 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-28 16:06:56,328 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-28 16:06:56,393 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差28.67, 边缘比例0.0351
2025-07-28 16:06:56,400 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_160656.png
2025-07-28 16:06:56,401 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 16:06:56,402 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 16:06:56,403 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-28 16:06:56,404 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-28 16:06:56,406 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-28 16:06:56,414 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_160656.png
2025-07-28 16:06:56,415 - WeChatAutoAdd - INFO - 底部区域原始检测到 38 个轮廓
2025-07-28 16:06:56,417 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 16:06:56,418 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 16:06:56,419 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 16:06:56,420 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-07-28 16:06:56,421 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 16:06:56,422 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 16:06:56,423 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-07-28 16:06:56,424 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 16:06:56,425 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-07-28 16:06:56,430 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-07-28 16:06:56,432 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-07-28 16:06:56,435 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 16:06:56,438 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 16:06:56,441 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 16:06:56,448 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 16:06:56,450 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 16:06:56,458 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 16:06:56,460 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 16:06:56,461 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 16:06:56,462 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-07-28 16:06:56,464 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 16:06:56,465 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-07-28 16:06:56,475 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 16:06:56,478 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-07-28 16:06:56,479 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-07-28 16:06:56,480 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-07-28 16:06:56,481 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-07-28 16:06:56,485 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-07-28 16:06:56,490 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-07-28 16:06:56,492 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-07-28 16:06:56,494 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-07-28 16:06:56,495 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-07-28 16:06:56,497 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-07-28 16:06:56,498 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-07-28 16:06:56,501 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-07-28 16:06:56,503 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-07-28 16:06:56,507 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-07-28 16:06:56,508 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-28 16:06:56,509 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-07-28 16:06:56,509 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-07-28 16:06:56,510 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-28 16:06:56,511 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 16:06:56,520 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_160656.png
2025-07-28 16:06:56,524 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 16:06:56,542 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-07-28 16:06:56,554 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_160656.png
2025-07-28 16:06:56,663 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 16:06:56,690 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-07-28 16:06:56,693 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 16:06:56,696 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 16:06:57,023 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-07-28 16:06:57,802 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 16:06:57,803 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 16:06:57,804 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 16:06:57,805 - modules.wechat_auto_add_simple - INFO - ✅ 13755762006 添加朋友操作执行成功
2025-07-28 16:06:57,805 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 16:06:57,805 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 16:06:59,807 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-28 16:06:59,807 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-28 16:06:59,807 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-28 16:06:59,808 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 16:06:59,808 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 16:06:59,808 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 16:06:59,809 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 16:06:59,809 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 16:06:59,810 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-28 16:06:59,810 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13755762006
2025-07-28 16:06:59,813 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-28 16:06:59,814 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 16:06:59,814 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:684 in _handle_friend_request_window
2025-07-28 16:06:59,815 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-28 16:06:59,816 - modules.friend_request_window - INFO -    📱 phone: '13755762006'
2025-07-28 16:06:59,816 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-28 16:06:59,817 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-28 16:07:00,369 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-28 16:07:00,370 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-28 16:07:00,370 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-28 16:07:00,371 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 16:07:00,372 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13755762006
2025-07-28 16:07:00,373 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-28 16:07:00,373 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 16:07:00,375 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-28 16:07:00,375 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-28 16:07:00,375 - modules.friend_request_window - INFO -    📱 手机号码: 13755762006
2025-07-28 16:07:00,376 - modules.friend_request_window - INFO -    🆔 准考证: 015825120188
2025-07-28 16:07:00,376 - modules.friend_request_window - INFO -    👤 姓名: 李宇晴
2025-07-28 16:07:00,377 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 16:07:00,377 - modules.friend_request_window - INFO -    📝 备注格式: '015825120188-李宇晴-2025-07-29 00:07:00'
2025-07-28 16:07:00,377 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 16:07:00,378 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '015825120188-李宇晴-2025-07-29 00:07:00'
2025-07-28 16:07:00,378 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 16:07:00,380 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-28 16:07:00,380 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-28 16:07:00,381 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 16:07:00,381 - modules.wechat_auto_add_simple - INFO - ✅ 13755762006 申请添加朋友窗口处理完成: 申请添加朋友窗口处理失败: 未找到好友申请窗口
2025-07-28 16:07:00,381 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 13755762006
2025-07-28 16:07:00,384 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 16:07:04,072 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2981
2025-07-28 16:07:04,073 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15527919808 (朱腾万)
2025-07-28 16:07:04,073 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 16:07:10,625 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15527919808
2025-07-28 16:07:10,626 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 16:07:10,626 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15527919808 执行添加朋友操作...
2025-07-28 16:07:10,626 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 16:07:10,627 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 16:07:10,627 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 16:07:10,628 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 16:07:10,632 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 16:07:10,634 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 16:07:10,635 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 16:07:10,635 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 16:07:10,636 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 16:07:10,636 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 16:07:10,636 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 16:07:10,637 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 16:07:10,646 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-28 16:07:10,648 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 16:07:10,650 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 16:07:10,652 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 16:07:10,655 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-28 16:07:10,656 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-28 16:07:11,162 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-28 16:07:11,163 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-28 16:07:11,237 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差37.76, 边缘比例0.0440
2025-07-28 16:07:11,245 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_160711.png
2025-07-28 16:07:11,247 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 16:07:11,252 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 16:07:11,255 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-28 16:07:11,256 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-28 16:07:11,257 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-28 16:07:11,261 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_160711.png
2025-07-28 16:07:11,264 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-28 16:07:11,270 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-28 16:07:11,271 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-28 16:07:11,272 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-28 16:07:11,273 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-28 16:07:11,276 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-28 16:07:11,277 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-28 16:07:11,278 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 16:07:11,288 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_160711.png
2025-07-28 16:07:11,290 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 16:07:11,291 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-28 16:07:11,295 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_160711.png
2025-07-28 16:07:11,318 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 16:07:11,322 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-28 16:07:11,323 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 16:07:11,324 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 16:07:11,625 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-28 16:07:12,403 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 16:07:12,405 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 16:07:12,410 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 16:07:12,411 - modules.wechat_auto_add_simple - INFO - ✅ 15527919808 添加朋友操作执行成功
2025-07-28 16:07:12,411 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 16:07:12,412 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 16:07:14,413 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-28 16:07:14,413 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-28 16:07:14,414 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-28 16:07:14,414 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 16:07:14,414 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 16:07:14,415 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 16:07:14,415 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 16:07:14,416 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 16:07:14,416 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-28 16:07:14,417 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 15527919808
2025-07-28 16:07:14,417 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-28 16:07:14,418 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 16:07:14,418 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:684 in _handle_friend_request_window
2025-07-28 16:07:14,419 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-28 16:07:14,419 - modules.friend_request_window - INFO -    📱 phone: '15527919808'
2025-07-28 16:07:14,420 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-28 16:07:14,421 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-28 16:07:15,012 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-28 16:07:15,013 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-28 16:07:15,013 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-28 16:07:15,014 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 16:07:15,016 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 15527919808
2025-07-28 16:07:15,016 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-28 16:07:15,017 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 16:07:15,018 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-28 16:07:15,019 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-28 16:07:15,020 - modules.friend_request_window - INFO -    📱 手机号码: 15527919808
2025-07-28 16:07:15,021 - modules.friend_request_window - INFO -    🆔 准考证: 015825120189
2025-07-28 16:07:15,021 - modules.friend_request_window - INFO -    👤 姓名: 朱腾万
2025-07-28 16:07:15,022 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 16:07:15,022 - modules.friend_request_window - INFO -    📝 备注格式: '015825120189-朱腾万-2025-07-29 00:07:15'
2025-07-28 16:07:15,022 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 16:07:15,023 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '015825120189-朱腾万-2025-07-29 00:07:15'
2025-07-28 16:07:15,023 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 16:07:15,026 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 2296894, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-28 16:07:15,028 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 2296894)
2025-07-28 16:07:15,028 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-28 16:07:15,028 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-28 16:07:15,029 - modules.friend_request_window - INFO - 🔄 激活窗口: 2296894
2025-07-28 16:07:15,732 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-28 16:07:15,732 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-28 16:07:15,733 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-28 16:07:15,734 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:499 in _analyze_search_result
2025-07-28 16:07:15,734 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-28 16:07:15,735 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 16:07:15,735 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-28 16:07:15,736 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:684 in _handle_friend_request_window
2025-07-28 16:07:15,736 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-28 16:07:15,736 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1159 in execute_friend_request_flow
2025-07-28 16:07:15,736 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-28 16:07:15,737 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-28 16:07:15,737 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-28 16:07:15,737 - modules.friend_request_window - INFO -    📝 remark参数: '015825120189-朱腾万-2025-07-29 00:07:15' (类型: <class 'str'>, 长度: 36)
2025-07-28 16:07:15,738 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-28 16:07:15,738 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '015825120189-朱腾万-2025-07-29 00:07:15'
2025-07-28 16:07:15,739 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-28 16:07:15,739 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-28 16:07:15,739 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-28 16:07:15,739 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-28 16:07:15,739 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-28 16:07:15,740 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-28 16:07:15,740 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-28 16:07:16,653 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-28 16:07:21,930 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-28 16:07:21,974 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-28 16:07:22,010 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-28 16:07:22,079 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-28 16:07:22,176 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '窗口管理→主界面→简单添加→图像识别→好友申请→频率处理-...' (前50字符)
2025-07-28 16:07:22,529 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 16:07:22,612 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 16:07:23,803 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 16:07:23,884 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 16:07:23,941 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-28 16:07:23,978 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-28 16:07:24,031 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-28 16:07:24,079 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-28 16:07:24,735 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-28 16:07:24,835 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-28 16:07:24,918 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-28 16:07:24,978 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-28 16:07:25,084 - modules.friend_request_window - INFO -    📝 内容: '015825120189-朱腾万-2025-07-29 00:07:15'
2025-07-28 16:07:25,188 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-28 16:07:25,276 - modules.friend_request_window - INFO -    🔤 内容编码: b'015825120189-\xe6\x9c\xb1\xe8\x85\xbe\xe4\xb8\x87-2025-07-29 00:07:15'
2025-07-28 16:07:25,344 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-28 16:07:26,491 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-28 16:07:32,921 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-28 16:07:32,922 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-28 16:07:32,922 - modules.friend_request_window - INFO -    📝 原始文本: '015825120189-朱腾万-2025-07-29 00:07:15'
2025-07-28 16:07:32,923 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-28 16:07:32,923 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '窗口管理→主界面→简单添加→图像识别→好友申请→频率处理-...' (前50字符)
2025-07-28 16:07:33,235 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 16:07:33,236 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 16:07:34,138 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 16:07:34,146 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 16:07:34,149 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '015825120189-朱腾万-2025-07-29 00:07:15'
2025-07-28 16:07:34,150 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-28 16:07:34,150 - modules.friend_request_window - INFO -    📝 已填写内容: '015825120189-朱腾万-2025-07-29 00:07:15'
2025-07-28 16:07:34,151 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-28 16:07:34,652 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '015825120189-朱腾万-2025-07-29 00:07:15'
2025-07-28 16:07:34,652 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-28 16:07:34,652 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-28 16:07:34,653 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-28 16:07:34,653 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-28 16:07:34,653 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-28 16:07:34,654 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-28 16:07:35,455 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-28 16:07:35,455 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-28 16:07:35,455 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-28 16:07:36,067 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 16:07:36,067 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-28 16:07:36,068 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-28 16:07:36,068 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-28 16:07:36,570 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-28 16:07:36,572 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-28 16:07:36,572 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-28 16:07:36,573 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-28 16:07:36,573 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-28 16:07:36,573 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-28 16:07:36,573 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-28 16:07:36,574 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-28 16:07:36,574 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-28 16:07:36,574 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-28 16:07:36,574 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-28 16:07:36,574 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-28 16:07:36,575 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-28 16:07:36,575 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-28 16:07:36,576 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-28 16:07:36,576 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-28 16:07:36,576 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-28 16:07:36,577 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-28 16:07:36,578 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-28 16:07:37,079 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-28 16:07:37,080 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-28 16:07:37,080 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-28 16:07:37,080 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-28 16:07:37,081 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-28 16:07:37,081 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-28 16:07:37,081 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-28 16:07:37,081 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-28 16:07:38,002 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-28 16:07:38,003 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-28 16:07:38,003 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-28 16:07:38,004 - modules.friend_request_window - INFO - 🔄 步骤2: 关闭添加朋友窗口...
2025-07-28 16:07:38,004 - modules.friend_request_window - INFO - 🎯 使用指定坐标 (1504, 13) 关闭添加朋友窗口...
2025-07-28 16:07:38,505 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui点击坐标 (1504, 13)...
2025-07-28 16:07:39,616 - modules.friend_request_window - INFO - ✅ pyautogui点击添加朋友窗口关闭按钮成功
2025-07-28 16:07:39,617 - modules.friend_request_window - INFO - 🔄 步骤3: 关闭当前微信窗口...
2025-07-28 16:07:39,617 - modules.friend_request_window - INFO - 🎯 使用指定坐标 (700, 16) 关闭当前微信窗口...
2025-07-28 16:07:40,118 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui点击坐标 (700, 16)...
2025-07-28 16:07:41,234 - modules.friend_request_window - INFO - ✅ pyautogui点击当前微信窗口关闭按钮成功
2025-07-28 16:07:41,235 - modules.friend_request_window - INFO - 🔄 关闭错误相关窗口...
2025-07-28 16:07:41,259 - modules.friend_request_window - INFO - ✅ 频率错误处理完成
2025-07-28 16:07:41,259 - modules.friend_request_window - INFO - 🔄 频率错误处理完成，需要重新开始流程
2025-07-28 16:07:41,262 - modules.friend_request_window - INFO - 🔄 已设置重新开始标志
2025-07-28 16:07:41,263 - modules.friend_request_window - INFO - ✅ 原有频率错误处理成功，已切换到微信2
2025-07-28 16:07:41,265 - modules.friend_request_window - WARNING - ⚠️ 检测到并处理了频率错误，已切换到微信2
2025-07-28 16:07:41,265 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-28 16:07:41,271 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-28 16:07:41,272 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 16:07:41,273 - modules.friend_request_window - INFO -    📝 备注信息: '015825120189-朱腾万-2025-07-29 00:07:15'
2025-07-28 16:07:41,773 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-28 16:07:41,774 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 16:07:41,775 - modules.wechat_auto_add_simple - INFO - ✅ 15527919808 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-28 16:07:41,775 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 15527919808
2025-07-28 16:07:41,776 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 16:07:45,735 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 3/2981
2025-07-28 16:07:45,736 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13691631959 (卢婵娟)
2025-07-28 16:07:45,736 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 16:07:52,683 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13691631959
2025-07-28 16:07:52,683 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 16:07:52,684 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13691631959 执行添加朋友操作...
2025-07-28 16:07:52,685 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 16:07:52,685 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 16:07:52,686 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 16:07:52,689 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 16:07:52,694 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 16:07:52,698 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 16:07:52,699 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 16:07:52,699 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 16:07:52,700 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 16:07:52,701 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 16:07:52,702 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 16:07:52,702 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 16:07:52,707 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 16:07:52,720 - WeChatAutoAdd - DEBUG - 找到微信窗口: ● main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 16:07:52,724 - WeChatAutoAdd - INFO - 共找到 2 个微信窗口
2025-07-28 16:07:52,726 - WeChatAutoAdd - INFO - 找到微信主窗口: 微信
2025-07-28 16:07:52,735 - WeChatAutoAdd - WARNING - 方法1截图失败: Error code from Windows: 18 - 没有更多文件。, 尝试方法2
2025-07-28 16:07:52,812 - WeChatAutoAdd - INFO - 使用win32方法截取窗口: 
2025-07-28 16:07:52,819 - WeChatAutoAdd - INFO - win32窗口尺寸: 1x1, 位置: (0, 0)
2025-07-28 16:07:52,822 - WeChatAutoAdd - WARNING - win32 BitBlt操作可能失败
2025-07-28 16:07:52,827 - WeChatAutoAdd - WARNING - 截图可能为纯色图像，标准差: 0.0
2025-07-28 16:07:52,835 - WeChatAutoAdd - WARNING - win32截图内容验证失败
2025-07-28 16:07:52,841 - WeChatAutoAdd - INFO - 保存win32截图: screenshots\window_capture_win32_20250728_160752.png
2025-07-28 16:07:52,844 - WeChatAutoAdd - INFO - win32截图成功
2025-07-28 16:07:52,859 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 16:07:52,861 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 16:07:52,869 - WeChatAutoAdd - INFO - 截图尺寸: 1x1
2025-07-28 16:07:52,872 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=0-1 (高度:1)
2025-07-28 16:07:52,878 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_160752.png
2025-07-28 16:07:52,886 - WeChatAutoAdd - INFO - 底部区域原始检测到 0 个轮廓
2025-07-28 16:07:52,888 - WeChatAutoAdd - WARNING - 底部245像素区域未找到符合条件的按钮
2025-07-28 16:07:52,889 - WeChatAutoAdd - WARNING - 底部区域未找到按钮
2025-07-28 16:07:52,893 - WeChatAutoAdd - ERROR - 无法找到'添加到通讯录'按钮，结束当前流程
2025-07-28 16:07:52,895 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 16:07:52,895 - modules.wechat_auto_add_simple - ERROR - ❌ 13691631959 添加朋友操作执行失败
2025-07-28 16:07:52,903 - modules.wechat_auto_add_simple - WARNING - ⚠️ 添加朋友失败: 13691631959 - 添加朋友操作失败，可能原因：未找到'添加朋友'窗口或'添加到通讯录'按钮
2025-07-28 16:07:52,905 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
