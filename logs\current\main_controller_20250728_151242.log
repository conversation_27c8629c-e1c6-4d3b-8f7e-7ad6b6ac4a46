2025-07-28 15:12:42,721 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 15:12:42,722 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 15:12:42,722 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-28 15:12:42,723 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 15:12:42,724 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-28 15:12:42,724 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 15:12:42,725 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 15:12:42,726 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 15:12:42,726 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 15:12:42,727 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 15:12:42,727 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:12:42,730 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 15:12:42,735 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250728_151242.log
2025-07-28 15:12:42,736 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-28 15:12:42,736 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-28 15:12:42,738 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-28 15:12:42,739 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-28 15:12:42,740 - __main__ - INFO - 📅 当前北京时间: 2025-07-28 15:12:42
2025-07-28 15:12:42,743 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-28 15:12:42,745 - __main__ - INFO - 📅 启动时间: 2025-07-28 15:12:42
2025-07-28 15:12:42,748 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-28 15:12:42,751 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 15:12:43,297 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 15:12:43,298 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 15:12:43,840 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 15:12:43,842 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 15:12:43,845 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-28 15:12:43,845 - __main__ - INFO - ✅ 找到 2 个微信窗口
2025-07-28 15:12:43,846 - __main__ - INFO -   窗口 1: 微信 (句柄: 197994)
2025-07-28 15:12:43,846 - __main__ - INFO -   窗口 2: 微信 (句柄: 525668)
2025-07-28 15:12:43,847 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-28 15:12:44,798 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-28 15:12:44,799 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-28 15:12:44,799 - __main__ - INFO - 📋 待处理联系人数: 2995
2025-07-28 15:12:44,799 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-28 15:12:44,801 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2995
2025-07-28 15:12:44,801 - __main__ - INFO - 
============================================================
2025-07-28 15:12:44,802 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口
2025-07-28 15:12:44,802 - __main__ - INFO - ============================================================
2025-07-28 15:12:44,802 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-28 15:12:44,803 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-28 15:12:44,803 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-28 15:12:44,803 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-28 15:12:44,803 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 15:12:45,123 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 15:12:45,123 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 15:12:45,124 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 15:12:45,124 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 15:12:45,124 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 15:12:45,125 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 15:12:45,125 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 15:12:45,125 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 15:12:45,126 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 15:12:45,126 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 15:12:45,328 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 15:12:45,329 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 15:12:45,329 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-28 15:12:45,329 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 15:12:45,633 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 15:12:45,633 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 15:12:45,634 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 15:12:45,634 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 15:12:45,635 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 15:12:45,635 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 15:12:45,635 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 15:12:45,635 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 15:12:45,636 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 15:12:45,636 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 15:12:45,838 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 15:12:45,839 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 15:12:45,840 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-28 15:12:46,141 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 15:12:46,142 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 15:12:46,143 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 15:12:46,143 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 15:12:46,143 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 15:12:46,144 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 15:12:46,144 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-28 15:12:47,144 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-28 15:12:47,145 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-28 15:12:47,145 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-28 15:12:47,146 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-28 15:12:47,146 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-28 15:12:47,147 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-28 15:12:47,147 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-28 15:12:47,147 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-28 15:12:47,348 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-28 15:12:47,349 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-28 15:12:49,737 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-28 15:12:49,737 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-28 15:12:49,738 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.2 秒...
2025-07-28 15:12:51,941 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-28 15:12:52,142 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-28 15:12:52,142 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-28 15:12:54,519 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-28 15:12:54,519 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-28 15:12:54,520 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.5 秒...
2025-07-28 15:12:57,030 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-28 15:12:57,231 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-28 15:12:57,232 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-28 15:12:59,603 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-28 15:12:59,604 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-28 15:12:59,604 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.8 秒...
2025-07-28 15:13:01,428 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-28 15:13:01,629 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-28 15:13:01,629 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-28 15:13:04,002 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-28 15:13:04,002 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-28 15:13:04,003 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.0 秒...
2025-07-28 15:13:06,034 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-28 15:13:06,235 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-28 15:13:06,235 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-28 15:13:08,619 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-28 15:13:08,620 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-28 15:13:08,620 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-28 15:13:08,621 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-28 15:13:08,621 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 15:13:08,622 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 15:13:08,623 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 15:13:08,624 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 15:13:08,624 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 15:13:08,625 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1575836, 进程: Weixin.exe)
2025-07-28 15:13:08,628 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-28 15:13:08,628 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 1575836)
2025-07-28 15:13:08,629 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1575836) - 增强版
2025-07-28 15:13:08,937 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 15:13:08,937 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 15:13:08,937 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-28 15:13:08,938 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-28 15:13:08,938 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-28 15:13:08,939 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-28 15:13:09,145 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-28 15:13:09,145 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-28 15:13:09,347 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 15:13:09,347 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 15:13:09,348 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-28 15:13:09,348 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-28 15:13:09,349 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-28 15:13:09,349 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-28 15:13:09,349 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-28 15:13:10,350 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-28 15:13:10,351 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 15:13:10,352 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 15:13:10,353 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 15:13:10,353 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 15:13:10,353 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 15:13:10,354 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1575836, 进程: Weixin.exe)
2025-07-28 15:13:10,356 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-28 15:13:10,357 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-28 15:13:10,357 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-28 15:13:10,358 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-28 15:13:10,358 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-28 15:13:10,359 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 15:13:10,666 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 15:13:10,666 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 15:13:10,667 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 15:13:10,667 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 15:13:10,667 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 15:13:10,667 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 15:13:10,668 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 15:13:10,668 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 15:13:10,668 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 15:13:10,669 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 15:13:10,871 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 15:13:10,871 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 15:13:10,874 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-28 15:13:11,175 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 15:13:11,175 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-28 15:13:12,176 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-28 15:13:12,177 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-28 15:13:12,177 - __main__ - INFO - 🚀 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-28 15:13:12,180 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250728_151312.log
2025-07-28 15:13:12,180 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-28 15:13:12,180 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-28 15:13:12,180 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-28 15:13:12,181 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-28 15:13:12,184 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-28 15:13:12,185 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-28 15:13:12,185 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-28 15:13:12,185 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-28 15:13:12,186 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-28 15:13:12,186 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-28 15:13:12,186 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-28 15:13:12,187 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 15:13:12,187 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 1575836
2025-07-28 15:13:12,188 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1575836) - 增强版
2025-07-28 15:13:12,495 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 15:13:12,496 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 15:13:12,496 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-28 15:13:12,497 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-28 15:13:12,497 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-28 15:13:12,497 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-28 15:13:12,497 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-28 15:13:12,498 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-28 15:13:12,700 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 15:13:12,700 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 15:13:12,704 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 1575836 (API返回: None)
2025-07-28 15:13:13,005 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 15:13:13,006 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-28 15:13:13,006 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-28 15:13:13,006 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-28 15:13:13,007 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 15:13:13,008 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-28 15:13:13,008 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-28 15:13:13,013 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-28 15:13:13,013 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-28 15:13:13,541 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-28 15:13:13,541 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 15:13:13,814 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2995 个
2025-07-28 15:13:13,815 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2995 个 (总计: 3135 个)
2025-07-28 15:13:13,816 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2995
2025-07-28 15:13:13,817 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 17707167456 (李珂)
2025-07-28 15:13:20,392 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 17707167456
2025-07-28 15:13:20,392 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 15:13:20,392 - modules.wechat_auto_add_simple - INFO - 👥 开始为 17707167456 执行添加朋友操作...
2025-07-28 15:13:20,393 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 15:13:20,393 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 15:13:20,394 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 15:13:20,394 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 15:13:20,398 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 3 个图片文件
2025-07-28 15:13:20,399 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 15:13:20,400 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 15:13:20,402 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 15:13:20,404 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 15:13:20,405 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 15:13:20,411 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 15:13:20,412 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 15:13:20,422 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-28 15:13:20,428 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 15:13:20,430 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 15:13:20,438 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 15:13:20,441 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-28 15:13:20,444 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-28 15:13:20,947 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-28 15:13:20,949 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-28 15:13:21,026 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差28.30, 边缘比例0.0349
2025-07-28 15:13:21,033 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_151321.png
2025-07-28 15:13:21,034 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 15:13:21,035 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 15:13:21,036 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-28 15:13:21,037 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-28 15:13:21,037 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-28 15:13:21,042 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_151321.png
2025-07-28 15:13:21,043 - WeChatAutoAdd - INFO - 底部区域原始检测到 38 个轮廓
2025-07-28 15:13:21,045 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:13:21,047 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:13:21,048 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:13:21,050 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-07-28 15:13:21,052 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 15:13:21,055 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 15:13:21,057 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-07-28 15:13:21,058 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:13:21,059 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-07-28 15:13:21,060 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-07-28 15:13:21,061 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-07-28 15:13:21,062 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:13:21,063 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 15:13:21,064 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:13:21,065 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:13:21,067 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 15:13:21,071 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 15:13:21,073 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:13:21,075 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:13:21,076 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-07-28 15:13:21,077 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:13:21,078 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-07-28 15:13:21,079 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:13:21,080 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-07-28 15:13:21,088 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-07-28 15:13:21,089 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-07-28 15:13:21,090 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-07-28 15:13:21,092 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-07-28 15:13:21,094 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-07-28 15:13:21,095 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-07-28 15:13:21,097 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-07-28 15:13:21,098 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-07-28 15:13:21,102 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-07-28 15:13:21,104 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-07-28 15:13:21,106 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-07-28 15:13:21,109 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-07-28 15:13:21,111 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-07-28 15:13:21,113 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-28 15:13:21,120 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-07-28 15:13:21,126 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-07-28 15:13:21,126 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-28 15:13:21,128 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 15:13:21,140 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_151321.png
2025-07-28 15:13:21,141 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 15:13:21,143 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-07-28 15:13:21,146 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_151321.png
2025-07-28 15:13:21,195 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 15:13:21,196 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-07-28 15:13:21,197 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 15:13:21,198 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 15:13:21,501 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-07-28 15:13:22,284 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 15:13:22,290 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 15:13:22,293 - modules.wechat_auto_add_simple - INFO - ✅ 17707167456 添加朋友操作执行成功
2025-07-28 15:13:22,294 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 15:13:24,297 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-28 15:13:24,297 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-28 15:13:24,297 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-28 15:13:24,298 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 15:13:24,298 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 15:13:24,298 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 15:13:24,299 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 15:13:24,299 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 15:13:24,299 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-28 15:13:24,300 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 17707167456
2025-07-28 15:13:24,303 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-28 15:13:24,303 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:536 in _execute_auto_add_friend
2025-07-28 15:13:24,304 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:614 in _handle_friend_request_window
2025-07-28 15:13:24,305 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-28 15:13:24,305 - modules.friend_request_window - INFO -    📱 phone: '17707167456'
2025-07-28 15:13:24,306 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-28 15:13:24,306 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-28 15:13:24,893 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-28 15:13:24,894 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-28 15:13:24,894 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-28 15:13:24,894 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 15:13:24,896 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 17707167456
2025-07-28 15:13:24,896 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-28 15:13:24,897 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 15:13:24,899 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-28 15:13:24,900 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-28 15:13:24,900 - modules.friend_request_window - INFO -    📱 手机号码: 17707167456
2025-07-28 15:13:24,901 - modules.friend_request_window - INFO -    🆔 准考证: 015825120165
2025-07-28 15:13:24,902 - modules.friend_request_window - INFO -    👤 姓名: 李珂
2025-07-28 15:13:24,902 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 15:13:24,903 - modules.friend_request_window - INFO -    📝 备注格式: '015825120165-李珂-2025-07-28 15:13:24'
2025-07-28 15:13:24,903 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 15:13:24,904 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '015825120165-李珂-2025-07-28 15:13:24'
2025-07-28 15:13:24,904 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 15:13:24,907 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-28 15:13:24,907 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-28 15:13:24,910 - modules.wechat_auto_add_simple - INFO - ✅ 17707167456 申请添加朋友窗口处理完成: 申请添加朋友窗口处理失败: 未找到好友申请窗口
2025-07-28 15:13:24,910 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 17707167456
2025-07-28 15:13:28,604 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2995
2025-07-28 15:13:28,604 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13839615486 (李豪伟)
2025-07-28 15:13:35,239 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13839615486
2025-07-28 15:13:35,239 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 15:13:35,240 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13839615486 执行添加朋友操作...
2025-07-28 15:13:35,240 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 15:13:35,240 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 15:13:35,241 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 15:13:35,242 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 15:13:35,247 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 15:13:35,248 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 15:13:35,249 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 15:13:35,250 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 15:13:35,250 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 15:13:35,250 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 15:13:35,253 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 15:13:35,254 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 15:13:35,258 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-28 15:13:35,260 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 15:13:35,262 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 15:13:35,264 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 15:13:35,268 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-28 15:13:35,274 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-28 15:13:35,776 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-28 15:13:35,777 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-28 15:13:35,871 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-07-28 15:13:35,872 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-07-28 15:13:35,879 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_151335.png
2025-07-28 15:13:35,880 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 15:13:35,882 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 15:13:35,883 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-28 15:13:35,884 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-28 15:13:35,889 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-28 15:13:35,894 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_151335.png
2025-07-28 15:13:35,896 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-28 15:13:35,898 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-28 15:13:35,901 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-28 15:13:35,903 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-28 15:13:35,904 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-28 15:13:35,905 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-28 15:13:35,906 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-28 15:13:35,907 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 15:13:35,914 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_151335.png
2025-07-28 15:13:35,917 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 15:13:35,918 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-28 15:13:35,923 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_151335.png
2025-07-28 15:13:35,944 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 15:13:35,946 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-28 15:13:35,953 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 15:13:35,959 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 15:13:36,260 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-28 15:13:37,034 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 15:13:37,036 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 15:13:37,037 - modules.wechat_auto_add_simple - INFO - ✅ 13839615486 添加朋友操作执行成功
2025-07-28 15:13:37,037 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 15:13:39,039 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-28 15:13:39,039 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-28 15:13:39,040 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-28 15:13:39,040 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 15:13:39,040 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 15:13:39,040 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 15:13:39,041 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 15:13:39,041 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 15:13:39,041 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-28 15:13:39,042 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13839615486
2025-07-28 15:13:39,042 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-28 15:13:39,043 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:536 in _execute_auto_add_friend
2025-07-28 15:13:39,043 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:614 in _handle_friend_request_window
2025-07-28 15:13:39,043 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-28 15:13:39,044 - modules.friend_request_window - INFO -    📱 phone: '13839615486'
2025-07-28 15:13:39,044 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-28 15:13:39,044 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-28 15:13:39,633 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-28 15:13:39,633 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-28 15:13:39,634 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-28 15:13:39,634 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 15:13:39,635 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13839615486
2025-07-28 15:13:39,635 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-28 15:13:39,636 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 15:13:39,636 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-28 15:13:39,637 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-28 15:13:39,637 - modules.friend_request_window - INFO -    📱 手机号码: 13839615486
2025-07-28 15:13:39,637 - modules.friend_request_window - INFO -    🆔 准考证: 015825120167
2025-07-28 15:13:39,637 - modules.friend_request_window - INFO -    👤 姓名: 李豪伟
2025-07-28 15:13:39,638 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 15:13:39,638 - modules.friend_request_window - INFO -    📝 备注格式: '015825120167-李豪伟-2025-07-28 15:13:39'
2025-07-28 15:13:39,638 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 15:13:39,638 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '015825120167-李豪伟-2025-07-28 15:13:39'
2025-07-28 15:13:39,639 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 15:13:39,640 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 3345202, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-28 15:13:39,643 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 3345202)
2025-07-28 15:13:39,643 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-28 15:13:39,645 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-28 15:13:39,648 - modules.friend_request_window - INFO - 🔄 激活窗口: 3345202
2025-07-28 15:13:40,352 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-28 15:13:40,353 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-28 15:13:40,354 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-28 15:13:40,354 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:471 in _analyze_search_result
2025-07-28 15:13:40,355 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-28 15:13:40,355 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:536 in _execute_auto_add_friend
2025-07-28 15:13:40,355 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-28 15:13:40,356 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:614 in _handle_friend_request_window
2025-07-28 15:13:40,356 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-28 15:13:40,357 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1158 in execute_friend_request_flow
2025-07-28 15:13:40,357 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-28 15:13:40,357 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-28 15:13:40,358 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-28 15:13:40,358 - modules.friend_request_window - INFO -    📝 remark参数: '015825120167-李豪伟-2025-07-28 15:13:39' (类型: <class 'str'>, 长度: 36)
2025-07-28 15:13:40,359 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-28 15:13:40,360 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '015825120167-李豪伟-2025-07-28 15:13:39'
2025-07-28 15:13:40,360 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-28 15:13:40,360 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-28 15:13:40,361 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-28 15:13:40,361 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-28 15:13:40,362 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-28 15:13:40,362 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-28 15:13:40,362 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-28 15:13:41,283 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-28 15:13:46,524 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-28 15:13:46,525 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-28 15:13:46,525 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-28 15:13:46,525 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-28 15:13:46,528 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '点击错误提示确定按钮...' (前50字符)
2025-07-28 15:13:46,838 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 15:13:46,839 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 15:13:47,742 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 15:13:47,752 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 15:13:47,753 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-28 15:13:47,754 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-28 15:13:47,754 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-28 15:13:47,754 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-28 15:13:48,255 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-28 15:13:48,255 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-28 15:13:48,255 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-28 15:13:48,256 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-28 15:13:48,256 - modules.friend_request_window - INFO -    📝 内容: '015825120167-李豪伟-2025-07-28 15:13:39'
2025-07-28 15:13:48,256 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-28 15:13:48,256 - modules.friend_request_window - INFO -    🔤 内容编码: b'015825120167-\xe6\x9d\x8e\xe8\xb1\xaa\xe4\xbc\x9f-2025-07-28 15:13:39'
2025-07-28 15:13:48,257 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-28 15:13:49,165 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-28 15:13:54,407 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-28 15:13:54,408 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-28 15:13:54,408 - modules.friend_request_window - INFO -    📝 原始文本: '015825120167-李豪伟-2025-07-28 15:13:39'
2025-07-28 15:13:54,409 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-28 15:13:54,409 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '点击错误提示确定按钮...' (前50字符)
2025-07-28 15:13:54,723 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 15:13:54,723 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 15:13:55,626 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 15:13:55,633 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 15:13:55,634 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '015825120167-李豪伟-2025-07-28 15:13:39'
2025-07-28 15:13:55,635 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-28 15:13:55,635 - modules.friend_request_window - INFO -    📝 已填写内容: '015825120167-李豪伟-2025-07-28 15:13:39'
2025-07-28 15:13:55,635 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-28 15:13:56,136 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '015825120167-李豪伟-2025-07-28 15:13:39'
2025-07-28 15:13:56,136 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-28 15:13:56,137 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-28 15:13:56,137 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-28 15:13:56,137 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-28 15:13:56,137 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-28 15:13:56,138 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-28 15:13:56,938 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-28 15:13:56,939 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-28 15:13:56,939 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-28 15:13:57,553 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:13:57,553 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-28 15:13:57,554 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-28 15:13:57,554 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-28 15:13:58,056 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-28 15:13:58,058 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-28 15:13:58,058 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-28 15:13:58,059 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-28 15:13:58,059 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-28 15:13:58,059 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-28 15:13:58,059 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-28 15:13:58,060 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-28 15:13:58,060 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-28 15:13:58,060 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-28 15:13:58,060 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-28 15:13:58,061 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-28 15:13:58,061 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-28 15:13:58,061 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-28 15:13:58,062 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-28 15:13:58,062 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-28 15:13:58,062 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-28 15:13:58,063 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-28 15:13:58,065 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-28 15:13:58,566 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-28 15:13:58,567 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-28 15:13:58,567 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-28 15:13:58,567 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-28 15:13:58,568 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-28 15:13:58,568 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-28 15:13:58,568 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-28 15:13:58,568 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-28 15:13:59,483 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-28 15:13:59,483 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-28 15:13:59,484 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-28 15:13:59,484 - modules.friend_request_window - INFO - 🔄 步骤2: 关闭添加朋友窗口...
2025-07-28 15:13:59,484 - modules.friend_request_window - INFO - 🎯 使用指定坐标 (1504, 13) 关闭添加朋友窗口...
2025-07-28 15:13:59,985 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui点击坐标 (1504, 13)...
2025-07-28 15:14:01,099 - modules.friend_request_window - INFO - ✅ pyautogui点击添加朋友窗口关闭按钮成功
2025-07-28 15:14:01,099 - modules.friend_request_window - INFO - 🔄 步骤3: 关闭当前微信窗口...
2025-07-28 15:14:01,100 - modules.friend_request_window - INFO - 🎯 使用指定坐标 (700, 16) 关闭当前微信窗口...
2025-07-28 15:14:01,601 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui点击坐标 (700, 16)...
2025-07-28 15:14:02,715 - modules.friend_request_window - INFO - ✅ pyautogui点击当前微信窗口关闭按钮成功
2025-07-28 15:14:02,716 - modules.friend_request_window - INFO - 🔄 关闭错误相关窗口...
2025-07-28 15:14:02,738 - modules.friend_request_window - INFO - ✅ 频率错误处理完成
2025-07-28 15:14:02,739 - modules.friend_request_window - INFO - ✅ 原有频率错误处理成功，已切换到微信2
2025-07-28 15:14:02,739 - modules.friend_request_window - WARNING - ⚠️ 检测到并处理了频率错误，已切换到微信2
2025-07-28 15:14:02,739 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-28 15:14:02,740 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-28 15:14:02,741 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 15:14:02,741 - modules.friend_request_window - INFO -    📝 备注信息: '015825120167-李豪伟-2025-07-28 15:13:39'
2025-07-28 15:14:03,242 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-28 15:14:03,242 - modules.wechat_auto_add_simple - INFO - ✅ 13839615486 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-28 15:14:03,243 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 13839615486
2025-07-28 15:14:06,899 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 3/2995
2025-07-28 15:14:06,899 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15915374076 (赵婷婷)
2025-07-28 15:14:13,506 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15915374076
2025-07-28 15:14:13,507 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 15:14:13,507 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15915374076 执行添加朋友操作...
2025-07-28 15:14:13,507 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 15:14:13,507 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 15:14:13,508 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 15:14:13,509 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 15:14:13,514 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 15:14:13,520 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 15:14:13,521 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 15:14:13,525 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 15:14:13,526 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 15:14:13,526 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 15:14:13,529 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 15:14:13,532 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 15:14:13,537 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 15:14:13,540 - WeChatAutoAdd - DEBUG - 找到微信窗口: ● main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 15:14:13,541 - WeChatAutoAdd - INFO - 共找到 2 个微信窗口
2025-07-28 15:14:13,543 - WeChatAutoAdd - INFO - 找到微信主窗口: 微信
2025-07-28 15:14:14,051 - WeChatAutoAdd - INFO - 目标窗口: 微信
2025-07-28 15:14:14,055 - WeChatAutoAdd - INFO - 窗口位置: (0, 0), 尺寸: 726x650
2025-07-28 15:14:14,166 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差38.32, 边缘比例0.0619
2025-07-28 15:14:14,184 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_151414.png
2025-07-28 15:14:14,186 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 15:14:14,187 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 15:14:14,189 - WeChatAutoAdd - INFO - 截图尺寸: 726x650
2025-07-28 15:14:14,190 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=405-650 (高度:245)
2025-07-28 15:14:14,192 - WeChatAutoAdd - INFO - 特别关注区域: Y=466-526 (距底部154像素)
2025-07-28 15:14:14,202 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_151414.png
2025-07-28 15:14:14,204 - WeChatAutoAdd - INFO - 底部区域原始检测到 162 个轮廓
2025-07-28 15:14:14,205 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,649), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:14:14,206 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(75,642), 尺寸3x5, 长宽比0.60, 面积15
2025-07-28 15:14:14,206 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(83,641), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:14:14,207 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(78,640), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:14:14,209 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(218,639), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:14:14,212 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,639), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 15:14:14,218 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(210,639), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:14:14,220 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(106,638), 尺寸4x3, 长宽比1.33, 面积12
2025-07-28 15:14:14,220 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(97,638), 尺寸8x3, 长宽比2.67, 面积24
2025-07-28 15:14:14,221 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,638), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:14:14,222 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(91,637), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:14:14,226 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(83,636), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:14:14,230 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(247,635), 尺寸4x6, 长宽比0.67, 面积24
2025-07-28 15:14:14,234 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(242,631), 尺寸17x11, 长宽比1.55, 面积187
2025-07-28 15:14:14,235 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(233,631), 尺寸10x9, 长宽比1.11, 面积90
2025-07-28 15:14:14,237 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(231,631), 尺寸6x10, 长宽比0.60, 面积60
2025-07-28 15:14:14,238 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(80,631), 尺寸4x16, 长宽比0.25, 面积64
2025-07-28 15:14:14,239 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,630), 尺寸6x11, 长宽比0.55, 面积66
2025-07-28 15:14:14,241 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,630), 尺寸5x11, 长宽比0.45, 面积55
2025-07-28 15:14:14,242 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,630), 尺寸8x11, 长宽比0.73, 面积88
2025-07-28 15:14:14,243 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,630), 尺寸9x11, 长宽比0.82, 面积99
2025-07-28 15:14:14,250 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(185,629), 尺寸24x13, 长宽比1.85, 面积312
2025-07-28 15:14:14,252 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=84.3 (阈值:60)
2025-07-28 15:14:14,254 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,629), 尺寸15x13, 长宽比1.15, 面积195
2025-07-28 15:14:14,257 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=83.6 (阈值:60)
2025-07-28 15:14:14,261 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,629), 尺寸14x13, 长宽比1.08, 面积182
2025-07-28 15:14:14,265 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=78.1 (阈值:60)
2025-07-28 15:14:14,275 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,612), 尺寸17x1, 长宽比17.00, 面积17
2025-07-28 15:14:14,279 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,610), 尺寸18x1, 长宽比18.00, 面积18
2025-07-28 15:14:14,286 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,604), 尺寸18x3, 长宽比6.00, 面积54
2025-07-28 15:14:14,289 - WeChatAutoAdd - INFO - 重要轮廓: 位置(614,602), 尺寸96x32, 长宽比3.00, 已知特征:False
2025-07-28 15:14:14,291 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度96 (需要70-95)
2025-07-28 15:14:14,292 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,601), 尺寸18x1, 长宽比18.00, 面积18
2025-07-28 15:14:14,300 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,599), 尺寸17x1, 长宽比17.00, 面积17
2025-07-28 15:14:14,305 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(200,594), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:14:14,307 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(188,593), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:14:14,309 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,592), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:14:14,316 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(245,592), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 15:14:14,319 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(243,592), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:14:14,321 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(241,592), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:14:14,324 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(124,592), 尺寸4x4, 长宽比1.00, 面积16
2025-07-28 15:14:14,326 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,591), 尺寸4x3, 长宽比1.33, 面积12
2025-07-28 15:14:14,331 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,590), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 15:14:14,337 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(181,589), 尺寸1x5, 长宽比0.20, 面积5
2025-07-28 15:14:14,341 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(127,589), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:14:14,342 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(226,588), 尺寸4x6, 长宽比0.67, 面积24
2025-07-28 15:14:14,343 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(224,588), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 15:14:14,346 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,588), 尺寸3x1, 长宽比3.00, 面积3
2025-07-28 15:14:14,351 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(146,588), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 15:14:14,353 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(143,588), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:14:14,354 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(191,587), 尺寸6x8, 长宽比0.75, 面积48
2025-07-28 15:14:14,357 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(179,585), 尺寸11x9, 长宽比1.22, 面积99
2025-07-28 15:14:14,359 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(147,585), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 15:14:14,360 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(198,584), 尺寸27x11, 长宽比2.45, 面积297
2025-07-28 15:14:14,368 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(122,584), 尺寸45x12, 长宽比3.75, 面积540
2025-07-28 15:14:14,370 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,584), 尺寸4x11, 长宽比0.36, 面积44
2025-07-28 15:14:14,374 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,583), 尺寸11x13, 长宽比0.85, 面积143
2025-07-28 15:14:14,375 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(153,583), 尺寸8x11, 长宽比0.73, 面积88
2025-07-28 15:14:14,376 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(147,583), 尺寸7x3, 长宽比2.33, 面积21
2025-07-28 15:14:14,383 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(143,583), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 15:14:14,385 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,583), 尺寸13x11, 长宽比1.18, 面积143
2025-07-28 15:14:14,387 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(221,571), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:14:14,389 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(216,571), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 15:14:14,391 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,570), 尺寸7x4, 长宽比1.75, 面积28
2025-07-28 15:14:14,392 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(126,570), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 15:14:14,393 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(205,568), 尺寸2x5, 长宽比0.40, 面积10
2025-07-28 15:14:14,400 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(133,568), 尺寸2x5, 长宽比0.40, 面积10
2025-07-28 15:14:14,403 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(171,565), 尺寸4x4, 长宽比1.00, 面积16
2025-07-28 15:14:14,404 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(254,564), 尺寸4x8, 长宽比0.50, 面积32
2025-07-28 15:14:14,405 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(206,564), 尺寸8x10, 长宽比0.80, 面积80
2025-07-28 15:14:14,406 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,564), 尺寸6x10, 长宽比0.60, 面积60
2025-07-28 15:14:14,407 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(242,563), 尺寸11x11, 长宽比1.00, 面积121
2025-07-28 15:14:14,410 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(233,563), 尺寸10x9, 长宽比1.11, 面积90
2025-07-28 15:14:14,415 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(231,563), 尺寸6x10, 长宽比0.60, 面积60
2025-07-28 15:14:14,417 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(141,563), 尺寸10x11, 长宽比0.91, 面积110
2025-07-28 15:14:14,422 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(199,562), 尺寸6x11, 长宽比0.55, 面积66
2025-07-28 15:14:14,423 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(196,562), 尺寸5x6, 长宽比0.83, 面积30
2025-07-28 15:14:14,427 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(191,562), 尺寸7x11, 长宽比0.64, 面积77
2025-07-28 15:14:14,432 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(151,562), 尺寸28x11, 长宽比2.55, 面积308
2025-07-28 15:14:14,435 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=78.7 (阈值:60)
2025-07-28 15:14:14,436 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(120,561), 尺寸12x12, 长宽比1.00, 面积144
2025-07-28 15:14:14,437 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=78.6 (阈值:60)
2025-07-28 15:14:14,439 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,559), 尺寸38x38, 长宽比1.00, 面积1444
2025-07-28 15:14:14,441 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=71.2 (阈值:60)
2025-07-28 15:14:14,442 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,547), 尺寸16x22, 长宽比0.73, 面积352
2025-07-28 15:14:14,443 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(161,526), 尺寸4x1, 长宽比4.00, 面积4
2025-07-28 15:14:14,451 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,524), 尺寸4x2, 长宽比2.00, 面积8
2025-07-28 15:14:14,452 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,523), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:14:14,454 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,522), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 15:14:14,457 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(161,522), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 15:14:14,459 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,521), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 15:14:14,460 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(155,521), 尺寸5x7, 长宽比0.71, 面积35
2025-07-28 15:14:14,462 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(143,521), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:14:14,468 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(146,520), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:14:14,485 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(179,519), 尺寸10x7, 长宽比1.43, 面积70
2025-07-28 15:14:14,487 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(162,519), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 15:14:14,489 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(196,518), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:14:14,493 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,518), 尺寸1x5, 长宽比0.20, 面积5
2025-07-28 15:14:14,501 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,518), 尺寸4x2, 长宽比2.00, 面积8
2025-07-28 15:14:14,505 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(150,518), 尺寸1x9, 长宽比0.11, 面积9
2025-07-28 15:14:14,509 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(180,516), 尺寸10x2, 长宽比5.00, 面积20
2025-07-28 15:14:14,516 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(155,516), 尺寸29x12, 长宽比2.42, 面积348
2025-07-28 15:14:14,518 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=516 (距底部154像素区域)
2025-07-28 15:14:14,522 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 15:14:14,524 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,516), 尺寸4x13, 长宽比0.31, 面积52
2025-07-28 15:14:14,525 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(185,515), 尺寸42x13, 长宽比3.23, 面积546
2025-07-28 15:14:14,526 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=515 (距底部154像素区域)
2025-07-28 15:14:14,530 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 15:14:14,536 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(123,515), 尺寸26x14, 长宽比1.86, 面积364
2025-07-28 15:14:14,538 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=515 (距底部154像素区域)
2025-07-28 15:14:14,539 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 15:14:14,541 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,505), 尺寸8x1, 长宽比8.00, 面积8
2025-07-28 15:14:14,542 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,503), 尺寸9x1, 长宽比9.00, 面积9
2025-07-28 15:14:14,543 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,502), 尺寸8x4, 长宽比2.00, 面积32
2025-07-28 15:14:14,546 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(139,502), 尺寸6x2, 长宽比3.00, 面积12
2025-07-28 15:14:14,552 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,499), 尺寸8x3, 长宽比2.67, 面积24
2025-07-28 15:14:14,553 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(163,498), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:14:14,554 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(154,498), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:14:14,556 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,497), 尺寸1x4, 长宽比0.25, 面积4
2025-07-28 15:14:14,558 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(229,495), 尺寸29x11, 长宽比2.64, 面积319
2025-07-28 15:14:14,559 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=495 (距底部154像素区域)
2025-07-28 15:14:14,561 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 15:14:14,565 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(163,495), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 15:14:14,567 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,495), 尺寸10x10, 长宽比1.00, 面积100
2025-07-28 15:14:14,569 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=495 (距底部154像素区域)
2025-07-28 15:14:14,571 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 15:14:14,572 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(170,494), 尺寸5x11, 长宽比0.45, 面积55
2025-07-28 15:14:14,573 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(168,494), 尺寸1x7, 长宽比0.14, 面积7
2025-07-28 15:14:14,575 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(161,493), 尺寸6x13, 长宽比0.46, 面积78
2025-07-28 15:14:14,576 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,493), 尺寸10x1, 长宽比10.00, 面积10
2025-07-28 15:14:14,577 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,493), 尺寸41x14, 长宽比2.93, 面积574
2025-07-28 15:14:14,584 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=493 (距底部154像素区域)
2025-07-28 15:14:14,585 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 15:14:14,587 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,491), 尺寸37x37, 长宽比1.00, 面积1369
2025-07-28 15:14:14,588 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=491 (距底部154像素区域)
2025-07-28 15:14:14,590 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 15:14:14,591 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,456), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:14:14,592 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(247,456), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:14:14,593 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(244,456), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 15:14:14,602 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(203,456), 尺寸9x3, 长宽比3.00, 面积27
2025-07-28 15:14:14,604 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(208,454), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 15:14:14,605 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(203,454), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:14:14,606 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(175,454), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:14:14,608 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,454), 尺寸10x8, 长宽比1.25, 面积80
2025-07-28 15:14:14,609 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(201,453), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:14:14,610 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(158,453), 尺寸4x5, 长宽比0.80, 面积20
2025-07-28 15:14:14,616 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,452), 尺寸4x4, 长宽比1.00, 面积16
2025-07-28 15:14:14,617 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,452), 尺寸7x4, 长宽比1.75, 面积28
2025-07-28 15:14:14,619 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,452), 尺寸8x7, 长宽比1.14, 面积56
2025-07-28 15:14:14,621 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,452), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:14:14,623 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,452), 尺寸1x3, 长宽比0.33, 面积3
2025-07-28 15:14:14,625 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,452), 尺寸6x10, 长宽比0.60, 面积60
2025-07-28 15:14:14,626 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,451), 尺寸5x4, 长宽比1.25, 面积20
2025-07-28 15:14:14,627 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(159,451), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:14:14,633 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,451), 尺寸5x4, 长宽比1.25, 面积20
2025-07-28 15:14:14,635 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(132,451), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:14:14,636 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(123,451), 尺寸8x7, 长宽比1.14, 面积56
2025-07-28 15:14:14,638 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(171,450), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:14:14,639 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(230,449), 尺寸13x11, 长宽比1.18, 面积143
2025-07-28 15:14:14,641 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,449), 尺寸5x1, 长宽比5.00, 面积5
2025-07-28 15:14:14,642 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,449), 尺寸23x11, 长宽比2.09, 面积253
2025-07-28 15:14:14,643 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,448), 尺寸17x12, 长宽比1.42, 面积204
2025-07-28 15:14:14,650 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(173,448), 尺寸10x8, 长宽比1.25, 面积80
2025-07-28 15:14:14,653 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,447), 尺寸19x13, 长宽比1.46, 面积247
2025-07-28 15:14:14,655 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,447), 尺寸11x13, 长宽比0.85, 面积143
2025-07-28 15:14:14,657 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(159,447), 尺寸13x3, 长宽比4.33, 面积39
2025-07-28 15:14:14,658 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(218,435), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:14:14,660 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,435), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 15:14:14,662 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(152,435), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 15:14:14,668 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(253,431), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 15:14:14,670 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(186,429), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:14:14,675 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(229,427), 尺寸30x11, 长宽比2.73, 面积330
2025-07-28 15:14:14,677 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(186,427), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:14:14,684 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(188,426), 尺寸3x11, 长宽比0.27, 面积33
2025-07-28 15:14:14,686 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,426), 尺寸6x11, 长宽比0.55, 面积66
2025-07-28 15:14:14,689 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,426), 尺寸5x11, 长宽比0.45, 面积55
2025-07-28 15:14:14,691 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,426), 尺寸17x11, 长宽比1.55, 面积187
2025-07-28 15:14:14,694 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=87.1 (阈值:60)
2025-07-28 15:14:14,700 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(202,425), 尺寸9x13, 长宽比0.69, 面积117
2025-07-28 15:14:14,703 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,425), 尺寸7x13, 长宽比0.54, 面积91
2025-07-28 15:14:14,705 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(153,425), 尺寸15x13, 长宽比1.15, 面积195
2025-07-28 15:14:14,708 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=88.5 (阈值:60)
2025-07-28 15:14:14,710 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,425), 尺寸14x13, 长宽比1.08, 面积182
2025-07-28 15:14:14,716 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=78.1 (阈值:60)
2025-07-28 15:14:14,717 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,423), 尺寸38x38, 长宽比1.00, 面积1444
2025-07-28 15:14:14,719 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=71.2 (阈值:60)
2025-07-28 15:14:14,722 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,405), 尺寸725x245, 长宽比2.96, 面积177625
2025-07-28 15:14:14,723 - WeChatAutoAdd - INFO - 底部区域找到 23 个按钮候选
2025-07-28 15:14:14,724 - WeChatAutoAdd - INFO - 选择特殊位置按钮: Y=491 (距底部154像素)
2025-07-28 15:14:14,725 - WeChatAutoAdd - INFO - 在底部找到按钮: (90, 509), 尺寸: 37x37, 位置得分: 3.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-07-28 15:14:14,728 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 15:14:14,754 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_151414.png
2025-07-28 15:14:14,756 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 15:14:14,757 - WeChatAutoAdd - INFO - 开始验证按钮区域(90, 509)是否包含'添加到通讯录'文字
2025-07-28 15:14:14,760 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_151414.png
2025-07-28 15:14:14,787 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 15:14:14,791 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0508, 平均亮度=227.0, 亮度标准差=45.1
2025-07-28 15:14:14,795 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 15:14:14,798 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 15:14:15,103 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(90, 509) -> 屏幕坐标(90, 509)
2025-07-28 15:14:15,880 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 15:14:15,881 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 15:14:15,882 - modules.wechat_auto_add_simple - INFO - ✅ 15915374076 添加朋友操作执行成功
2025-07-28 15:14:15,882 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
