#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多窗口频率错误处理流程修复

作者：AI助手
创建时间：2025-01-28
"""

import sys
import logging
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

def test_multi_window_frequency_fix():
    """测试多窗口频率错误处理流程修复"""
    logger = setup_logging()
    logger.info("🧪 开始测试多窗口频率错误处理流程修复...")
    
    try:
        # 1. 测试导入
        logger.info("📦 测试模块导入...")
        from main_controller import WeChatMainController
        from modules.frequency_error_handler import FrequencyErrorHandler
        logger.info("✅ 模块导入成功")
        
        # 2. 模拟多窗口环境
        logger.info("🔧 模拟多窗口环境...")
        mock_windows = [
            {"hwnd": 12345, "title": "微信窗口1", "index": 0},
            {"hwnd": 12346, "title": "微信窗口2", "index": 1},
            {"hwnd": 12347, "title": "微信窗口3", "index": 2}
        ]
        
        mock_contacts = [
            {"phone": "13800138001", "name": "联系人1"},
            {"phone": "13800138002", "name": "联系人2"},
            {"phone": "13800138003", "name": "联系人3"}
        ]
        
        logger.info(f"📋 模拟窗口数量: {len(mock_windows)}")
        logger.info(f"📋 模拟联系人数量: {len(mock_contacts)}")
        
        # 3. 测试修复前的问题场景
        logger.info("🔍 测试修复前的问题场景...")
        logger.info("📋 问题场景描述:")
        logger.info("  1. 窗口1检测到频率错误")
        logger.info("  2. 执行关闭微信窗口操作")
        logger.info("  3. 系统没有正确切换到窗口2")
        logger.info("  4. 系统没有在窗口2上重新开始6步骤流程")
        
        # 4. 测试修复后的正确流程
        logger.info("🔍 测试修复后的正确流程...")
        
        # 模拟频率错误处理流程
        logger.info("⚠️ 模拟窗口1检测到频率错误...")
        
        # 模拟 execute_multi_window_flow 的逻辑
        window_index = 0  # 当前处理窗口1
        current_window = mock_windows[window_index]
        
        logger.info(f"🎯 当前处理第 {window_index + 1}/{len(mock_windows)} 个微信窗口")
        logger.info(f"📋 窗口信息: {current_window['title']} (句柄: {current_window['hwnd']})")
        
        # 模拟 execute_single_window_flow 返回 RESTART_REQUIRED
        mock_result = "RESTART_REQUIRED"
        logger.info(f"📊 模拟单窗口流程返回: {mock_result}")
        
        # 测试修复后的处理逻辑
        if mock_result == "RESTART_REQUIRED":
            logger.info(f"🔄 第 {window_index + 1} 个微信窗口检测到频率错误")
            logger.info("🚪 频率错误处理完成，当前窗口已关闭")
            logger.info("🔄 根据频率错误处理机制，切换到下一个微信窗口")
            
            # 标记当前窗口为频率错误状态
            logger.info(f"📋 标记窗口{window_index + 1}为频率错误状态")
            
            # 切换到下一个窗口
            window_index += 1
            if window_index < len(mock_windows):
                next_window = mock_windows[window_index]
                logger.info(f"🔄 准备激活第 {window_index + 1} 个微信窗口")
                logger.info(f"📋 下一个窗口信息: {next_window['title']} (句柄: {next_window['hwnd']})")
                logger.info("🔄 将在新窗口上从第一步（窗口管理）重新开始完整的6步骤流程")
                
                # 模拟窗口切换延迟
                logger.info("⏳ 窗口切换延迟...")
                time.sleep(1)  # 测试时缩短延迟
                
                logger.info("✅ 窗口切换完成，准备在新窗口上执行6步骤流程")
            else:
                logger.warning("⚠️ 已是最后一个窗口，无法切换到下一个窗口")
        
        # 5. 测试6步骤流程重新开始
        logger.info("🔍 测试6步骤流程重新开始...")
        
        expected_steps = [
            "步骤1：窗口管理 - 激活窗口2",
            "步骤2：主界面操作 - 在窗口2上执行",
            "步骤3：简单添加 - 在窗口2上执行",
            "步骤4：图像识别 - 在窗口2上执行",
            "步骤5：好友申请 - 在窗口2上执行",
            "步骤6：频率处理 - 在窗口2上执行"
        ]
        
        logger.info("📋 预期的6步骤流程:")
        for i, step in enumerate(expected_steps, 1):
            logger.info(f"   {i}. {step}")
        
        # 6. 测试关键改进点
        logger.info("🔍 测试关键改进点...")
        
        improvements = [
            "✅ 检测到RESTART_REQUIRED时不再重新处理当前窗口",
            "✅ 正确切换到下一个微信窗口",
            "✅ 在新窗口上从第一步重新开始",
            "✅ 标记频率错误窗口状态",
            "✅ 添加窗口切换延迟",
            "✅ 提供详细的日志信息",
            "✅ 处理最后一个窗口的边界情况"
        ]
        
        logger.info("📋 关键改进点:")
        for improvement in improvements:
            logger.info(f"   {improvement}")
        
        # 7. 测试边界情况
        logger.info("🔍 测试边界情况...")
        
        # 测试最后一个窗口的频率错误处理
        logger.info("📋 测试场景：最后一个窗口检测到频率错误")
        last_window_index = len(mock_windows) - 1
        logger.info(f"🎯 当前处理第 {last_window_index + 1}/{len(mock_windows)} 个微信窗口（最后一个）")
        
        if mock_result == "RESTART_REQUIRED":
            logger.info(f"🔄 第 {last_window_index + 1} 个微信窗口检测到频率错误")
            
            # 切换到下一个窗口
            next_index = last_window_index + 1
            if next_index < len(mock_windows):
                logger.info(f"🔄 准备激活第 {next_index + 1} 个微信窗口")
            else:
                logger.warning("⚠️ 已是最后一个窗口，无法切换到下一个窗口")
                logger.info("📋 这种情况下，多窗口处理流程将结束")
        
        # 8. 验证修复效果
        logger.info("🔍 验证修复效果...")
        
        before_after_comparison = [
            ("修复前", "窗口1频率错误 → 重新处理窗口1 → 无限循环"),
            ("修复后", "窗口1频率错误 → 切换到窗口2 → 在窗口2重新开始6步骤")
        ]
        
        logger.info("📋 修复前后对比:")
        for status, flow in before_after_comparison:
            logger.info(f"   {status}: {flow}")
        
        logger.info("🎉 多窗口频率错误处理流程修复测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生异常: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """主函数"""
    logger = setup_logging()
    logger.info("=" * 70)
    logger.info("多窗口频率错误处理流程修复测试")
    logger.info("=" * 70)
    
    success = test_multi_window_frequency_fix()
    
    if success:
        logger.info("✅ 测试通过：多窗口频率错误处理流程修复成功")
        logger.info("📋 修复说明：")
        logger.info("  - 窗口1检测到频率错误后正确切换到窗口2")
        logger.info("  - 在窗口2上从第一步重新开始完整的6步骤流程")
        logger.info("  - 不再在同一窗口上重新开始处理")
        logger.info("  - 正确处理最后一个窗口的边界情况")
        logger.info("  - 提供详细的窗口切换日志信息")
    else:
        logger.error("❌ 测试失败：多窗口频率错误处理流程修复有问题")
    
    return success

if __name__ == "__main__":
    main()
