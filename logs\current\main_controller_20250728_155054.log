2025-07-28 15:50:54,637 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 15:50:54,638 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 15:50:54,638 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-28 15:50:54,639 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 15:50:54,639 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-28 15:50:54,640 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 15:50:54,640 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 15:50:54,641 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 15:50:54,642 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 15:50:54,644 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 15:50:54,646 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:50:54,654 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 15:50:54,659 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250728_155054.log
2025-07-28 15:50:54,660 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-28 15:50:54,662 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-28 15:50:54,663 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-28 15:50:54,666 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-28 15:50:54,667 - __main__ - INFO - 📅 当前北京时间: 2025-07-28 15:50:54
2025-07-28 15:50:54,668 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-28 15:50:54,671 - __main__ - INFO - 📅 启动时间: 2025-07-28 15:50:54
2025-07-28 15:50:54,672 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-28 15:50:54,673 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 15:50:55,224 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 15:50:55,224 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 15:50:55,768 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 15:50:55,769 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 15:50:55,772 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-28 15:50:55,772 - __main__ - INFO - ✅ 找到 2 个微信窗口
2025-07-28 15:50:55,773 - __main__ - INFO -   窗口 1: 微信 (句柄: 197994)
2025-07-28 15:50:55,776 - __main__ - INFO -   窗口 2: 微信 (句柄: 525668)
2025-07-28 15:50:55,777 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-28 15:50:57,199 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-28 15:50:57,199 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-28 15:50:57,200 - __main__ - INFO - 📋 待处理联系人数: 2983
2025-07-28 15:50:57,201 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-28 15:50:57,201 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2983
2025-07-28 15:50:57,202 - __main__ - INFO - 
============================================================
2025-07-28 15:50:57,202 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口
2025-07-28 15:50:57,203 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-28 15:50:57,203 - __main__ - INFO - ============================================================
2025-07-28 15:50:57,204 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-28 15:50:57,204 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-28 15:50:57,205 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-28 15:50:57,205 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-28 15:50:57,207 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 15:50:57,511 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 15:50:57,512 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 15:50:57,512 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 15:50:57,512 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 15:50:57,513 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 15:50:57,513 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 15:50:57,514 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 15:50:57,515 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 15:50:57,516 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 15:50:57,516 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 15:50:57,718 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 15:50:57,725 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 15:50:57,726 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-28 15:50:57,728 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 15:50:58,047 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 15:50:58,051 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 15:50:58,053 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 15:50:58,054 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 15:50:58,055 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 15:50:58,062 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 15:50:58,068 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 15:50:58,070 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 15:50:58,071 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 15:50:58,071 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 15:50:58,273 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 15:50:58,273 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 15:50:58,274 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-28 15:50:58,575 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 15:50:58,575 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 15:50:58,576 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 15:50:58,576 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 15:50:58,576 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 15:50:58,577 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 15:50:58,577 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-28 15:50:59,578 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-28 15:50:59,578 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-28 15:50:59,578 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-28 15:50:59,579 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-28 15:50:59,579 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-28 15:50:59,579 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-28 15:50:59,580 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-28 15:50:59,580 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-28 15:50:59,781 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-28 15:50:59,782 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-28 15:51:02,162 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-28 15:51:02,162 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-28 15:51:02,163 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.6 秒...
2025-07-28 15:51:04,800 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-28 15:51:05,002 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-28 15:51:05,002 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-28 15:51:07,395 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-28 15:51:07,395 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-28 15:51:07,396 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 3.0 秒...
2025-07-28 15:51:10,391 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-28 15:51:10,592 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-28 15:51:10,593 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-28 15:51:12,978 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-28 15:51:12,978 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-28 15:51:12,979 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.4 秒...
2025-07-28 15:51:15,404 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-28 15:51:15,627 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-28 15:51:15,627 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-28 15:51:18,012 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-28 15:51:18,012 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-28 15:51:18,013 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.8 秒...
2025-07-28 15:51:20,798 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-28 15:51:20,999 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-28 15:51:21,000 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-28 15:51:23,394 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-28 15:51:23,395 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-28 15:51:23,395 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-28 15:51:23,395 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-28 15:51:23,396 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 15:51:23,398 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 15:51:23,399 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 15:51:23,400 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 15:51:23,400 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 15:51:23,402 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1838020, 进程: Weixin.exe)
2025-07-28 15:51:23,405 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-28 15:51:23,405 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 1838020)
2025-07-28 15:51:23,406 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1838020) - 增强版
2025-07-28 15:51:23,710 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 15:51:23,711 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 15:51:23,711 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-28 15:51:23,712 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-28 15:51:23,712 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-28 15:51:23,712 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-28 15:51:23,916 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-28 15:51:23,917 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-28 15:51:24,118 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 15:51:24,119 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 15:51:24,119 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-28 15:51:24,119 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-28 15:51:24,120 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-28 15:51:24,120 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-28 15:51:24,120 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-28 15:51:25,121 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-28 15:51:25,121 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 15:51:25,123 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 15:51:25,124 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 15:51:25,125 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 15:51:25,126 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 15:51:25,127 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1838020, 进程: Weixin.exe)
2025-07-28 15:51:25,129 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-28 15:51:25,130 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-28 15:51:25,130 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-28 15:51:25,131 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-28 15:51:25,131 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-28 15:51:25,131 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 15:51:25,438 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 15:51:25,438 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 15:51:25,439 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 15:51:25,439 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 15:51:25,439 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 15:51:25,439 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 15:51:25,440 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 15:51:25,440 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 15:51:25,440 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 15:51:25,441 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 15:51:25,642 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 15:51:25,643 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 15:51:25,644 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-28 15:51:25,945 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 15:51:25,946 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-28 15:51:26,946 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-28 15:51:26,947 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-28 15:51:26,947 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-28 15:51:26,949 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250728_155126.log
2025-07-28 15:51:26,950 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-28 15:51:26,950 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-28 15:51:26,950 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-28 15:51:26,951 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-28 15:51:26,952 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-28 15:51:26,953 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-28 15:51:26,953 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-28 15:51:26,953 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-28 15:51:26,954 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-28 15:51:26,954 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-28 15:51:26,955 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-28 15:51:26,955 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 15:51:26,956 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 1838020
2025-07-28 15:51:26,957 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1838020) - 增强版
2025-07-28 15:51:27,265 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 15:51:27,265 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 15:51:27,266 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-28 15:51:27,266 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-28 15:51:27,267 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-28 15:51:27,267 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-28 15:51:27,268 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-28 15:51:27,268 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-28 15:51:27,469 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 15:51:27,470 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 15:51:27,474 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 1838020 (API返回: None)
2025-07-28 15:51:27,776 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 15:51:27,777 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-28 15:51:27,777 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-28 15:51:27,778 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-28 15:51:27,779 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 15:51:27,779 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-28 15:51:27,779 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-28 15:51:27,784 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-28 15:51:27,787 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-28 15:51:28,392 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-28 15:51:28,393 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 15:51:28,706 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2983 个
2025-07-28 15:51:28,707 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2983 个 (总计: 3135 个)
2025-07-28 15:51:28,707 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:51:28,707 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-28 15:51:28,708 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2983
2025-07-28 15:51:28,708 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 17630939086 (徐菲)
2025-07-28 15:51:28,708 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:51:35,306 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 17630939086
2025-07-28 15:51:35,306 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 15:51:35,306 - modules.wechat_auto_add_simple - INFO - 👥 开始为 17630939086 执行添加朋友操作...
2025-07-28 15:51:35,307 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 15:51:35,307 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 15:51:35,308 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 15:51:35,309 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 15:51:35,313 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 15:51:35,313 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 15:51:35,315 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 15:51:35,315 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 15:51:35,316 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 15:51:35,316 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 15:51:35,317 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 15:51:35,317 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 15:51:35,321 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-28 15:51:35,323 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 15:51:35,324 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 15:51:35,325 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 15:51:35,333 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-28 15:51:35,338 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-28 15:51:35,840 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-28 15:51:35,841 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-28 15:51:35,924 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-07-28 15:51:35,927 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-07-28 15:51:35,936 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_155135.png
2025-07-28 15:51:35,938 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 15:51:35,941 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 15:51:35,944 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-28 15:51:35,946 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-28 15:51:35,946 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-28 15:51:35,955 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_155135.png
2025-07-28 15:51:35,957 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-28 15:51:35,962 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-28 15:51:35,964 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-28 15:51:35,965 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-28 15:51:35,969 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-28 15:51:35,971 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-28 15:51:35,972 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-28 15:51:35,976 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 15:51:35,985 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_155135.png
2025-07-28 15:51:35,989 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 15:51:35,993 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-28 15:51:35,999 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_155135.png
2025-07-28 15:51:36,066 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 15:51:36,086 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-28 15:51:36,165 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 15:51:36,175 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 15:51:36,480 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-28 15:51:37,276 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 15:51:37,277 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 15:51:37,278 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:51:37,278 - modules.wechat_auto_add_simple - INFO - ✅ 17630939086 添加朋友操作执行成功
2025-07-28 15:51:37,279 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:51:37,279 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 15:51:39,280 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-28 15:51:39,281 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-28 15:51:39,282 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-28 15:51:39,282 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 15:51:39,282 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 15:51:39,283 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 15:51:39,284 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 15:51:39,284 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 15:51:39,285 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-28 15:51:39,285 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 17630939086
2025-07-28 15:51:39,294 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-28 15:51:39,294 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 15:51:39,295 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:684 in _handle_friend_request_window
2025-07-28 15:51:39,296 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-28 15:51:39,298 - modules.friend_request_window - INFO -    📱 phone: '17630939086'
2025-07-28 15:51:39,298 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-28 15:51:39,298 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-28 15:51:39,859 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-28 15:51:39,860 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-28 15:51:39,860 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-28 15:51:39,861 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 15:51:39,863 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 17630939086
2025-07-28 15:51:39,864 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-28 15:51:39,864 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 15:51:39,866 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-28 15:51:39,866 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-28 15:51:39,867 - modules.friend_request_window - INFO -    📱 手机号码: 17630939086
2025-07-28 15:51:39,867 - modules.friend_request_window - INFO -    🆔 准考证: 015825120186
2025-07-28 15:51:39,867 - modules.friend_request_window - INFO -    👤 姓名: 徐菲
2025-07-28 15:51:39,867 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 15:51:39,868 - modules.friend_request_window - INFO -    📝 备注格式: '015825120186-徐菲-2025-07-28 15:51:39'
2025-07-28 15:51:39,868 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 15:51:39,868 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '015825120186-徐菲-2025-07-28 15:51:39'
2025-07-28 15:51:39,868 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 15:51:39,870 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 2231354, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-28 15:51:39,871 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 2231354)
2025-07-28 15:51:39,871 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-28 15:51:39,871 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-28 15:51:39,873 - modules.friend_request_window - INFO - 🔄 激活窗口: 2231354
2025-07-28 15:51:40,577 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-28 15:51:40,579 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-28 15:51:40,580 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-28 15:51:40,584 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:499 in _analyze_search_result
2025-07-28 15:51:40,584 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-28 15:51:40,585 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 15:51:40,586 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-28 15:51:40,586 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:684 in _handle_friend_request_window
2025-07-28 15:51:40,586 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-28 15:51:40,587 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1158 in execute_friend_request_flow
2025-07-28 15:51:40,587 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-28 15:51:40,587 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-28 15:51:40,587 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-28 15:51:40,588 - modules.friend_request_window - INFO -    📝 remark参数: '015825120186-徐菲-2025-07-28 15:51:39' (类型: <class 'str'>, 长度: 35)
2025-07-28 15:51:40,588 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-28 15:51:40,591 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '015825120186-徐菲-2025-07-28 15:51:39'
2025-07-28 15:51:40,592 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-28 15:51:40,595 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-28 15:51:40,595 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-28 15:51:40,595 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-28 15:51:40,596 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-28 15:51:40,596 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-28 15:51:40,596 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-28 15:51:41,511 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-28 15:51:46,754 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-28 15:51:46,754 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-28 15:51:46,754 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-28 15:51:46,755 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-28 15:51:46,757 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '窗口1检测到频率错误并处理完成后

应该切换到窗口2

在窗口2上从第一步重新开始6步骤流程...' (前50字符)
2025-07-28 15:51:47,068 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 15:51:47,069 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 15:51:47,974 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 15:51:47,984 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 15:51:47,986 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-28 15:51:47,986 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-28 15:51:47,987 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-28 15:51:47,987 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-28 15:51:48,488 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-28 15:51:48,488 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-28 15:51:48,489 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-28 15:51:48,489 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-28 15:51:48,489 - modules.friend_request_window - INFO -    📝 内容: '015825120186-徐菲-2025-07-28 15:51:39'
2025-07-28 15:51:48,490 - modules.friend_request_window - INFO -    📏 内容长度: 35 字符
2025-07-28 15:51:48,490 - modules.friend_request_window - INFO -    🔤 内容编码: b'015825120186-\xe5\xbe\x90\xe8\x8f\xb2-2025-07-28 15:51:39'
2025-07-28 15:51:48,490 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-28 15:51:49,409 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-28 15:51:54,695 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-28 15:51:54,696 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-28 15:51:54,697 - modules.friend_request_window - INFO -    📝 原始文本: '015825120186-徐菲-2025-07-28 15:51:39'
2025-07-28 15:51:54,697 - modules.friend_request_window - INFO -    📏 文本长度: 35 字符
2025-07-28 15:51:54,697 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '窗口1检测到频率错误并处理完成后

应该切换到窗口2

在窗口2上从第一步重新开始6步骤流程...' (前50字符)
2025-07-28 15:51:55,006 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 15:51:55,006 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 15:51:55,909 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 15:51:55,919 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 15:51:55,919 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '015825120186-徐菲-2025-07-28 15:51:39'
2025-07-28 15:51:55,920 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-28 15:51:55,920 - modules.friend_request_window - INFO -    📝 已填写内容: '015825120186-徐菲-2025-07-28 15:51:39'
2025-07-28 15:51:55,921 - modules.friend_request_window - INFO -    � 内容长度: 35 字符
2025-07-28 15:51:56,425 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '015825120186-徐菲-2025-07-28 15:51:39'
2025-07-28 15:51:56,426 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-28 15:51:56,426 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-28 15:51:56,427 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-28 15:51:56,427 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-28 15:51:56,428 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-28 15:51:56,428 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-28 15:51:57,229 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-28 15:51:57,229 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-28 15:51:57,230 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-28 15:51:57,843 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:51:57,845 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-28 15:51:57,846 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-28 15:51:57,846 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-28 15:51:58,348 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-28 15:51:58,351 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-28 15:51:58,352 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-28 15:51:58,352 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-28 15:51:58,353 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-28 15:51:58,353 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-28 15:51:58,353 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-28 15:51:58,353 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-28 15:51:58,354 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-28 15:51:58,355 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-28 15:51:58,356 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-28 15:51:58,356 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-28 15:51:58,357 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-28 15:51:58,358 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-28 15:51:58,358 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-28 15:51:58,366 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-28 15:51:58,367 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-28 15:51:58,370 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-28 15:51:58,371 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-28 15:51:58,874 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-28 15:51:58,875 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-28 15:51:58,875 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-28 15:51:58,876 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-28 15:51:58,876 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-28 15:51:58,876 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-28 15:51:58,876 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-28 15:51:58,877 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-28 15:51:59,794 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-28 15:51:59,798 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-28 15:51:59,799 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-28 15:51:59,799 - modules.friend_request_window - INFO - 🔄 步骤2: 关闭添加朋友窗口...
2025-07-28 15:51:59,800 - modules.friend_request_window - INFO - 🎯 使用指定坐标 (1504, 13) 关闭添加朋友窗口...
2025-07-28 15:52:00,301 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui点击坐标 (1504, 13)...
2025-07-28 15:52:01,410 - modules.friend_request_window - INFO - ✅ pyautogui点击添加朋友窗口关闭按钮成功
2025-07-28 15:52:01,416 - modules.friend_request_window - INFO - 🔄 步骤3: 关闭当前微信窗口...
2025-07-28 15:52:01,417 - modules.friend_request_window - INFO - 🎯 使用指定坐标 (700, 16) 关闭当前微信窗口...
2025-07-28 15:52:01,918 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui点击坐标 (700, 16)...
2025-07-28 15:52:03,042 - modules.friend_request_window - INFO - ✅ pyautogui点击当前微信窗口关闭按钮成功
2025-07-28 15:52:03,043 - modules.friend_request_window - INFO - 🔄 关闭错误相关窗口...
2025-07-28 15:52:03,060 - modules.friend_request_window - INFO - ✅ 频率错误处理完成
2025-07-28 15:52:03,060 - modules.friend_request_window - INFO - 🔄 频率错误处理完成，需要重新开始流程
2025-07-28 15:52:03,061 - modules.friend_request_window - INFO - 🔄 已设置重新开始标志
2025-07-28 15:52:03,063 - modules.friend_request_window - INFO - ✅ 原有频率错误处理成功，已切换到微信2
2025-07-28 15:52:03,065 - modules.friend_request_window - WARNING - ⚠️ 检测到并处理了频率错误，已切换到微信2
2025-07-28 15:52:03,066 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-28 15:52:03,067 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-28 15:52:03,067 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 15:52:03,067 - modules.friend_request_window - INFO -    📝 备注信息: '015825120186-徐菲-2025-07-28 15:51:39'
2025-07-28 15:52:03,568 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-28 15:52:03,569 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:52:03,569 - modules.wechat_auto_add_simple - INFO - ✅ 17630939086 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-28 15:52:03,569 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 17630939086
2025-07-28 15:52:03,570 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:52:07,441 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2983
2025-07-28 15:52:07,442 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15098011006 (田祎)
2025-07-28 15:52:07,443 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:52:14,571 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15098011006
2025-07-28 15:52:14,572 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 15:52:14,573 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15098011006 执行添加朋友操作...
2025-07-28 15:52:14,574 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 15:52:14,575 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 15:52:14,576 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 15:52:14,584 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 15:52:14,591 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 15:52:14,595 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 15:52:14,595 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 15:52:14,596 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 15:52:14,596 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 15:52:14,596 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 15:52:14,597 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 15:52:14,597 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 15:52:14,606 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 15:52:14,612 - WeChatAutoAdd - DEBUG - 找到微信窗口: ● main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 15:52:14,615 - WeChatAutoAdd - INFO - 共找到 2 个微信窗口
2025-07-28 15:52:14,617 - WeChatAutoAdd - INFO - 找到微信主窗口: 微信
2025-07-28 15:52:14,619 - WeChatAutoAdd - WARNING - 方法1截图失败: Error code from Windows: 18 - 没有更多文件。, 尝试方法2
2025-07-28 15:52:14,708 - WeChatAutoAdd - INFO - 使用win32方法截取窗口: 
2025-07-28 15:52:14,711 - WeChatAutoAdd - INFO - win32窗口尺寸: 1x1, 位置: (0, 0)
2025-07-28 15:52:14,712 - WeChatAutoAdd - WARNING - win32 BitBlt操作可能失败
2025-07-28 15:52:14,713 - WeChatAutoAdd - WARNING - 截图可能为纯色图像，标准差: 0.0
2025-07-28 15:52:14,715 - WeChatAutoAdd - WARNING - win32截图内容验证失败
2025-07-28 15:52:14,721 - WeChatAutoAdd - INFO - 保存win32截图: screenshots\window_capture_win32_20250728_155214.png
2025-07-28 15:52:14,728 - WeChatAutoAdd - INFO - win32截图成功
2025-07-28 15:52:14,802 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 15:52:14,864 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 15:52:14,869 - WeChatAutoAdd - INFO - 截图尺寸: 1x1
2025-07-28 15:52:14,876 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=0-1 (高度:1)
2025-07-28 15:52:14,912 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_155214.png
2025-07-28 15:52:14,928 - WeChatAutoAdd - INFO - 底部区域原始检测到 0 个轮廓
2025-07-28 15:52:14,932 - WeChatAutoAdd - WARNING - 底部245像素区域未找到符合条件的按钮
2025-07-28 15:52:14,934 - WeChatAutoAdd - WARNING - 底部区域未找到按钮
2025-07-28 15:52:14,942 - WeChatAutoAdd - ERROR - 无法找到'添加到通讯录'按钮，结束当前流程
2025-07-28 15:52:14,948 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:52:14,949 - modules.wechat_auto_add_simple - ERROR - ❌ 15098011006 添加朋友操作执行失败
2025-07-28 15:52:14,950 - modules.wechat_auto_add_simple - WARNING - ⚠️ 添加朋友失败: 15098011006 - 添加朋友操作失败，可能原因：未找到'添加朋友'窗口或'添加到通讯录'按钮
2025-07-28 15:52:14,951 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:52:19,843 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 3/2983
2025-07-28 15:52:19,843 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13755762006 (李宇晴)
2025-07-28 15:52:19,844 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
