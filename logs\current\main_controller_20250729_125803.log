2025-07-29 12:58:03,439 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 12:58:03,440 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 12:58:03,440 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-29 12:58:03,441 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 12:58:03,442 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-29 12:58:03,442 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 12:58:03,442 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 12:58:03,443 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 12:58:03,443 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 12:58:03,444 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 12:58:03,450 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:58:03,453 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 12:58:03,456 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250729_125803.log
2025-07-29 12:58:03,457 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 12:58:03,458 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-29 12:58:03,458 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-29 12:58:03,459 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-29 12:58:03,460 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-29 12:58:03,461 - __main__ - INFO - 📅 当前北京时间: 2025-07-29 20:58:03
2025-07-29 12:58:03,462 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-29 12:58:03,462 - __main__ - INFO - 📅 启动时间: 2025-07-29 20:58:03
2025-07-29 12:58:03,465 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-29 12:58:03,466 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 12:58:04,003 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 12:58:04,003 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 12:58:04,537 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 12:58:04,538 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 12:58:04,540 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 12:58:04,540 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 1901038, 进程: Weixin.exe)
2025-07-29 12:58:04,543 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-29 12:58:04,543 - __main__ - INFO - ✅ 找到 3 个微信窗口
2025-07-29 12:58:04,544 - __main__ - INFO -   窗口 1: 微信 (句柄: 197994)
2025-07-29 12:58:04,546 - __main__ - INFO -   窗口 2: 微信 (句柄: 525668)
2025-07-29 12:58:04,547 - __main__ - INFO -   窗口 3: 微信 (句柄: 1901038)
2025-07-29 12:58:04,548 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-29 12:58:04,601 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-29 12:58:04,603 - __main__ - INFO - 📊 总联系人数: 5
2025-07-29 12:58:04,604 - __main__ - INFO - 📋 待处理联系人数: 5
2025-07-29 12:58:04,605 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-29 12:58:04,608 - __main__ - INFO - 📊 总窗口数: 3, 总联系人数: 5
2025-07-29 12:58:04,609 - __main__ - INFO - 
============================================================
2025-07-29 12:58:04,610 - __main__ - INFO - 🎯 开始处理第 1/3 个微信窗口
2025-07-29 12:58:04,610 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-29 12:58:04,611 - __main__ - INFO - ============================================================
2025-07-29 12:58:04,612 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-29 12:58:04,612 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-29 12:58:04,613 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-29 12:58:04,614 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-29 12:58:04,617 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 12:58:04,945 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 12:58:04,945 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 12:58:04,945 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 12:58:04,946 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 12:58:04,946 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 12:58:04,946 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 12:58:04,947 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 12:58:04,947 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 12:58:04,947 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 12:58:04,947 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 12:58:05,149 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 12:58:05,149 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 12:58:05,150 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-29 12:58:05,150 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 12:58:05,453 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 12:58:05,453 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 12:58:05,454 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 12:58:05,454 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 12:58:05,454 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 12:58:05,455 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 12:58:05,456 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 12:58:05,456 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 12:58:05,457 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 12:58:05,457 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 12:58:05,659 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 12:58:05,659 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 12:58:05,662 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-29 12:58:05,963 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 12:58:05,965 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 12:58:05,965 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 12:58:05,966 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 12:58:05,966 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 12:58:05,967 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 12:58:05,967 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-29 12:58:05,968 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-29 12:58:06,969 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-29 12:58:06,969 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-29 12:58:06,970 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-29 12:58:06,970 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-29 12:58:06,971 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-29 12:58:06,971 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-29 12:58:06,974 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-29 12:58:06,977 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-29 12:58:07,178 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-29 12:58:07,179 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-29 12:58:09,576 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-29 12:58:09,576 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-29 12:58:09,577 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.2 秒...
2025-07-29 12:58:11,789 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-29 12:58:11,989 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-29 12:58:11,991 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-29 12:58:14,375 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-29 12:58:14,377 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-29 12:58:14,377 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.8 秒...
2025-07-29 12:58:16,145 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-29 12:58:16,347 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-29 12:58:16,347 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-29 12:58:18,741 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-29 12:58:18,742 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-29 12:58:18,742 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-29 12:58:21,032 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-29 12:58:21,233 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-29 12:58:21,233 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-29 12:58:23,607 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-29 12:58:23,607 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-29 12:58:23,607 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.7 秒...
2025-07-29 12:58:26,378 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-29 12:58:26,750 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-29 12:58:26,886 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-29 12:58:30,023 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-29 12:58:30,098 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-29 12:58:30,177 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 12:58:30,279 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 12:58:30,414 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 12:58:30,560 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 12:58:30,686 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 12:58:30,812 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 12:58:30,892 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 12:58:30,976 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 589886, 进程: Weixin.exe)
2025-07-29 12:58:31,109 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 12:58:31,206 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 1901038, 进程: Weixin.exe)
2025-07-29 12:58:31,291 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-29 12:58:31,364 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 589886)
2025-07-29 12:58:31,444 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 589886) - 增强版
2025-07-29 12:58:31,861 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 12:58:31,927 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 12:58:31,955 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 12:58:32,033 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 12:58:32,088 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-29 12:58:32,127 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 12:58:32,376 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-29 12:58:32,391 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 12:58:32,601 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 12:58:32,658 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 12:58:32,721 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-29 12:58:32,795 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-29 12:58:32,831 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-29 12:58:32,867 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-29 12:58:32,891 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-29 12:58:33,944 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-29 12:58:34,009 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 12:58:34,265 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 12:58:34,394 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 12:58:34,506 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 12:58:34,641 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 12:58:34,744 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 589886, 进程: Weixin.exe)
2025-07-29 12:58:34,840 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 12:58:35,071 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 1901038, 进程: Weixin.exe)
2025-07-29 12:58:35,177 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-29 12:58:35,236 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-29 12:58:35,295 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-29 12:58:35,373 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-29 12:58:35,434 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-29 12:58:35,504 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 12:58:35,900 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 12:58:35,938 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 12:58:36,007 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 12:58:36,096 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 12:58:36,137 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 12:58:36,182 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 12:58:36,227 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 12:58:36,266 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 12:58:36,293 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 12:58:36,315 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 12:58:36,531 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 12:58:36,545 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 12:58:36,556 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-29 12:58:36,866 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 12:58:36,902 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-29 12:58:36,950 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-29 12:58:37,983 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-29 12:58:38,002 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-29 12:58:38,032 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-29 12:58:38,058 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250729_125838.log
2025-07-29 12:58:38,068 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 12:58:38,079 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-29 12:58:38,087 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-29 12:58:38,091 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-29 12:58:38,092 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-29 12:58:38,101 - modules.wechat_auto_add_simple - INFO - 🎯 找到 4 个微信窗口:
2025-07-29 12:58:38,109 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-29 12:58:38,116 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 3 个
2025-07-29 12:58:38,128 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-29 12:58:38,133 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-29 12:58:38,141 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-29 12:58:38,146 - modules.wechat_auto_add_simple - INFO -   4. 微信 (726x650) - main
2025-07-29 12:58:38,152 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-29 12:58:38,155 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 12:58:38,156 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 589886
2025-07-29 12:58:38,158 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 589886) - 增强版
2025-07-29 12:58:38,486 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 12:58:38,501 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 12:58:38,514 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 12:58:38,533 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 12:58:38,540 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-29 12:58:38,545 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 12:58:38,596 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-29 12:58:38,644 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 12:58:38,883 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 12:58:38,888 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 12:58:38,891 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 589886 (API返回: None)
2025-07-29 12:58:39,197 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 12:58:39,198 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-29 12:58:39,199 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-29 12:58:39,200 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-29 12:58:39,201 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 12:58:39,202 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-29 12:58:39,202 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-29 12:58:39,208 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-29 12:58:39,213 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-29 12:58:39,231 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 5 行数据
2025-07-29 12:58:39,235 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 12:58:39,243 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 5 个
2025-07-29 12:58:39,249 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 5 个 (总计: 5 个)
2025-07-29 12:58:39,250 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-29 12:58:39,250 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-29 12:58:39,252 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:58:39,253 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-29 12:58:39,259 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/5
2025-07-29 12:58:39,260 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13886107212 (黄欣)
2025-07-29 12:58:39,263 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:58:45,873 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13886107212
2025-07-29 12:58:45,873 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 12:58:45,874 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13886107212 执行添加朋友操作...
2025-07-29 12:58:45,874 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 12:58:45,875 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 12:58:45,876 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 12:58:45,877 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 12:58:45,882 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-29 12:58:45,882 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 12:58:45,883 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 12:58:45,884 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 12:58:45,884 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 12:58:45,884 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 12:58:45,885 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 12:58:45,885 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 12:58:45,889 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 12:58:45,891 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 12:58:45,895 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 12:58:45,897 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 12:58:45,898 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 12:58:45,900 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-07-29 12:58:45,902 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 12:58:46,404 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 12:58:46,406 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 12:58:46,485 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差38.25, 边缘比例0.0422
2025-07-29 12:58:46,493 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_125846.png
2025-07-29 12:58:46,498 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 12:58:46,500 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 12:58:46,507 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 12:58:46,513 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 12:58:46,517 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 12:58:46,523 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_125846.png
2025-07-29 12:58:46,527 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-29 12:58:46,528 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,244), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-29 12:58:46,530 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-29 12:58:46,532 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 12:58:46,533 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-29 12:58:46,534 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-07-29 12:58:46,534 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 259), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-29 12:58:46,535 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-29 12:58:46,546 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_125846.png
2025-07-29 12:58:46,548 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-29 12:58:46,549 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 259)是否包含'添加到通讯录'文字
2025-07-29 12:58:46,554 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250729_125846.png
2025-07-29 12:58:46,615 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-29 12:58:46,616 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-29 12:58:46,616 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-29 12:58:46,617 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 12:58:46,918 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 259) -> 屏幕坐标(1364, 259)
2025-07-29 12:58:47,689 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 12:58:47,690 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 12:58:47,691 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:58:47,692 - modules.wechat_auto_add_simple - INFO - ✅ 13886107212 添加朋友操作执行成功
2025-07-29 12:58:47,692 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:58:47,692 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 12:58:49,694 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 12:58:49,695 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 12:58:49,695 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 12:58:49,695 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 12:58:49,696 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 12:58:49,697 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 12:58:49,697 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 12:58:49,698 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 12:58:49,698 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 12:58:49,698 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13886107212
2025-07-29 12:58:49,702 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 12:58:49,708 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 12:58:49,709 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 12:58:49,710 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 12:58:49,715 - modules.friend_request_window - INFO -    📱 phone: '13886107212'
2025-07-29 12:58:49,715 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 12:58:49,715 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 12:58:49,743 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 5 行数据
2025-07-29 12:58:49,749 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 12:58:49,754 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 12:58:49,762 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 12:58:49,767 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13886107212
2025-07-29 12:58:49,772 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 12:58:49,778 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 12:58:49,785 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 12:58:49,790 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 12:58:49,791 - modules.friend_request_window - INFO -    📱 手机号码: 13886107212
2025-07-29 12:58:49,791 - modules.friend_request_window - INFO -    🆔 准考证: 014325110088
2025-07-29 12:58:49,792 - modules.friend_request_window - INFO -    👤 姓名: 黄欣
2025-07-29 12:58:49,792 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 12:58:49,793 - modules.friend_request_window - INFO -    📝 备注格式: '014325110088-黄欣-2025-07-29 20:58:49'
2025-07-29 12:58:49,794 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 12:58:49,794 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110088-黄欣-2025-07-29 20:58:49'
2025-07-29 12:58:49,795 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 12:58:49,796 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 5507552, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-29 12:58:49,799 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 5507552)
2025-07-29 12:58:49,799 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-29 12:58:49,804 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-29 12:58:49,805 - modules.friend_request_window - INFO - 🔄 激活窗口: 5507552
2025-07-29 12:58:50,511 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-29 12:58:50,511 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-29 12:58:50,512 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-29 12:58:50,512 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-29 12:58:50,513 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-29 12:58:50,513 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 12:58:50,513 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-29 12:58:50,514 - modules.friend_request_window - INFO -    3. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 12:58:50,514 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-29 12:58:50,514 - modules.friend_request_window - INFO -    4. D:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-29 12:58:50,514 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-29 12:58:50,515 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-29 12:58:50,516 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-29 12:58:50,516 - modules.friend_request_window - INFO -    📝 remark参数: '014325110088-黄欣-2025-07-29 20:58:49' (类型: <class 'str'>, 长度: 35)
2025-07-29 12:58:50,517 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-29 12:58:50,517 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110088-黄欣-2025-07-29 20:58:49'
2025-07-29 12:58:50,517 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-29 12:58:50,518 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-29 12:58:50,521 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-29 12:58:50,522 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-29 12:58:50,523 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-29 12:58:50,524 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-29 12:58:50,524 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-29 12:58:51,441 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-29 12:58:56,687 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-29 12:58:56,688 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-29 12:58:56,688 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-29 12:58:56,688 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-29 12:58:56,690 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '<EMAIL>...' (前50字符)
2025-07-29 12:58:57,001 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 12:58:57,018 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 12:58:57,964 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 12:58:57,974 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 12:58:57,974 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-29 12:58:57,974 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-29 12:58:57,975 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-29 12:58:57,976 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-29 12:58:58,476 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-29 12:58:58,477 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-29 12:58:58,477 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-29 12:58:58,477 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-29 12:58:58,478 - modules.friend_request_window - INFO -    📝 内容: '014325110088-黄欣-2025-07-29 20:58:49'
2025-07-29 12:58:58,478 - modules.friend_request_window - INFO -    📏 内容长度: 35 字符
2025-07-29 12:58:58,478 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110088-\xe9\xbb\x84\xe6\xac\xa3-2025-07-29 20:58:49'
2025-07-29 12:58:58,478 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-29 12:58:59,388 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-29 12:59:04,640 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-29 12:59:04,640 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-29 12:59:04,641 - modules.friend_request_window - INFO -    📝 原始文本: '014325110088-黄欣-2025-07-29 20:58:49'
2025-07-29 12:59:04,641 - modules.friend_request_window - INFO -    📏 文本长度: 35 字符
2025-07-29 12:59:04,641 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '<EMAIL>...' (前50字符)
2025-07-29 12:59:04,951 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 12:59:04,952 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 12:59:05,856 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 12:59:05,867 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 12:59:05,868 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110088-黄欣-2025-07-29 20:58:49'
2025-07-29 12:59:05,870 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-29 12:59:05,870 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110088-黄欣-2025-07-29 20:58:49'
2025-07-29 12:59:05,871 - modules.friend_request_window - INFO -    � 内容长度: 35 字符
2025-07-29 12:59:06,372 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110088-黄欣-2025-07-29 20:58:49'
2025-07-29 12:59:06,372 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-29 12:59:06,373 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-29 12:59:06,373 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-29 12:59:06,374 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-29 12:59:06,374 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-29 12:59:06,375 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-29 12:59:07,175 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-29 12:59:07,176 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-29 12:59:07,176 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-29 12:59:07,789 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:59:07,790 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-29 12:59:07,790 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-29 12:59:07,790 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-29 12:59:08,309 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 12:59:08,544 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 12:59:08,781 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 12:59:09,022 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 12:59:09,261 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 12:59:09,496 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 12:59:09,729 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 12:59:09,982 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 12:59:10,216 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 12:59:10,453 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 12:59:10,687 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 12:59:10,924 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 12:59:11,169 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 12:59:11,403 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 12:59:11,638 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 12:59:11,871 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 12:59:12,105 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 12:59:12,349 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 12:59:12,584 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 12:59:12,802 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-29 12:59:12,803 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-29 12:59:13,803 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 12:59:13,806 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-29 12:59:13,806 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-29 12:59:13,807 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-29 12:59:13,807 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-29 12:59:13,807 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 12:59:13,808 - modules.friend_request_window - INFO -    📝 备注信息: '014325110088-黄欣-2025-07-29 20:58:49'
2025-07-29 12:59:14,309 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-29 12:59:14,309 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:59:14,310 - modules.wechat_auto_add_simple - INFO - ✅ 13886107212 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-29 12:59:14,310 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 13886107212
2025-07-29 12:59:14,311 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:59:16,725 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/5
2025-07-29 12:59:16,725 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18672705056 (石刚)
2025-07-29 12:59:16,726 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:59:23,320 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18672705056
2025-07-29 12:59:23,320 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 12:59:23,321 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18672705056 执行添加朋友操作...
2025-07-29 12:59:23,321 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 12:59:23,321 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 12:59:23,322 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 12:59:23,323 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 12:59:23,328 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-29 12:59:23,329 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 12:59:23,330 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 12:59:23,330 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 12:59:23,331 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 12:59:23,331 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 12:59:23,332 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 12:59:23,332 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 12:59:23,339 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 12:59:23,340 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 12:59:23,342 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 12:59:23,344 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 12:59:23,345 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 12:59:23,347 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-07-29 12:59:23,353 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 12:59:23,857 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 12:59:23,858 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 12:59:23,933 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差27.41, 边缘比例0.0352
2025-07-29 12:59:23,942 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_125923.png
2025-07-29 12:59:23,947 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 12:59:23,949 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 12:59:23,955 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 12:59:23,957 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 12:59:23,958 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 12:59:23,963 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_125923.png
2025-07-29 12:59:23,965 - WeChatAutoAdd - INFO - 底部区域原始检测到 38 个轮廓
2025-07-29 12:59:23,966 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 12:59:23,974 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 12:59:23,975 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:59:23,977 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-07-29 12:59:23,978 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-07-29 12:59:23,979 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-07-29 12:59:23,980 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-07-29 12:59:23,982 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:59:23,983 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-07-29 12:59:23,990 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-07-29 12:59:23,992 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-07-29 12:59:23,994 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:59:23,995 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-07-29 12:59:23,998 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 12:59:24,000 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:59:24,006 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-29 12:59:24,008 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-29 12:59:24,009 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:59:24,011 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:59:24,013 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-07-29 12:59:24,014 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-07-29 12:59:24,015 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-07-29 12:59:24,025 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:59:24,027 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-07-29 12:59:24,029 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-07-29 12:59:24,031 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-07-29 12:59:24,033 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-07-29 12:59:24,040 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-07-29 12:59:24,042 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-07-29 12:59:24,043 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-07-29 12:59:24,046 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-07-29 12:59:24,048 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-07-29 12:59:24,054 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-07-29 12:59:24,056 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-07-29 12:59:24,058 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-07-29 12:59:24,059 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-07-29 12:59:24,061 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-07-29 12:59:24,062 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 12:59:24,064 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-07-29 12:59:24,072 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-07-29 12:59:24,074 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-29 12:59:24,076 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-29 12:59:24,085 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_125924.png
2025-07-29 12:59:24,091 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-29 12:59:24,093 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-07-29 12:59:24,098 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250729_125924.png
2025-07-29 12:59:24,165 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-29 12:59:24,215 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-07-29 12:59:24,220 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-29 12:59:24,239 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 12:59:24,542 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-07-29 12:59:25,320 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 12:59:25,321 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 12:59:25,322 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:59:25,323 - modules.wechat_auto_add_simple - INFO - ✅ 18672705056 添加朋友操作执行成功
2025-07-29 12:59:25,323 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:59:25,324 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 12:59:27,325 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 12:59:27,325 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 12:59:27,326 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 12:59:27,326 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 12:59:27,326 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 12:59:27,326 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 12:59:27,327 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 12:59:27,327 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 12:59:27,327 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 12:59:27,328 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18672705056
2025-07-29 12:59:27,328 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 12:59:27,328 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 12:59:27,329 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 12:59:27,329 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 12:59:27,329 - modules.friend_request_window - INFO -    📱 phone: '18672705056'
2025-07-29 12:59:27,329 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 12:59:27,330 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 12:59:27,343 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 5 行数据
2025-07-29 12:59:27,344 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 12:59:27,346 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 12:59:27,346 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 12:59:27,351 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18672705056
2025-07-29 12:59:27,351 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 12:59:27,352 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 12:59:27,354 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 12:59:27,355 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 12:59:27,355 - modules.friend_request_window - INFO -    📱 手机号码: 18672705056
2025-07-29 12:59:27,355 - modules.friend_request_window - INFO -    🆔 准考证: 014325110089
2025-07-29 12:59:27,356 - modules.friend_request_window - INFO -    👤 姓名: 石刚
2025-07-29 12:59:27,356 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 12:59:27,356 - modules.friend_request_window - INFO -    📝 备注格式: '014325110089-石刚-2025-07-29 20:59:27'
2025-07-29 12:59:27,357 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 12:59:27,357 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110089-石刚-2025-07-29 20:59:27'
2025-07-29 12:59:27,357 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 12:59:27,361 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-29 12:59:27,362 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-29 12:59:27,362 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:59:27,363 - modules.wechat_auto_add_simple - INFO - ✅ 18672705056 申请添加朋友窗口处理完成: 申请添加朋友窗口处理失败: 未找到好友申请窗口
2025-07-29 12:59:27,363 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 18672705056
2025-07-29 12:59:27,364 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:59:27,435 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-29 12:59:27,436 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-29 12:59:27,438 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-29 12:59:27,443 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-29 12:59:27,444 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-29 12:59:27,445 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-29 12:59:27,445 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-29 12:59:27,445 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-29 12:59:27,446 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-29 12:59:27,446 - __main__ - INFO - 🔄 第 1 个微信窗口达到联系人处理数量限制
2025-07-29 12:59:27,447 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-29 12:59:27,447 - __main__ - INFO - 🔄 准备激活第 2 个微信窗口
2025-07-29 12:59:27,448 - __main__ - INFO - 🔄 将在新窗口上从第一步（窗口管理）重新开始完整的6步骤流程
2025-07-29 12:59:27,448 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-29 12:59:30,449 - __main__ - INFO - 
============================================================
2025-07-29 12:59:30,450 - __main__ - INFO - 🎯 开始处理第 2/3 个微信窗口
2025-07-29 12:59:30,451 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 525668)
2025-07-29 12:59:30,451 - __main__ - INFO - ============================================================
2025-07-29 12:59:30,451 - __main__ - INFO - 🚀 开始处理微信窗口 2
2025-07-29 12:59:30,451 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 525668)
2025-07-29 12:59:30,452 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-29 12:59:30,452 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 2
2025-07-29 12:59:30,452 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-29 12:59:30,773 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 12:59:30,774 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 12:59:30,775 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 12:59:30,775 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 12:59:30,776 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 12:59:30,776 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 12:59:30,776 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 12:59:30,776 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 12:59:30,777 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 12:59:30,777 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 12:59:30,979 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 12:59:30,979 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 12:59:30,979 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 525668
2025-07-29 12:59:30,980 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-29 12:59:31,283 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 12:59:31,284 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 12:59:31,284 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 12:59:31,285 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 12:59:31,285 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 12:59:31,285 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 12:59:31,286 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 12:59:31,286 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 12:59:31,287 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 12:59:31,287 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 12:59:31,488 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 12:59:31,489 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 12:59:31,491 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 525668 (API返回: None)
2025-07-29 12:59:31,791 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 12:59:31,792 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 12:59:31,792 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 12:59:31,792 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 12:59:31,793 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 12:59:31,793 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 12:59:31,793 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-29 12:59:31,794 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-29 12:59:32,794 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-29 12:59:32,795 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 2
2025-07-29 12:59:32,795 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-29 12:59:32,795 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-29 12:59:32,796 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-29 12:59:32,796 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-29 12:59:32,796 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-29 12:59:32,796 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-29 12:59:32,997 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-29 12:59:32,997 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-29 12:59:35,373 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-29 12:59:35,374 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-29 12:59:35,375 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.7 秒...
2025-07-29 12:59:38,123 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-29 12:59:38,323 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-29 12:59:38,324 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-29 12:59:40,727 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-29 12:59:40,728 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-29 12:59:40,728 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.1 秒...
2025-07-29 12:59:42,783 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-29 12:59:42,984 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-29 12:59:42,985 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-29 12:59:45,385 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-29 12:59:45,385 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-29 12:59:45,386 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.7 秒...
2025-07-29 12:59:47,123 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-29 12:59:47,324 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-29 12:59:47,325 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-29 12:59:49,727 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-29 12:59:49,728 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-29 12:59:49,728 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.7 秒...
2025-07-29 12:59:51,409 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-29 12:59:51,610 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-29 12:59:51,611 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-29 12:59:53,986 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-29 12:59:53,987 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-29 12:59:53,987 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 12:59:53,987 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 12:59:53,988 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 12:59:53,989 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 12:59:53,990 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 12:59:53,991 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 589886, 进程: Weixin.exe)
2025-07-29 12:59:53,991 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 12:59:53,992 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 12:59:53,994 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 4589370, 进程: Weixin.exe)
2025-07-29 12:59:53,995 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 12:59:53,995 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 1901038, 进程: Weixin.exe)
2025-07-29 12:59:53,999 - modules.window_manager - INFO - 🎯 总共找到 5 个微信窗口
2025-07-29 12:59:54,000 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 589886)
2025-07-29 12:59:54,001 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 589886) - 增强版
2025-07-29 12:59:54,326 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 12:59:54,329 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 12:59:54,330 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 12:59:54,331 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 12:59:54,335 - modules.window_manager - INFO - 📏 当前窗口位置: (1200, 0), 大小: 328x454
2025-07-29 12:59:54,338 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 12:59:54,543 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-29 12:59:54,544 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 12:59:54,746 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 12:59:54,747 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 12:59:54,747 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-29 12:59:54,747 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-29 12:59:54,748 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-29 12:59:54,748 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-29 12:59:54,749 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-29 12:59:55,749 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-29 12:59:55,750 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 12:59:55,751 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 589886, 进程: Weixin.exe)
2025-07-29 12:59:55,752 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 12:59:55,753 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 12:59:55,753 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 12:59:55,754 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 12:59:55,755 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 4589370, 进程: Weixin.exe)
2025-07-29 12:59:55,756 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 12:59:55,759 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 1901038, 进程: Weixin.exe)
2025-07-29 12:59:55,762 - modules.window_manager - INFO - 🎯 总共找到 5 个微信窗口
2025-07-29 12:59:55,767 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-29 12:59:55,768 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-29 12:59:55,769 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-29 12:59:55,770 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 525668
2025-07-29 12:59:55,770 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-29 12:59:56,112 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 12:59:56,112 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 12:59:56,113 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 12:59:56,113 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 12:59:56,113 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 12:59:56,113 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 12:59:56,114 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 12:59:56,114 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 12:59:56,114 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 12:59:56,116 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 12:59:56,317 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 12:59:56,319 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 12:59:56,320 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 525668 (API返回: None)
2025-07-29 12:59:56,621 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 12:59:56,621 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-29 12:59:56,622 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-29 12:59:57,622 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-29 12:59:57,625 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 2
2025-07-29 12:59:57,625 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-29 12:59:57,628 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250729_125957.log
2025-07-29 12:59:57,629 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 12:59:57,629 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-29 12:59:57,630 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-29 12:59:57,630 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-29 12:59:57,633 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-29 12:59:57,637 - modules.wechat_auto_add_simple - INFO - 🎯 找到 5 个微信窗口:
2025-07-29 12:59:57,639 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 2 个
2025-07-29 12:59:57,640 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 3 个
2025-07-29 12:59:57,641 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-29 12:59:57,642 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-29 12:59:57,642 - modules.wechat_auto_add_simple - INFO -   3. 微信 (726x650) - main
2025-07-29 12:59:57,644 - modules.wechat_auto_add_simple - INFO -   4. 添加朋友 (328x454) - add_friend
2025-07-29 12:59:57,646 - modules.wechat_auto_add_simple - INFO -   5. 微信 (726x650) - main
2025-07-29 12:59:57,648 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-29 12:59:57,649 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 12:59:57,651 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 4589370
2025-07-29 12:59:57,652 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 4589370) - 增强版
2025-07-29 12:59:57,966 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 12:59:57,966 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 12:59:57,967 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 12:59:57,968 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 12:59:57,968 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-29 12:59:57,969 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 12:59:58,181 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-29 12:59:58,221 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 12:59:58,456 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 12:59:58,456 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 12:59:58,459 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 4589370 (API返回: None)
2025-07-29 12:59:58,760 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 12:59:58,761 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-29 12:59:58,761 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-29 12:59:58,762 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-29 12:59:58,763 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 12:59:58,763 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-29 12:59:58,763 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-29 12:59:58,769 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-29 12:59:58,776 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-29 12:59:58,793 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 5 行数据
2025-07-29 12:59:58,794 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 12:59:58,796 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 3 个
2025-07-29 12:59:58,796 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 3 个 (总计: 5 个)
2025-07-29 12:59:58,797 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-29 12:59:58,797 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-29 12:59:58,797 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:59:58,798 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-29 12:59:58,801 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/3
2025-07-29 12:59:58,802 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13803774710 (金鸣)
2025-07-29 12:59:58,803 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:00:05,412 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13803774710
2025-07-29 13:00:05,412 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 13:00:05,413 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13803774710 执行添加朋友操作...
2025-07-29 13:00:05,413 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 13:00:05,413 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 13:00:05,414 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 13:00:05,416 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 13:00:05,423 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-29 13:00:05,430 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 13:00:05,430 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 13:00:05,430 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 13:00:05,430 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 13:00:05,431 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 13:00:05,433 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 13:00:05,434 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 13:00:05,452 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 13:00:05,457 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 13:00:05,462 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 13:00:05,463 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 13:00:05,473 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 13:00:05,475 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 13:00:05,479 - WeChatAutoAdd - INFO - 共找到 6 个微信窗口
2025-07-29 13:00:05,482 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 13:00:05,988 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 13:00:05,990 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 13:00:06,075 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差27.32, 边缘比例0.0348
2025-07-29 13:00:06,082 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_130006.png
2025-07-29 13:00:06,084 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 13:00:06,087 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 13:00:06,091 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 13:00:06,092 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 13:00:06,093 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 13:00:06,099 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_130006.png
2025-07-29 13:00:06,101 - WeChatAutoAdd - INFO - 底部区域原始检测到 38 个轮廓
2025-07-29 13:00:06,108 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 13:00:06,109 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 13:00:06,111 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 13:00:06,113 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-07-29 13:00:06,116 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-07-29 13:00:06,121 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-07-29 13:00:06,125 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-07-29 13:00:06,127 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 13:00:06,129 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-07-29 13:00:06,130 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-07-29 13:00:06,141 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-07-29 13:00:06,145 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 13:00:06,161 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-07-29 13:00:06,175 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 13:00:06,188 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 13:00:06,190 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-29 13:00:06,192 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-29 13:00:06,196 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 13:00:06,203 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 13:00:06,209 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-07-29 13:00:06,216 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-07-29 13:00:06,241 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-07-29 13:00:06,355 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 13:00:06,379 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-07-29 13:00:06,395 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-07-29 13:00:06,401 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-07-29 13:00:06,405 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-07-29 13:00:06,407 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-07-29 13:00:06,410 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-07-29 13:00:06,414 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-07-29 13:00:06,430 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-07-29 13:00:06,442 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-07-29 13:00:06,445 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-07-29 13:00:06,450 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-07-29 13:00:06,457 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-07-29 13:00:06,466 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-07-29 13:00:06,475 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-07-29 13:00:06,478 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 13:00:06,480 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-07-29 13:00:06,488 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-07-29 13:00:06,489 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-29 13:00:06,491 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-29 13:00:06,499 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_130006.png
2025-07-29 13:00:06,503 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-29 13:00:06,505 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-07-29 13:00:06,515 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250729_130006.png
2025-07-29 13:00:06,543 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-29 13:00:06,545 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-07-29 13:00:06,547 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-29 13:00:06,552 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 13:00:06,854 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-07-29 13:00:07,651 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 13:00:07,654 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 13:00:07,665 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:00:07,665 - modules.wechat_auto_add_simple - INFO - ✅ 13803774710 添加朋友操作执行成功
2025-07-29 13:00:07,669 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:00:07,675 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 13:00:09,680 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 13:00:09,681 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 13:00:09,681 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 13:00:09,682 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 13:00:09,682 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 13:00:09,682 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 13:00:09,683 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 13:00:09,683 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 13:00:09,683 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 13:00:09,683 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13803774710
2025-07-29 13:00:09,684 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 13:00:09,684 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 13:00:09,684 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 13:00:09,685 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 13:00:09,685 - modules.friend_request_window - INFO -    📱 phone: '13803774710'
2025-07-29 13:00:09,685 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 13:00:09,686 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 13:00:09,701 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 5 行数据
2025-07-29 13:00:09,701 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 13:00:09,702 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 13:00:09,702 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 13:00:09,706 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13803774710
2025-07-29 13:00:09,706 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 13:00:09,707 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 13:00:09,708 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 13:00:09,708 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 13:00:09,709 - modules.friend_request_window - INFO -    📱 手机号码: 13803774710
2025-07-29 13:00:09,709 - modules.friend_request_window - INFO -    🆔 准考证: 014325110090
2025-07-29 13:00:09,709 - modules.friend_request_window - INFO -    👤 姓名: 金鸣
2025-07-29 13:00:09,710 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 13:00:09,710 - modules.friend_request_window - INFO -    📝 备注格式: '014325110090-金鸣-2025-07-29 21:00:09'
2025-07-29 13:00:09,710 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 13:00:09,711 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110090-金鸣-2025-07-29 21:00:09'
2025-07-29 13:00:09,711 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 13:00:09,718 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-29 13:00:09,720 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-29 13:00:09,723 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:00:09,724 - modules.wechat_auto_add_simple - INFO - ✅ 13803774710 申请添加朋友窗口处理完成: 申请添加朋友窗口处理失败: 未找到好友申请窗口
2025-07-29 13:00:09,725 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 13803774710
2025-07-29 13:00:09,728 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:00:12,061 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/3
2025-07-29 13:00:12,061 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15912345678 (刘敬星)
2025-07-29 13:00:12,062 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:00:18,707 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15912345678
2025-07-29 13:00:18,708 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 13:00:18,709 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15912345678 执行添加朋友操作...
2025-07-29 13:00:18,710 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 13:00:18,711 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 13:00:18,712 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 13:00:18,726 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 13:00:18,734 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-29 13:00:18,737 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 13:00:18,738 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 13:00:18,738 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 13:00:18,739 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 13:00:18,739 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 13:00:18,740 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 13:00:18,745 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 13:00:18,763 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 13:00:18,770 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 13:00:18,786 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 13:00:18,801 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 13:00:18,806 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 13:00:18,818 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 13:00:18,838 - WeChatAutoAdd - INFO - 共找到 6 个微信窗口
2025-07-29 13:00:18,972 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 13:00:19,486 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 13:00:19,488 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 13:00:19,599 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差38.62, 边缘比例0.0401
2025-07-29 13:00:19,607 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_130019.png
2025-07-29 13:00:19,609 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 13:00:19,618 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 13:00:19,622 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 13:00:19,624 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 13:00:19,626 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 13:00:19,632 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_130019.png
2025-07-29 13:00:19,637 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-29 13:00:19,639 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-29 13:00:19,642 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-29 13:00:19,644 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 13:00:19,650 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-29 13:00:19,653 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-29 13:00:19,655 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-29 13:00:19,657 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-29 13:00:19,670 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_130019.png
2025-07-29 13:00:19,675 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-29 13:00:19,815 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-29 13:00:19,856 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250729_130019.png
2025-07-29 13:00:19,900 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-29 13:00:19,925 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-29 13:00:19,931 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-29 13:00:19,936 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 13:00:20,245 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-29 13:00:21,066 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 13:00:21,068 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 13:00:21,069 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:00:21,070 - modules.wechat_auto_add_simple - INFO - ✅ 15912345678 添加朋友操作执行成功
2025-07-29 13:00:21,070 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:00:21,070 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 13:00:23,071 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 13:00:23,073 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 13:00:23,074 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 13:00:23,076 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 13:00:23,078 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 13:00:23,081 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 13:00:23,082 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 13:00:23,083 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 13:00:23,083 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 13:00:23,083 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 15912345678
2025-07-29 13:00:23,084 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 13:00:23,084 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 13:00:23,085 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 13:00:23,085 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 13:00:23,086 - modules.friend_request_window - INFO -    📱 phone: '15912345678'
2025-07-29 13:00:23,087 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 13:00:23,087 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 13:00:23,112 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 5 行数据
2025-07-29 13:00:23,114 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 13:00:23,114 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 13:00:23,115 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 13:00:23,117 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 15912345678
2025-07-29 13:00:23,117 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 13:00:23,118 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 13:00:23,119 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 13:00:23,121 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 13:00:23,124 - modules.friend_request_window - INFO -    📱 手机号码: 15912345678
2025-07-29 13:00:23,129 - modules.friend_request_window - INFO -    🆔 准考证: 014325110091
2025-07-29 13:00:23,133 - modules.friend_request_window - INFO -    👤 姓名: 刘敬星
2025-07-29 13:00:23,135 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 13:00:23,136 - modules.friend_request_window - INFO -    📝 备注格式: '014325110091-刘敬星-2025-07-29 21:00:23'
2025-07-29 13:00:23,136 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 13:00:23,137 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110091-刘敬星-2025-07-29 21:00:23'
2025-07-29 13:00:23,137 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 13:00:23,141 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 2493416, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-29 13:00:23,143 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 2493416)
2025-07-29 13:00:23,149 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-29 13:00:23,158 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-29 13:00:23,160 - modules.friend_request_window - INFO - 🔄 激活窗口: 2493416
2025-07-29 13:00:23,864 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-29 13:00:23,864 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-29 13:00:23,865 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-29 13:00:23,866 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-29 13:00:23,866 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-29 13:00:23,866 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 13:00:23,867 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-29 13:00:23,867 - modules.friend_request_window - INFO -    3. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 13:00:23,867 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-29 13:00:23,867 - modules.friend_request_window - INFO -    4. D:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-29 13:00:23,868 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-29 13:00:23,868 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-29 13:00:23,868 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-29 13:00:23,868 - modules.friend_request_window - INFO -    📝 remark参数: '014325110091-刘敬星-2025-07-29 21:00:23' (类型: <class 'str'>, 长度: 36)
2025-07-29 13:00:23,869 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-29 13:00:23,869 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110091-刘敬星-2025-07-29 21:00:23'
2025-07-29 13:00:23,869 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-29 13:00:23,870 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-29 13:00:23,871 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-29 13:00:23,873 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-29 13:00:23,873 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-29 13:00:23,874 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-29 13:00:23,875 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-29 13:00:24,784 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-29 13:00:30,100 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-29 13:00:30,100 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-29 13:00:30,101 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-29 13:00:30,101 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-29 13:00:30,101 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '<EMAIL>...' (前50字符)
2025-07-29 13:00:30,410 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 13:00:30,411 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 13:00:31,313 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 13:00:31,322 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 13:00:31,323 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-29 13:00:31,323 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-29 13:00:31,324 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-29 13:00:31,324 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-29 13:00:31,825 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-29 13:00:31,826 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-29 13:00:31,826 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-29 13:00:31,826 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-29 13:00:31,827 - modules.friend_request_window - INFO -    📝 内容: '014325110091-刘敬星-2025-07-29 21:00:23'
2025-07-29 13:00:31,827 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-29 13:00:31,827 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110091-\xe5\x88\x98\xe6\x95\xac\xe6\x98\x9f-2025-07-29 21:00:23'
2025-07-29 13:00:31,827 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-29 13:00:32,799 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-29 13:00:38,051 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-29 13:00:38,052 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-29 13:00:38,052 - modules.friend_request_window - INFO -    📝 原始文本: '014325110091-刘敬星-2025-07-29 21:00:23'
2025-07-29 13:00:38,053 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-29 13:00:38,053 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '<EMAIL>...' (前50字符)
2025-07-29 13:00:38,364 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 13:00:38,365 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 13:00:39,268 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 13:00:39,278 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 13:00:39,278 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110091-刘敬星-2025-07-29 21:00:23'
2025-07-29 13:00:39,280 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-29 13:00:39,280 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110091-刘敬星-2025-07-29 21:00:23'
2025-07-29 13:00:39,282 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-29 13:00:39,783 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110091-刘敬星-2025-07-29 21:00:23'
2025-07-29 13:00:39,784 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-29 13:00:39,784 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-29 13:00:39,784 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-29 13:00:39,786 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-29 13:00:39,786 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-29 13:00:39,787 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-29 13:00:40,587 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-29 13:00:40,588 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-29 13:00:40,588 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-29 13:00:41,232 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:00:41,233 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-29 13:00:41,233 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-29 13:00:41,234 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-29 13:00:41,751 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:41,752 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:42,000 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:42,022 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:42,305 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:42,306 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:42,542 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:42,543 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:42,785 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:42,786 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:43,026 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:43,027 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:43,270 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:43,271 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:43,504 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:43,505 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:43,745 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:43,748 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:43,989 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:43,989 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:44,228 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:44,229 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:44,468 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:44,469 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:44,701 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:44,702 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:44,940 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:44,941 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:45,180 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:45,181 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:45,419 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:45,420 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:45,662 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:45,663 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:45,907 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:45,907 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:46,143 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:46,143 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 13:00:46,359 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-29 13:00:46,359 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-29 13:00:47,360 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 13:00:47,364 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-29 13:00:47,364 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-29 13:00:47,365 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-29 13:00:47,365 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-29 13:00:47,369 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 13:00:47,370 - modules.friend_request_window - INFO -    📝 备注信息: '014325110091-刘敬星-2025-07-29 21:00:23'
2025-07-29 13:00:47,873 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-29 13:00:47,873 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:00:47,874 - modules.wechat_auto_add_simple - INFO - ✅ 15912345678 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-29 13:00:47,874 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 15912345678
2025-07-29 13:00:47,875 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:00:47,941 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-29 13:00:47,941 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-29 13:00:47,941 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-29 13:00:47,942 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-29 13:00:47,942 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-29 13:00:47,942 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-29 13:00:47,942 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-29 13:00:47,943 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-29 13:00:47,943 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-29 13:00:47,943 - __main__ - INFO - 🔄 第 2 个微信窗口达到联系人处理数量限制
2025-07-29 13:00:47,943 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-29 13:00:47,944 - __main__ - INFO - 🔄 准备激活第 3 个微信窗口
2025-07-29 13:00:47,944 - __main__ - INFO - 🔄 将在新窗口上从第一步（窗口管理）重新开始完整的6步骤流程
2025-07-29 13:00:47,944 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-29 13:00:50,945 - __main__ - INFO - 
============================================================
2025-07-29 13:00:50,945 - __main__ - INFO - 🎯 开始处理第 3/3 个微信窗口
2025-07-29 13:00:50,946 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 1901038)
2025-07-29 13:00:50,946 - __main__ - INFO - ============================================================
2025-07-29 13:00:50,946 - __main__ - INFO - 🚀 开始处理微信窗口 3
2025-07-29 13:00:50,946 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 1901038)
2025-07-29 13:00:50,947 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-29 13:00:50,947 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 3
2025-07-29 13:00:50,947 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1901038) - 增强版
2025-07-29 13:00:51,269 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 13:00:51,269 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 13:00:51,270 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 13:00:51,270 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 13:00:51,270 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 13:00:51,271 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 13:00:51,271 - modules.window_manager - INFO - 📏 当前窗口位置: (938, 171), 大小: 726x650
2025-07-29 13:00:51,271 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 13:00:51,271 - modules.window_manager - INFO - 🔄 尝试调整窗口大小和位置...
2025-07-29 13:00:51,576 - modules.window_manager - INFO - 📊 验证结果 - 当前位置: (0, 0), 实际大小: 726x650
2025-07-29 13:00:51,576 - modules.window_manager - INFO - 📊 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 13:00:51,577 - modules.window_manager - INFO - 📊 差异: 位置(0, 0), 大小(3, 0)
2025-07-29 13:00:51,577 - modules.window_manager - INFO - ✅ 窗口大小和位置验证通过
2025-07-29 13:00:51,577 - modules.window_manager - INFO - ✅ 虽然API可能报告失败，但窗口实际已调整到目标大小和位置
2025-07-29 13:00:51,577 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 13:00:51,779 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 13:00:51,779 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 13:00:51,780 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 1901038
2025-07-29 13:00:51,780 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1901038) - 增强版
2025-07-29 13:00:52,084 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 13:00:52,085 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 13:00:52,085 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 13:00:52,085 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 13:00:52,086 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 13:00:52,086 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 13:00:52,087 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 13:00:52,087 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 13:00:52,087 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 13:00:52,088 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 13:00:52,290 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 13:00:52,291 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 13:00:52,294 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 1901038 (API返回: None)
2025-07-29 13:00:52,595 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 13:00:52,595 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 13:00:52,596 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 13:00:52,596 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 13:00:52,596 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 13:00:52,597 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 13:00:52,597 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-29 13:00:52,597 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-29 13:00:53,598 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-29 13:00:53,598 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 3
2025-07-29 13:00:53,599 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-29 13:00:53,599 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-29 13:00:53,599 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-29 13:00:53,599 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-29 13:00:53,600 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-29 13:00:53,600 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-29 13:00:53,801 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-29 13:00:53,801 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-29 13:00:56,182 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-29 13:00:56,183 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-29 13:00:56,184 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.0 秒...
2025-07-29 13:00:58,227 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-29 13:00:58,427 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-29 13:00:58,428 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-29 13:01:00,826 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-29 13:01:00,826 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-29 13:01:00,827 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.7 秒...
2025-07-29 13:01:03,576 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-29 13:01:03,777 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-29 13:01:03,778 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-29 13:01:06,148 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-29 13:01:06,149 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-29 13:01:06,150 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.7 秒...
2025-07-29 13:01:08,881 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-29 13:01:09,082 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-29 13:01:09,083 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-29 13:01:11,465 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-29 13:01:11,465 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-29 13:01:11,466 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.7 秒...
2025-07-29 13:01:13,159 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-29 13:01:13,359 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-29 13:01:13,360 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-29 13:01:15,730 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-29 13:01:15,730 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-29 13:01:15,731 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 13:01:15,731 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 13:01:15,732 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 13:01:15,734 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 13:01:15,734 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 1901038, 进程: Weixin.exe)
2025-07-29 13:01:15,736 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 4589370, 进程: Weixin.exe)
2025-07-29 13:01:15,737 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 13:01:15,742 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 13:01:15,743 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 589886, 进程: Weixin.exe)
2025-07-29 13:01:15,746 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 13:01:15,746 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 13:01:15,748 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 7276788, 进程: Weixin.exe)
2025-07-29 13:01:15,752 - modules.window_manager - INFO - 🎯 总共找到 6 个微信窗口
2025-07-29 13:01:15,752 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 589886)
2025-07-29 13:01:15,754 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 589886) - 增强版
2025-07-29 13:01:16,081 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 13:01:16,082 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 13:01:16,083 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 13:01:16,084 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 13:01:16,085 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-29 13:01:16,086 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 13:01:16,087 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-29 13:01:16,088 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 13:01:16,290 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 13:01:16,291 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 13:01:16,291 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-29 13:01:16,292 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-29 13:01:16,292 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-29 13:01:16,292 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-29 13:01:16,293 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-29 13:01:17,293 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-29 13:01:17,295 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 13:01:17,296 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 589886, 进程: Weixin.exe)
2025-07-29 13:01:17,298 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 13:01:17,298 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 1901038, 进程: Weixin.exe)
2025-07-29 13:01:17,304 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 4589370, 进程: Weixin.exe)
2025-07-29 13:01:17,306 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 13:01:17,306 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 13:01:17,307 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 13:01:17,308 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 13:01:17,309 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 7276788, 进程: Weixin.exe)
2025-07-29 13:01:17,313 - modules.window_manager - INFO - 🎯 总共找到 6 个微信窗口
2025-07-29 13:01:17,315 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-29 13:01:17,315 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-29 13:01:17,315 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-29 13:01:17,316 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 1901038
2025-07-29 13:01:17,317 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1901038) - 增强版
2025-07-29 13:01:17,646 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 13:01:17,647 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 13:01:17,647 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 13:01:17,648 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 13:01:17,648 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 13:01:17,648 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 13:01:17,649 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 13:01:17,649 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 13:01:17,650 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 13:01:17,650 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 13:01:17,852 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 13:01:17,852 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 13:01:17,855 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 1901038 (API返回: None)
2025-07-29 13:01:18,156 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 13:01:18,156 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-29 13:01:18,156 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-29 13:01:19,157 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-29 13:01:19,158 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 3
2025-07-29 13:01:19,158 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-29 13:01:19,161 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250729_130119.log
2025-07-29 13:01:19,162 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 13:01:19,163 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-29 13:01:19,167 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-29 13:01:19,168 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-29 13:01:19,169 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-29 13:01:19,173 - modules.wechat_auto_add_simple - INFO - 🎯 找到 6 个微信窗口:
2025-07-29 13:01:19,173 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 3 个
2025-07-29 13:01:19,174 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 3 个
2025-07-29 13:01:19,175 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-29 13:01:19,175 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-29 13:01:19,176 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-29 13:01:19,176 - modules.wechat_auto_add_simple - INFO -   4. 微信 (726x650) - main
2025-07-29 13:01:19,179 - modules.wechat_auto_add_simple - INFO -   5. 微信 (726x650) - main
2025-07-29 13:01:19,180 - modules.wechat_auto_add_simple - INFO -   6. 添加朋友 (328x454) - add_friend
2025-07-29 13:01:19,181 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-29 13:01:19,183 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 13:01:19,184 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 7276788
2025-07-29 13:01:19,184 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 7276788) - 增强版
2025-07-29 13:01:19,491 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 13:01:19,492 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 13:01:19,493 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 13:01:19,494 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 13:01:19,495 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-29 13:01:19,496 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 13:01:19,702 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-29 13:01:19,702 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 13:01:19,904 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 13:01:19,905 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 13:01:19,908 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 7276788 (API返回: None)
2025-07-29 13:01:20,209 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 13:01:20,210 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-29 13:01:20,210 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-29 13:01:20,211 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-29 13:01:20,212 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 13:01:20,212 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-29 13:01:20,212 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-29 13:01:20,218 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-29 13:01:20,218 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-29 13:01:20,231 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 5 行数据
2025-07-29 13:01:20,232 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 13:01:20,233 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 1 个
2025-07-29 13:01:20,235 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 1 个 (总计: 5 个)
2025-07-29 13:01:20,235 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-29 13:01:20,236 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-29 13:01:20,236 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:01:20,237 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-29 13:01:20,237 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/1
2025-07-29 13:01:20,237 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13712345678 (陈浪)
2025-07-29 13:01:20,239 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:01:26,809 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13712345678
2025-07-29 13:01:26,810 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 13:01:26,810 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13712345678 执行添加朋友操作...
2025-07-29 13:01:26,810 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 13:01:26,811 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 13:01:26,811 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 13:01:26,813 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 13:01:26,818 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-29 13:01:26,821 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 13:01:26,822 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 13:01:26,823 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 13:01:26,823 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 13:01:26,825 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 13:01:26,825 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 13:01:26,830 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 13:01:26,835 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 13:01:26,838 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 13:01:26,840 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 13:01:26,844 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 13:01:26,850 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 13:01:26,854 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 13:01:26,859 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 13:01:26,865 - WeChatAutoAdd - INFO - 共找到 7 个微信窗口
2025-07-29 13:01:26,867 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 13:01:27,372 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 13:01:27,374 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 13:01:27,452 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差27.14, 边缘比例0.0349
2025-07-29 13:01:27,458 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_130127.png
2025-07-29 13:01:27,464 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 13:01:27,466 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 13:01:27,469 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 13:01:27,471 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 13:01:27,472 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 13:01:27,478 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_130127.png
2025-07-29 13:01:27,484 - WeChatAutoAdd - INFO - 底部区域原始检测到 38 个轮廓
2025-07-29 13:01:27,485 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 13:01:27,488 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 13:01:27,489 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 13:01:27,491 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-07-29 13:01:27,492 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-07-29 13:01:27,499 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-07-29 13:01:27,500 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-07-29 13:01:27,502 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 13:01:27,504 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-07-29 13:01:27,505 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-07-29 13:01:27,507 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-07-29 13:01:27,509 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 13:01:27,517 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-07-29 13:01:27,520 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 13:01:27,523 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 13:01:27,525 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-29 13:01:27,534 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-29 13:01:27,537 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 13:01:27,541 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 13:01:27,552 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-07-29 13:01:27,556 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-07-29 13:01:27,566 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-07-29 13:01:27,568 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 13:01:27,570 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-07-29 13:01:27,575 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-07-29 13:01:27,581 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-07-29 13:01:27,583 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-07-29 13:01:27,587 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-07-29 13:01:27,589 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-07-29 13:01:27,593 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-07-29 13:01:27,599 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-07-29 13:01:27,602 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-07-29 13:01:27,612 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-07-29 13:01:27,618 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-07-29 13:01:27,631 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-07-29 13:01:27,634 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-07-29 13:01:27,638 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-07-29 13:01:27,641 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 13:01:27,649 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-07-29 13:01:27,650 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-07-29 13:01:27,652 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-29 13:01:27,655 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-29 13:01:27,667 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_130127.png
2025-07-29 13:01:27,670 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-29 13:01:27,671 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-07-29 13:01:27,678 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250729_130127.png
2025-07-29 13:01:27,705 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-29 13:01:27,708 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-07-29 13:01:27,715 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-29 13:01:27,717 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 13:01:28,022 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-07-29 13:01:28,795 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 13:01:28,797 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 13:01:28,799 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:01:28,799 - modules.wechat_auto_add_simple - INFO - ✅ 13712345678 添加朋友操作执行成功
2025-07-29 13:01:28,800 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:01:28,800 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 13:01:30,801 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 13:01:30,802 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 13:01:30,802 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 13:01:30,802 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 13:01:30,802 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 13:01:30,803 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 13:01:30,803 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 13:01:30,803 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 13:01:30,804 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 13:01:30,804 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13712345678
2025-07-29 13:01:30,804 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 13:01:30,805 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 13:01:30,805 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 13:01:30,805 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 13:01:30,806 - modules.friend_request_window - INFO -    📱 phone: '13712345678'
2025-07-29 13:01:30,806 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 13:01:30,806 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 13:01:30,829 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 5 行数据
2025-07-29 13:01:30,833 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 13:01:30,833 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 13:01:30,834 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 13:01:30,837 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13712345678
2025-07-29 13:01:30,837 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 13:01:30,839 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 13:01:30,840 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 13:01:30,841 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 13:01:30,841 - modules.friend_request_window - INFO -    📱 手机号码: 13712345678
2025-07-29 13:01:30,846 - modules.friend_request_window - INFO -    🆔 准考证: 014325110092
2025-07-29 13:01:30,847 - modules.friend_request_window - INFO -    👤 姓名: 陈浪
2025-07-29 13:01:30,848 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 13:01:30,848 - modules.friend_request_window - INFO -    📝 备注格式: '014325110092-陈浪-2025-07-29 21:01:30'
2025-07-29 13:01:30,849 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 13:01:30,849 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110092-陈浪-2025-07-29 21:01:30'
2025-07-29 13:01:30,850 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 13:01:30,854 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-29 13:01:30,855 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-29 13:01:30,855 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:01:30,856 - modules.wechat_auto_add_simple - INFO - ✅ 13712345678 申请添加朋友窗口处理完成: 申请添加朋友窗口处理失败: 未找到好友申请窗口
2025-07-29 13:01:30,856 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 13712345678
2025-07-29 13:01:30,857 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 13:01:33,189 - modules.wechat_auto_add_simple - INFO - 
============================================================
2025-07-29 13:01:33,189 - modules.wechat_auto_add_simple - INFO - 📊 微信自动添加好友执行报告
2025-07-29 13:01:33,189 - modules.wechat_auto_add_simple - INFO - ============================================================
2025-07-29 13:01:33,190 - modules.wechat_auto_add_simple - INFO - ⏱️ 执行时间: 0:00:14
2025-07-29 13:01:33,190 - modules.wechat_auto_add_simple - INFO - 📊 总联系人: 5 个
2025-07-29 13:01:33,190 - modules.wechat_auto_add_simple - INFO - 📋 已处理: 1 个
2025-07-29 13:01:33,191 - modules.wechat_auto_add_simple - INFO - ✅ 成功: 1 个
2025-07-29 13:01:33,191 - modules.wechat_auto_add_simple - INFO - ❌ 失败: 0 个
2025-07-29 13:01:33,191 - modules.wechat_auto_add_simple - INFO - 📈 成功率: 100.0%
2025-07-29 13:01:33,192 - modules.wechat_auto_add_simple - INFO - ============================================================
2025-07-29 13:01:33,192 - modules.wechat_auto_add_simple - INFO - 🎉 自动化流程执行完成!
2025-07-29 13:01:33,193 - __main__ - INFO - ✅ wechat_auto_add_simple.py 执行成功
2025-07-29 13:01:33,193 - __main__ - INFO - 📊 步骤3完成 - 处理: 1, 成功: 1, 失败: 0
2025-07-29 13:01:33,194 - __main__ - INFO - ✅ 步骤 3 执行成功
2025-07-29 13:01:33,194 - __main__ - INFO - 📋 步骤3（简单添加好友）完成，检查是否需要继续后续步骤...
2025-07-29 13:01:34,195 - __main__ - INFO - 📍 当前执行步骤: IMAGE_RECOGNITION (步骤 4)
2025-07-29 13:01:34,196 - __main__ - INFO - 🖼️ 步骤4：图像识别添加 - 窗口 3
2025-07-29 13:01:34,196 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 13:01:34,202 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 13:01:34,206 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 13:01:34,216 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 13:01:34,219 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 13:01:34,222 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 13:01:34,224 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 13:01:34,235 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 13:01:34,239 - WeChatAutoAdd - INFO - 共找到 7 个微信窗口
2025-07-29 13:01:34,241 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 13:01:34,751 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 13:01:34,753 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 13:01:34,835 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差26.22, 边缘比例0.0285
2025-07-29 13:01:34,845 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_130134.png
2025-07-29 13:01:34,855 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 13:01:34,860 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 13:01:34,867 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 13:01:34,871 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 13:01:34,874 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 13:01:34,886 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_130134.png
2025-07-29 13:01:34,890 - WeChatAutoAdd - INFO - 底部区域原始检测到 38 个轮廓
2025-07-29 13:01:34,897 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 13:01:35,014 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 13:01:35,034 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 13:01:35,073 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-07-29 13:01:35,085 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-07-29 13:01:35,089 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-07-29 13:01:35,097 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-07-29 13:01:35,102 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 13:01:35,104 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-07-29 13:01:35,112 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-07-29 13:01:35,118 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-07-29 13:01:35,123 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 13:01:35,131 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-07-29 13:01:35,134 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 13:01:35,136 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 13:01:35,139 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-29 13:01:35,142 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-29 13:01:35,149 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 13:01:35,152 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 13:01:35,155 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-07-29 13:01:35,166 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-07-29 13:01:35,172 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-07-29 13:01:35,175 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 13:01:35,182 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-07-29 13:01:35,188 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-07-29 13:01:35,198 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-07-29 13:01:35,202 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-07-29 13:01:35,205 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-07-29 13:01:35,215 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-07-29 13:01:35,219 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-07-29 13:01:35,224 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-07-29 13:01:35,233 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-07-29 13:01:35,237 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-07-29 13:01:35,246 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-07-29 13:01:35,251 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-07-29 13:01:35,254 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-07-29 13:01:35,256 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-07-29 13:01:35,264 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 13:01:35,267 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-07-29 13:01:35,282 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-07-29 13:01:35,286 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-29 13:01:35,288 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-29 13:01:35,301 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_130135.png
2025-07-29 13:01:35,306 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-29 13:01:35,313 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-07-29 13:01:35,319 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250729_130135.png
2025-07-29 13:01:35,352 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-29 13:01:35,355 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-07-29 13:01:35,357 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-29 13:01:35,370 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 13:01:35,674 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-07-29 13:01:36,444 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 13:01:36,449 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 13:01:36,453 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 1901038
2025-07-29 13:01:36,454 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1901038) - 增强版
2025-07-29 13:01:36,766 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 13:01:36,766 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 13:01:36,767 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 13:01:36,767 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 13:01:36,767 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 13:01:36,768 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 13:01:36,768 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 13:01:36,769 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 13:01:36,769 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 13:01:36,769 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 13:01:36,971 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 13:01:36,972 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 13:01:36,974 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 1901038 (API返回: None)
2025-07-29 13:01:37,274 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 13:01:37,275 - __main__ - INFO - ✅ 步骤4：图像识别添加完成
2025-07-29 13:01:37,275 - __main__ - INFO - ✅ 步骤 4 执行成功
2025-07-29 13:01:38,276 - __main__ - INFO - 📍 当前执行步骤: FRIEND_REQUEST (步骤 5)
2025-07-29 13:01:38,277 - __main__ - INFO - 👥 步骤5：好友申请窗口 - 窗口 3
2025-07-29 13:01:38,277 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13886107212
2025-07-29 13:01:38,278 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 13:01:38,278 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\main_controller.py:409 in execute_single_window_flow
2025-07-29 13:01:38,279 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\main_controller.py:331 in execute_step_5_friend_request
2025-07-29 13:01:38,279 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 13:01:38,280 - modules.friend_request_window - INFO -    📱 phone: '13886107212'
2025-07-29 13:01:38,280 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 13:01:38,281 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 13:01:38,300 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 5 行数据
2025-07-29 13:01:38,300 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 13:01:38,301 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 13:01:38,301 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 13:01:38,303 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13886107212
2025-07-29 13:01:38,304 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 13:01:38,306 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 13:01:38,307 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 13:01:38,308 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 13:01:38,309 - modules.friend_request_window - INFO -    📱 手机号码: 13886107212
2025-07-29 13:01:38,311 - modules.friend_request_window - INFO -    🆔 准考证: 014325110088
2025-07-29 13:01:38,316 - modules.friend_request_window - INFO -    👤 姓名: 黄欣
2025-07-29 13:01:38,317 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 13:01:38,317 - modules.friend_request_window - INFO -    📝 备注格式: '014325110088-黄欣-2025-07-29 21:01:38'
2025-07-29 13:01:38,319 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 13:01:38,320 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110088-黄欣-2025-07-29 21:01:38'
2025-07-29 13:01:38,321 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 13:01:38,325 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-29 13:01:38,328 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-29 13:01:38,330 - __main__ - ERROR - ❌ 好友申请流程失败
2025-07-29 13:01:38,330 - __main__ - ERROR - ❌ 步骤 5 执行失败，终止当前窗口处理
2025-07-29 13:01:38,331 - __main__ - ERROR - ❌ 第 3 个微信窗口处理失败
2025-07-29 13:01:38,331 - __main__ - INFO - 
============================================================
2025-07-29 13:01:38,331 - __main__ - INFO - 📊 多窗口处理结果统计:
2025-07-29 13:01:38,332 - __main__ - INFO -   ✅ 成功处理窗口: 2/3
2025-07-29 13:01:38,332 - __main__ - INFO -   ❌ 失败处理窗口: 1/3
2025-07-29 13:01:38,332 - __main__ - INFO -   📈 成功率: 66.7%
2025-07-29 13:01:38,333 - __main__ - INFO - ============================================================
