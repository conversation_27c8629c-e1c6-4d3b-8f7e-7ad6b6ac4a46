#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置验证和优化工具
功能：验证配置文件和Excel文件的完整性，提供优化建议

版本：1.0.0
作者：AI助手
创建时间：2025-01-28
"""

import json
import pandas as pd
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple

class ConfigValidator:
    """配置验证器"""
    
    def __init__(self):
        self.config_file = "config.json"
        self.excel_file = "添加好友名单.xlsx"
        self.validation_results = {
            "config_valid": False,
            "excel_valid": False,
            "issues": [],
            "suggestions": []
        }
    
    def validate_config_file(self) -> bool:
        """验证配置文件"""
        try:
            print("🔍 验证配置文件...")
            
            if not Path(self.config_file).exists():
                self.validation_results["issues"].append(f"配置文件不存在: {self.config_file}")
                return False
            
            # 读取配置文件
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 检查必要的配置项
            required_sections = [
                "optimized_coordinates",
                "mouse_optimization", 
                "window_management",
                "batch_processing",
                "logging"
            ]
            
            missing_sections = []
            for section in required_sections:
                if section not in config:
                    missing_sections.append(section)
            
            if missing_sections:
                self.validation_results["issues"].append(f"配置文件缺少必要节: {missing_sections}")
                return False
            
            # 验证坐标配置
            coordinates = config.get("optimized_coordinates", {})
            required_coordinates = [
                "微信按钮", "通讯录按钮", "+快捷操作按钮", 
                "添加朋友选项", "搜索输入框", "搜索按钮"
            ]
            
            missing_coords = []
            for coord in required_coordinates:
                if coord not in coordinates:
                    missing_coords.append(coord)
            
            if missing_coords:
                self.validation_results["issues"].append(f"配置文件缺少坐标配置: {missing_coords}")
                return False
            
            # 验证窗口管理配置
            window_mgmt = config.get("window_management", {})
            if not window_mgmt.get("resize_main_window", True):
                self.validation_results["suggestions"].append("建议启用主窗口大小调整功能")
            
            # 验证批处理配置
            batch_config = config.get("batch_processing", {})
            if not batch_config.get("enabled", True):
                self.validation_results["suggestions"].append("建议启用批处理功能以提高效率")
            
            print("✅ 配置文件验证通过")
            self.validation_results["config_valid"] = True
            return True
            
        except json.JSONDecodeError as e:
            self.validation_results["issues"].append(f"配置文件JSON格式错误: {e}")
            return False
        except Exception as e:
            self.validation_results["issues"].append(f"配置文件验证异常: {e}")
            return False
    
    def validate_excel_file(self) -> bool:
        """验证Excel文件"""
        try:
            print("📊 验证Excel文件...")
            
            if not Path(self.excel_file).exists():
                self.validation_results["issues"].append(f"Excel文件不存在: {self.excel_file}")
                return False
            
            # 读取Excel文件
            df = pd.read_excel(self.excel_file)
            
            if len(df) == 0:
                self.validation_results["issues"].append("Excel文件为空")
                return False
            
            # 检查必要的列
            required_columns = ["手机号码", "姓名", "准考证", "验证信息"]
            missing_columns = []
            for col in required_columns:
                if col not in df.columns:
                    missing_columns.append(col)
            
            if missing_columns:
                self.validation_results["issues"].append(f"Excel文件缺少必要列: {missing_columns}")
                return False
            
            # 检查数据质量
            issues = []
            
            # 检查手机号码
            phone_col = "手机号码"
            empty_phones = df[df[phone_col].isna() | (df[phone_col].astype(str).str.strip() == "")].index.tolist()
            if empty_phones:
                issues.append(f"第 {[i+2 for i in empty_phones]} 行的手机号码为空")
            
            # 检查姓名
            name_col = "姓名"
            empty_names = df[df[name_col].isna() | (df[name_col].astype(str).str.strip() == "")].index.tolist()
            if empty_names:
                issues.append(f"第 {[i+2 for i in empty_names]} 行的姓名为空")
            
            # 检查准考证
            exam_col = "准考证"
            empty_exams = df[df[exam_col].isna() | (df[exam_col].astype(str).str.strip() == "")].index.tolist()
            if empty_exams:
                issues.append(f"第 {[i+2 for i in empty_exams]} 行的准考证为空")
            
            # 检查验证信息
            verify_col = "验证信息"
            empty_verifies = df[df[verify_col].isna() | (df[verify_col].astype(str).str.strip() == "")].index.tolist()
            if empty_verifies:
                issues.append(f"第 {[i+2 for i in empty_verifies]} 行的验证信息为空")
            
            if issues:
                self.validation_results["issues"].extend(issues)
                return False
            
            # 统计信息
            total_contacts = len(df)
            pending_contacts = len(df[df.get("处理状态", "").astype(str).str.strip().isin(["", "待处理"])])
            processed_contacts = total_contacts - pending_contacts
            
            print(f"✅ Excel文件验证通过")
            print(f"📊 总联系人数: {total_contacts}")
            print(f"📋 待处理联系人: {pending_contacts}")
            print(f"✅ 已处理联系人: {processed_contacts}")
            
            if pending_contacts == 0:
                self.validation_results["suggestions"].append("所有联系人都已处理，可能需要重置状态")
            
            self.validation_results["excel_valid"] = True
            return True
            
        except Exception as e:
            self.validation_results["issues"].append(f"Excel文件验证异常: {e}")
            return False
    
    def check_system_requirements(self) -> bool:
        """检查系统要求"""
        try:
            print("🖥️ 检查系统要求...")
            
            # 检查必要的Python包
            required_packages = [
                "pandas", "openpyxl", "pyautogui", "opencv-python", 
                "pillow", "pywin32", "psutil"
            ]
            
            missing_packages = []
            for package in required_packages:
                try:
                    __import__(package.replace("-", "_"))
                except ImportError:
                    missing_packages.append(package)
            
            if missing_packages:
                self.validation_results["issues"].append(f"缺少必要的Python包: {missing_packages}")
                self.validation_results["suggestions"].append(f"请运行: pip install {' '.join(missing_packages)}")
                return False
            
            print("✅ 系统要求检查通过")
            return True
            
        except Exception as e:
            self.validation_results["issues"].append(f"系统要求检查异常: {e}")
            return False
    
    def generate_optimization_suggestions(self) -> List[str]:
        """生成优化建议"""
        suggestions = []
        
        # 基于验证结果生成建议
        if self.validation_results["config_valid"] and self.validation_results["excel_valid"]:
            suggestions.append("✅ 配置完整，可以开始运行自动化程序")
            suggestions.append("💡 建议在网络稳定的环境下运行")
            suggestions.append("💡 建议关闭其他可能干扰的程序")
            suggestions.append("💡 建议定期备份Excel文件")
        
        return suggestions
    
    def run_validation(self) -> bool:
        """运行完整验证"""
        print("🔍 开始配置验证...")
        print("=" * 60)
        
        # 验证系统要求
        system_ok = self.check_system_requirements()
        
        # 验证配置文件
        config_ok = self.validate_config_file()
        
        # 验证Excel文件
        excel_ok = self.validate_excel_file()
        
        print("\n" + "=" * 60)
        print("📋 验证结果:")
        
        # 显示问题
        if self.validation_results["issues"]:
            print("❌ 发现问题:")
            for i, issue in enumerate(self.validation_results["issues"], 1):
                print(f"  {i}. {issue}")
        
        # 显示建议
        suggestions = self.validation_results["suggestions"] + self.generate_optimization_suggestions()
        if suggestions:
            print("\n💡 优化建议:")
            for i, suggestion in enumerate(suggestions, 1):
                print(f"  {i}. {suggestion}")
        
        # 总体结果
        overall_success = system_ok and config_ok and excel_ok
        
        print(f"\n{'✅ 验证通过' if overall_success else '❌ 验证失败'}")
        print("=" * 60)
        
        return overall_success

def main():
    """主函数"""
    try:
        print("🔧 微信自动化配置验证工具")
        print("=" * 60)
        print(f"📅 验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        validator = ConfigValidator()
        success = validator.run_validation()
        
        if success:
            print("\n🎉 配置验证完成，系统已准备就绪!")
            print("💡 可以运行 run_wechat_automation.py 开始自动化流程")
        else:
            print("\n⚠️ 配置验证失败，请修复上述问题后重试")
        
        return success
        
    except Exception as e:
        print(f"\n❌ 验证工具异常: {e}")
        return False
    
    finally:
        input("\n按回车键退出...")

if __name__ == "__main__":
    main()
