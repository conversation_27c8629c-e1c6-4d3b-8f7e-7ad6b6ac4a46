2025-07-28 15:58:39,111 - __main__ - INFO - 🚀 开始测试微信窗口切换修复
2025-07-28 15:58:39,112 - __main__ - INFO - ============================================================
2025-07-28 15:58:39,112 - __main__ - INFO - 📋 步骤1: 创建主控制器实例...
2025-07-28 15:58:40,812 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 15:58:40,812 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 15:58:40,813 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-28 15:58:40,815 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 15:58:40,817 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-28 15:58:40,818 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 15:58:40,818 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 15:58:40,821 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 15:58:40,822 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 15:58:40,823 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 15:58:40,824 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:58:40,827 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 15:58:40,835 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250728_155840.log
2025-07-28 15:58:40,843 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-28 15:58:40,843 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-28 15:58:40,844 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-28 15:58:40,844 - main_controller - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-28 15:58:40,844 - main_controller - INFO - 📅 当前北京时间: 2025-07-28 15:58:40
2025-07-28 15:58:40,845 - __main__ - INFO - 📋 步骤2: 模拟微信窗口数据...
2025-07-28 15:58:40,845 - __main__ - INFO -    - 模拟窗口数量: 2
2025-07-28 15:58:40,845 - __main__ - INFO -    - 模拟联系人数量: 2
2025-07-28 15:58:40,846 - __main__ - INFO - 📋 步骤3: 模拟步骤3返回频率错误...
2025-07-28 15:58:40,846 - __main__ - INFO - 📋 步骤4: 模拟其他步骤...
2025-07-28 15:58:40,847 - __main__ - INFO - 📋 步骤5: 测试单个窗口流程...
2025-07-28 15:58:40,849 - main_controller - INFO - 🚀 开始处理微信窗口 1
2025-07-28 15:58:40,854 - main_controller - INFO - 📋 窗口信息: 微信 (句柄: 12345)
2025-07-28 15:58:40,856 - main_controller - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-28 15:58:40,856 - main_controller - INFO - ✅ 步骤 1 执行成功
2025-07-28 15:58:41,858 - main_controller - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-28 15:58:41,858 - main_controller - INFO - ✅ 步骤 2 执行成功
2025-07-28 15:58:42,859 - main_controller - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-28 15:58:42,859 - __main__ - INFO - 🔧 模拟执行步骤3 - 窗口: 12345
2025-07-28 15:58:42,860 - __main__ - INFO - ⚠️ 模拟检测到频率错误
2025-07-28 15:58:42,860 - main_controller - WARNING - 🔄 步骤 3 检测到频率错误，需要立即切换窗口
2025-07-28 15:58:42,860 - main_controller - INFO - � 频率错误处理完成，当前微信窗口已关闭
2025-07-28 15:58:42,861 - main_controller - INFO - �🔄 立即终止当前窗口的所有后续步骤
2025-07-28 15:58:42,861 - main_controller - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-28 15:58:42,861 - __main__ - INFO - ✅ 第一个窗口正确返回RESTART_REQUIRED
2025-07-28 15:58:42,862 - __main__ - INFO - ✅ 第一个窗口状态正确标记为ERROR
2025-07-28 15:58:42,862 - __main__ - INFO - 📋 步骤6: 测试多窗口流程...
2025-07-28 15:58:42,862 - main_controller - INFO - 🔄 开始多微信窗口循环处理
2025-07-28 15:58:42,865 - main_controller - INFO - 📊 总窗口数: 2, 总联系人数: 2
2025-07-28 15:58:42,865 - main_controller - INFO - 
============================================================
2025-07-28 15:58:42,866 - main_controller - INFO - 🎯 开始处理第 1/2 个微信窗口
2025-07-28 15:58:42,866 - main_controller - INFO - 📋 窗口信息: 微信 (句柄: 12345)
2025-07-28 15:58:42,867 - main_controller - INFO - ============================================================
2025-07-28 15:58:42,868 - main_controller - INFO - 🚀 开始处理微信窗口 1
2025-07-28 15:58:42,868 - main_controller - INFO - 📋 窗口信息: 微信 (句柄: 12345)
2025-07-28 15:58:42,869 - main_controller - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-28 15:58:42,869 - main_controller - INFO - ✅ 步骤 1 执行成功
2025-07-28 15:58:43,870 - main_controller - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-28 15:58:43,870 - main_controller - INFO - ✅ 步骤 2 执行成功
2025-07-28 15:58:44,871 - main_controller - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-28 15:58:44,872 - __main__ - INFO - 🔧 模拟第一个窗口步骤3返回频率错误
2025-07-28 15:58:44,872 - main_controller - WARNING - 🔄 步骤 3 检测到频率错误，需要立即切换窗口
2025-07-28 15:58:44,872 - main_controller - INFO - � 频率错误处理完成，当前微信窗口已关闭
2025-07-28 15:58:44,873 - main_controller - INFO - �🔄 立即终止当前窗口的所有后续步骤
2025-07-28 15:58:44,873 - main_controller - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-28 15:58:44,873 - main_controller - WARNING - 🔄 第 1 个微信窗口检测到频率错误
2025-07-28 15:58:44,873 - main_controller - INFO - 🚪 频率错误处理完成，当前微信窗口已关闭
2025-07-28 15:58:44,874 - main_controller - INFO - 🔄 根据频率错误处理机制，立即切换到下一个微信窗口
2025-07-28 15:58:44,874 - main_controller - INFO - 🔄 准备激活第 2 个微信窗口
2025-07-28 15:58:44,875 - main_controller - INFO - 🔄 将在新窗口上从第一步（窗口管理）重新开始完整的6步骤流程
2025-07-28 15:58:44,875 - main_controller - INFO - ⏳ 频率错误后窗口切换延迟（5秒）...
2025-07-28 15:58:49,875 - main_controller - INFO - 
============================================================
2025-07-28 15:58:49,876 - main_controller - INFO - 🎯 开始处理第 2/2 个微信窗口
2025-07-28 15:58:49,876 - main_controller - INFO - 📋 窗口信息: 微信 (句柄: 67890)
2025-07-28 15:58:49,876 - main_controller - INFO - ============================================================
2025-07-28 15:58:49,877 - main_controller - INFO - 🚀 开始处理微信窗口 2
2025-07-28 15:58:49,877 - main_controller - INFO - 📋 窗口信息: 微信 (句柄: 67890)
2025-07-28 15:58:49,877 - main_controller - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-28 15:58:49,878 - main_controller - INFO - ✅ 步骤 1 执行成功
2025-07-28 15:58:50,878 - main_controller - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-28 15:58:50,880 - main_controller - INFO - ✅ 步骤 2 执行成功
2025-07-28 15:58:51,882 - main_controller - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-28 15:58:51,882 - __main__ - INFO - 🔧 模拟第二个窗口步骤3成功执行
2025-07-28 15:58:51,883 - main_controller - INFO - ✅ 步骤 3 执行成功
2025-07-28 15:58:51,883 - main_controller - INFO - 📋 步骤3（简单添加好友）完成，检查是否需要继续后续步骤...
2025-07-28 15:58:52,884 - main_controller - INFO - 📍 当前执行步骤: IMAGE_RECOGNITION (步骤 4)
2025-07-28 15:58:52,885 - main_controller - INFO - ✅ 步骤 4 执行成功
2025-07-28 15:58:53,885 - main_controller - INFO - 📍 当前执行步骤: FRIEND_REQUEST (步骤 5)
2025-07-28 15:58:53,886 - main_controller - INFO - ✅ 步骤 5 执行成功
2025-07-28 15:58:54,887 - main_controller - INFO - 📍 当前执行步骤: FREQUENCY_HANDLING (步骤 6)
2025-07-28 15:58:54,887 - main_controller - INFO - ✅ 步骤 6 执行成功
2025-07-28 15:58:55,888 - main_controller - INFO - ✅ 微信窗口 2 的6步骤流程全部完成
2025-07-28 15:58:55,888 - main_controller - INFO - ✅ 第 2 个微信窗口处理成功
2025-07-28 15:58:55,888 - main_controller - INFO - 
============================================================
2025-07-28 15:58:55,889 - main_controller - INFO - 📊 多窗口处理结果统计:
2025-07-28 15:58:55,889 - main_controller - INFO -   ✅ 成功处理窗口: 1/2
2025-07-28 15:58:55,890 - main_controller - INFO -   ❌ 失败处理窗口: 1/2
2025-07-28 15:58:55,890 - main_controller - INFO -   📈 成功率: 50.0%
2025-07-28 15:58:55,890 - main_controller - INFO - ============================================================
2025-07-28 15:58:55,891 - __main__ - INFO - 📋 步骤7: 验证测试结果...
2025-07-28 15:58:55,891 - __main__ - INFO -    - 窗口0状态: WindowStatus.ERROR
2025-07-28 15:58:55,891 - __main__ - INFO -    - 窗口1状态: WindowStatus.COMPLETED
2025-07-28 15:58:55,892 - __main__ - INFO - ✅ 第一个窗口正确标记为ERROR（频率错误）
2025-07-28 15:58:55,892 - __main__ - INFO - ✅ 第二个窗口正确标记为COMPLETED（正常完成）
2025-07-28 15:58:55,892 - __main__ - INFO - ✅ 多窗口流程执行成功
2025-07-28 15:58:55,893 - __main__ - INFO - ============================================================
2025-07-28 15:58:55,894 - __main__ - INFO - 🎉 微信窗口切换修复验证测试通过!
2025-07-28 15:58:55,894 - __main__ - INFO - ✅ 频率错误后能正确切换到下一个微信窗口
2025-07-28 15:58:55,895 - __main__ - INFO - ✅ 新窗口能正确从第一步重新开始
2025-07-28 15:58:55,895 - __main__ - INFO - ✅ 窗口状态标记正确
