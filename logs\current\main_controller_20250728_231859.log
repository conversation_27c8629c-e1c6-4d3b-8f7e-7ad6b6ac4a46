2025-07-28 23:18:59,132 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 23:18:59,133 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 23:18:59,134 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-28 23:18:59,135 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 23:18:59,136 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-28 23:18:59,137 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 23:18:59,138 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 23:18:59,140 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 23:18:59,141 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 23:18:59,142 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 23:18:59,144 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:18:59,150 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 23:18:59,160 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250728_231859.log
2025-07-28 23:18:59,163 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-28 23:18:59,168 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-28 23:18:59,169 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-28 23:18:59,180 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-28 23:18:59,182 - __main__ - INFO - 📅 当前北京时间: 2025-07-29 07:18:59
2025-07-28 23:18:59,183 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-28 23:18:59,187 - __main__ - INFO - 📅 启动时间: 2025-07-29 07:18:59
2025-07-28 23:18:59,188 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-28 23:18:59,189 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 23:18:59,779 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 23:18:59,783 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 23:19:00,318 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 23:19:00,322 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 23:19:00,327 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-28 23:19:00,327 - __main__ - INFO - ✅ 找到 2 个微信窗口
2025-07-28 23:19:00,328 - __main__ - INFO -   窗口 1: 微信 (句柄: 197994)
2025-07-28 23:19:00,329 - __main__ - INFO -   窗口 2: 微信 (句柄: 525668)
2025-07-28 23:19:00,329 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-28 23:19:01,826 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-28 23:19:01,827 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-28 23:19:01,828 - __main__ - INFO - 📋 待处理联系人数: 2978
2025-07-28 23:19:01,829 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-28 23:19:01,830 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2978
2025-07-28 23:19:01,830 - __main__ - INFO - 
============================================================
2025-07-28 23:19:01,831 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口
2025-07-28 23:19:01,832 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-28 23:19:01,832 - __main__ - INFO - ============================================================
2025-07-28 23:19:01,834 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-28 23:19:01,835 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-28 23:19:01,836 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-28 23:19:01,838 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-28 23:19:01,841 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 23:19:02,145 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 23:19:02,145 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 23:19:02,146 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 23:19:02,146 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 23:19:02,147 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 23:19:02,147 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 23:19:02,147 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 23:19:02,148 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 23:19:02,148 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 23:19:02,149 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 23:19:02,351 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 23:19:02,352 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 23:19:02,355 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-28 23:19:02,355 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 23:19:02,659 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 23:19:02,659 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 23:19:02,660 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 23:19:02,660 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 23:19:02,661 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 23:19:02,662 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 23:19:02,662 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 23:19:02,663 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 23:19:02,663 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 23:19:02,663 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 23:19:02,865 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 23:19:02,866 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 23:19:02,867 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-28 23:19:03,168 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 23:19:03,169 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 23:19:03,169 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 23:19:03,170 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 23:19:03,170 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 23:19:03,170 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 23:19:03,170 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-28 23:19:03,171 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-28 23:19:04,171 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-28 23:19:04,172 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-28 23:19:04,172 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-28 23:19:04,172 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-28 23:19:04,173 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-28 23:19:04,173 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-28 23:19:04,173 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-28 23:19:04,174 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-28 23:19:04,375 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-28 23:19:04,377 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-28 23:19:06,772 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-28 23:19:06,773 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-28 23:19:06,773 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-28 23:19:09,028 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-28 23:19:09,229 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-28 23:19:09,230 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-28 23:19:11,605 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-28 23:19:11,605 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-28 23:19:11,606 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-28 23:19:13,896 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-28 23:19:14,097 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-28 23:19:14,097 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-28 23:19:16,473 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-28 23:19:16,473 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-28 23:19:16,473 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.7 秒...
2025-07-28 23:19:18,129 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-28 23:19:18,330 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-28 23:19:18,331 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-28 23:19:20,704 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-28 23:19:20,705 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-28 23:19:20,705 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.6 秒...
2025-07-28 23:19:23,340 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-28 23:19:23,540 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-28 23:19:23,541 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-28 23:19:25,920 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-28 23:19:25,921 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-28 23:19:25,921 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-28 23:19:25,921 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-28 23:19:25,922 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 23:19:25,924 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 23:19:25,925 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 23:19:25,926 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 23:19:25,926 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 23:19:25,929 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1378610, 进程: Weixin.exe)
2025-07-28 23:19:25,932 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-28 23:19:25,936 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 1378610)
2025-07-28 23:19:25,937 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1378610) - 增强版
2025-07-28 23:19:26,243 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 23:19:26,244 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 23:19:26,245 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-28 23:19:26,245 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-28 23:19:26,245 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-28 23:19:26,246 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-28 23:19:26,451 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-28 23:19:26,452 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-28 23:19:26,653 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 23:19:26,654 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 23:19:26,654 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-28 23:19:26,654 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-28 23:19:26,654 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-28 23:19:26,655 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-28 23:19:26,655 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-28 23:19:27,656 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-28 23:19:27,656 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 23:19:27,659 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 23:19:27,660 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 23:19:27,660 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 23:19:27,661 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 23:19:27,662 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1378610, 进程: Weixin.exe)
2025-07-28 23:19:27,664 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-28 23:19:27,665 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-28 23:19:27,665 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-28 23:19:27,666 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-28 23:19:27,667 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-28 23:19:27,668 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 23:19:27,975 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 23:19:27,976 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 23:19:27,976 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 23:19:27,976 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 23:19:27,977 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 23:19:27,977 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 23:19:27,977 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 23:19:27,978 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 23:19:27,978 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 23:19:27,978 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 23:19:28,180 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 23:19:28,180 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 23:19:28,182 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-28 23:19:28,483 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 23:19:28,483 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-28 23:19:28,483 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-28 23:19:29,484 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-28 23:19:29,485 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-28 23:19:29,486 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-28 23:19:29,488 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250728_231929.log
2025-07-28 23:19:29,489 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-28 23:19:29,489 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-28 23:19:29,490 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-28 23:19:29,490 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-28 23:19:29,493 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-28 23:19:29,493 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-28 23:19:29,494 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-28 23:19:29,494 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-28 23:19:29,494 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-28 23:19:29,494 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-28 23:19:29,495 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-28 23:19:29,496 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 23:19:29,497 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 1378610
2025-07-28 23:19:29,497 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1378610) - 增强版
2025-07-28 23:19:29,806 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 23:19:29,808 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 23:19:29,809 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-28 23:19:29,809 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-28 23:19:29,810 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-28 23:19:29,810 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-28 23:19:29,811 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-28 23:19:29,811 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-28 23:19:30,013 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 23:19:30,014 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 23:19:30,016 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 1378610 (API返回: None)
2025-07-28 23:19:30,318 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 23:19:30,319 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-28 23:19:30,319 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-28 23:19:30,320 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-28 23:19:30,321 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 23:19:30,321 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-28 23:19:30,322 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-28 23:19:30,328 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-28 23:19:30,329 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-28 23:19:30,935 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-28 23:19:30,938 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 23:19:31,280 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2978 个
2025-07-28 23:19:31,281 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2978 个 (总计: 3135 个)
2025-07-28 23:19:31,281 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:19:31,282 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-28 23:19:31,282 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2978
2025-07-28 23:19:31,282 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 17278814816 (闫明张)
2025-07-28 23:19:31,283 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:19:37,860 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 17278814816
2025-07-28 23:19:37,860 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 23:19:37,861 - modules.wechat_auto_add_simple - INFO - 👥 开始为 17278814816 执行添加朋友操作...
2025-07-28 23:19:37,861 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 23:19:37,862 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 23:19:37,863 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 23:19:37,863 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 23:19:37,866 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 2 个图片文件
2025-07-28 23:19:37,869 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 23:19:37,870 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 23:19:37,871 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 23:19:37,873 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 23:19:37,875 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 23:19:37,876 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 23:19:37,877 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 23:19:37,885 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-28 23:19:37,895 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 23:19:37,902 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 23:19:37,914 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 23:19:37,915 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-28 23:19:37,919 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-28 23:19:38,424 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-28 23:19:38,425 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-28 23:19:38,500 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差37.37, 边缘比例0.0367
2025-07-28 23:19:38,507 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_231938.png
2025-07-28 23:19:38,508 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 23:19:38,510 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 23:19:38,511 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-28 23:19:38,512 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-28 23:19:38,517 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-28 23:19:38,525 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_231938.png
2025-07-28 23:19:38,526 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-28 23:19:38,535 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-28 23:19:38,538 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-28 23:19:38,539 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-28 23:19:38,540 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-28 23:19:38,541 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-28 23:19:38,542 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-28 23:19:38,544 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 23:19:38,557 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_231938.png
2025-07-28 23:19:38,559 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 23:19:38,567 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-28 23:19:38,572 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_231938.png
2025-07-28 23:19:38,644 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 23:19:38,645 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-28 23:19:38,646 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 23:19:38,647 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 23:19:38,948 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-28 23:19:39,736 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 23:19:39,738 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 23:19:39,741 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:19:39,742 - modules.wechat_auto_add_simple - INFO - ✅ 17278814816 添加朋友操作执行成功
2025-07-28 23:19:39,745 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:19:39,746 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 23:19:41,747 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-28 23:19:41,748 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-28 23:19:41,748 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-28 23:19:41,749 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 23:19:41,750 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 23:19:41,750 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 23:19:41,751 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 23:19:41,751 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 23:19:41,752 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-28 23:19:41,753 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 17278814816
2025-07-28 23:19:41,764 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-28 23:19:41,765 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 23:19:41,765 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:695 in _handle_friend_request_window
2025-07-28 23:19:41,766 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-28 23:19:41,766 - modules.friend_request_window - INFO -    📱 phone: '17278814816'
2025-07-28 23:19:41,767 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-28 23:19:41,768 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-28 23:19:42,364 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-28 23:19:42,364 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-28 23:19:42,365 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-28 23:19:42,365 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 23:19:42,368 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 17278814816
2025-07-28 23:19:42,368 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-28 23:19:42,369 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 23:19:42,371 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-28 23:19:42,371 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-28 23:19:42,372 - modules.friend_request_window - INFO -    📱 手机号码: 17278814816
2025-07-28 23:19:42,372 - modules.friend_request_window - INFO -    🆔 准考证: 015825120191
2025-07-28 23:19:42,373 - modules.friend_request_window - INFO -    👤 姓名: 闫明张
2025-07-28 23:19:42,373 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 23:19:42,373 - modules.friend_request_window - INFO -    📝 备注格式: '015825120191-闫明张-2025-07-29 07:19:42'
2025-07-28 23:19:42,374 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 23:19:42,374 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '015825120191-闫明张-2025-07-29 07:19:42'
2025-07-28 23:19:42,375 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 23:19:42,376 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 1968132, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-28 23:19:42,378 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 1968132)
2025-07-28 23:19:42,378 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-28 23:19:42,379 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-28 23:19:42,379 - modules.friend_request_window - INFO - 🔄 激活窗口: 1968132
2025-07-28 23:19:43,082 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-28 23:19:43,083 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-28 23:19:43,084 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-28 23:19:43,085 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:499 in _analyze_search_result
2025-07-28 23:19:43,086 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-28 23:19:43,086 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 23:19:43,087 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-28 23:19:43,087 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:695 in _handle_friend_request_window
2025-07-28 23:19:43,087 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-28 23:19:43,087 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-28 23:19:43,088 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-28 23:19:43,089 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-28 23:19:43,089 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-28 23:19:43,090 - modules.friend_request_window - INFO -    📝 remark参数: '015825120191-闫明张-2025-07-29 07:19:42' (类型: <class 'str'>, 长度: 36)
2025-07-28 23:19:43,090 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-28 23:19:43,091 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '015825120191-闫明张-2025-07-29 07:19:42'
2025-07-28 23:19:43,091 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-28 23:19:43,091 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-28 23:19:43,092 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-28 23:19:43,092 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-28 23:19:43,092 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-28 23:19:43,092 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-28 23:19:43,094 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-28 23:19:44,023 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-28 23:19:49,269 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-28 23:19:49,269 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-28 23:19:49,270 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-28 23:19:49,270 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-28 23:19:49,272 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '✅ 严格6步骤顺序执行：窗口管理→主界面→简单添加→图像识别→好友申请→频率处理-切换到下一个窗口-...' (前50字符)
2025-07-28 23:19:49,583 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 23:19:49,584 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 23:19:50,486 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 23:19:50,496 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 23:19:50,497 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-28 23:19:50,497 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-28 23:19:50,498 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-28 23:19:50,498 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-28 23:19:51,000 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-28 23:19:51,000 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-28 23:19:51,000 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-28 23:19:51,001 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-28 23:19:51,001 - modules.friend_request_window - INFO -    📝 内容: '015825120191-闫明张-2025-07-29 07:19:42'
2025-07-28 23:19:51,002 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-28 23:19:51,003 - modules.friend_request_window - INFO -    🔤 内容编码: b'015825120191-\xe9\x97\xab\xe6\x98\x8e\xe5\xbc\xa0-2025-07-29 07:19:42'
2025-07-28 23:19:51,003 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-28 23:19:51,919 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-28 23:19:57,163 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-28 23:19:57,164 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-28 23:19:57,164 - modules.friend_request_window - INFO -    📝 原始文本: '015825120191-闫明张-2025-07-29 07:19:42'
2025-07-28 23:19:57,164 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-28 23:19:57,165 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '✅ 严格6步骤顺序执行：窗口管理→主界面→简单添加→图像识别→好友申请→频率处理-切换到下一个窗口-...' (前50字符)
2025-07-28 23:19:57,477 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 23:19:57,477 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 23:19:58,381 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 23:19:58,390 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 23:19:58,391 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '015825120191-闫明张-2025-07-29 07:19:42'
2025-07-28 23:19:58,391 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-28 23:19:58,392 - modules.friend_request_window - INFO -    📝 已填写内容: '015825120191-闫明张-2025-07-29 07:19:42'
2025-07-28 23:19:58,393 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-28 23:19:58,894 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '015825120191-闫明张-2025-07-29 07:19:42'
2025-07-28 23:19:58,895 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-28 23:19:58,895 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-28 23:19:58,895 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-28 23:19:58,896 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-28 23:19:58,896 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-28 23:19:58,896 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-28 23:19:59,697 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-28 23:19:59,697 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-28 23:19:59,697 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-28 23:20:00,321 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:20:00,321 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-28 23:20:00,322 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-28 23:20:00,323 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-28 23:20:00,824 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-28 23:20:00,827 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-28 23:20:00,827 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-28 23:20:00,827 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-28 23:20:00,828 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-28 23:20:00,828 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-28 23:20:00,828 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-28 23:20:00,829 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-28 23:20:00,829 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-28 23:20:00,829 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-28 23:20:00,830 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-28 23:20:00,830 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-28 23:20:00,832 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-28 23:20:00,833 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-28 23:20:00,834 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-28 23:20:00,834 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-28 23:20:00,838 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-28 23:20:00,839 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-28 23:20:00,840 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-28 23:20:01,342 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-28 23:20:01,342 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-28 23:20:01,343 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-28 23:20:01,343 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-28 23:20:01,343 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-28 23:20:01,344 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-28 23:20:01,344 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-28 23:20:01,345 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-28 23:20:02,252 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-28 23:20:02,253 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-28 23:20:02,253 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-28 23:20:02,253 - modules.friend_request_window - INFO - 🔄 步骤2: 关闭添加朋友窗口...
2025-07-28 23:20:02,253 - modules.friend_request_window - INFO - 🎯 使用指定坐标 (1504, 13) 关闭添加朋友窗口...
2025-07-28 23:20:02,754 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui点击坐标 (1504, 13)...
2025-07-28 23:20:03,868 - modules.friend_request_window - INFO - ✅ pyautogui点击添加朋友窗口关闭按钮成功
2025-07-28 23:20:03,869 - modules.friend_request_window - INFO - 🔄 步骤3: 关闭当前微信窗口...
2025-07-28 23:20:03,869 - modules.friend_request_window - INFO - 🎯 使用指定坐标 (700, 16) 关闭当前微信窗口...
2025-07-28 23:20:04,370 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui点击坐标 (700, 16)...
2025-07-28 23:20:05,485 - modules.friend_request_window - INFO - ✅ pyautogui点击当前微信窗口关闭按钮成功
2025-07-28 23:20:05,485 - modules.friend_request_window - INFO - 🔄 关闭错误相关窗口...
2025-07-28 23:20:05,511 - modules.friend_request_window - INFO - ✅ 频率错误处理完成
2025-07-28 23:20:05,511 - modules.friend_request_window - INFO - 🔄 频率错误处理完成，需要重新开始流程
2025-07-28 23:20:05,512 - modules.friend_request_window - INFO - 🔄 已设置重新开始标志
2025-07-28 23:20:05,512 - modules.friend_request_window - INFO - ✅ 原有频率错误处理成功，已切换到微信2
2025-07-28 23:20:05,513 - modules.friend_request_window - WARNING - ⚠️ 检测到并处理了频率错误，已切换到微信2
2025-07-28 23:20:05,514 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-28 23:20:05,516 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-28 23:20:05,518 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 23:20:05,520 - modules.friend_request_window - INFO -    📝 备注信息: '015825120191-闫明张-2025-07-29 07:19:42'
2025-07-28 23:20:06,021 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-28 23:20:06,022 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:20:06,023 - modules.wechat_auto_add_simple - INFO - ✅ 17278814816 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-28 23:20:06,023 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 17278814816
2025-07-28 23:20:06,024 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:20:09,778 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2978
2025-07-28 23:20:09,778 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18672705056 (伍倩)
2025-07-28 23:20:09,779 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:20:16,497 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18672705056
2025-07-28 23:20:16,498 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 23:20:16,499 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18672705056 执行添加朋友操作...
2025-07-28 23:20:16,499 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 23:20:16,499 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 23:20:16,500 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 23:20:16,501 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 23:20:16,506 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 23:20:16,510 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 23:20:16,510 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 23:20:16,510 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 23:20:16,510 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 23:20:16,511 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 23:20:16,511 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 23:20:16,511 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 23:20:16,514 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 23:20:16,520 - WeChatAutoAdd - DEBUG - 找到微信窗口: ● main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 23:20:16,527 - WeChatAutoAdd - INFO - 共找到 2 个微信窗口
2025-07-28 23:20:16,535 - WeChatAutoAdd - INFO - 找到微信主窗口: 微信
2025-07-28 23:20:17,045 - WeChatAutoAdd - INFO - 目标窗口: 微信
2025-07-28 23:20:17,052 - WeChatAutoAdd - INFO - 窗口位置: (0, 0), 尺寸: 726x650
2025-07-28 23:20:17,157 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差36.60, 边缘比例0.0606
2025-07-28 23:20:17,176 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_232017.png
2025-07-28 23:20:17,177 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 23:20:17,178 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 23:20:17,179 - WeChatAutoAdd - INFO - 截图尺寸: 726x650
2025-07-28 23:20:17,180 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=405-650 (高度:245)
2025-07-28 23:20:17,181 - WeChatAutoAdd - INFO - 特别关注区域: Y=466-526 (距底部154像素)
2025-07-28 23:20:17,191 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_232017.png
2025-07-28 23:20:17,193 - WeChatAutoAdd - INFO - 底部区域原始检测到 91 个轮廓
2025-07-28 23:20:17,194 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,612), 尺寸17x1, 长宽比17.00, 面积17
2025-07-28 23:20:17,196 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,610), 尺寸18x1, 长宽比18.00, 面积18
2025-07-28 23:20:17,199 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,604), 尺寸18x3, 长宽比6.00, 面积54
2025-07-28 23:20:17,204 - WeChatAutoAdd - INFO - 重要轮廓: 位置(614,602), 尺寸96x32, 长宽比3.00, 已知特征:False
2025-07-28 23:20:17,206 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度96 (需要70-95)
2025-07-28 23:20:17,208 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,601), 尺寸18x1, 长宽比18.00, 面积18
2025-07-28 23:20:17,209 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,599), 尺寸17x1, 长宽比17.00, 面积17
2025-07-28 23:20:17,210 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,547), 尺寸16x22, 长宽比0.73, 面积352
2025-07-28 23:20:17,211 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(161,526), 尺寸4x1, 长宽比4.00, 面积4
2025-07-28 23:20:17,212 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,524), 尺寸4x2, 长宽比2.00, 面积8
2025-07-28 23:20:17,213 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,523), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 23:20:17,215 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,522), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 23:20:17,219 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(161,522), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 23:20:17,221 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,521), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 23:20:17,222 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(155,521), 尺寸5x7, 长宽比0.71, 面积35
2025-07-28 23:20:17,223 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(143,521), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:20:17,224 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(146,520), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 23:20:17,225 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(179,519), 尺寸10x7, 长宽比1.43, 面积70
2025-07-28 23:20:17,226 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(162,519), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 23:20:17,227 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(196,518), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 23:20:17,228 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,518), 尺寸1x5, 长宽比0.20, 面积5
2025-07-28 23:20:17,229 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,518), 尺寸4x2, 长宽比2.00, 面积8
2025-07-28 23:20:17,231 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(150,518), 尺寸1x9, 长宽比0.11, 面积9
2025-07-28 23:20:17,238 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(180,516), 尺寸10x2, 长宽比5.00, 面积20
2025-07-28 23:20:17,240 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(155,516), 尺寸29x12, 长宽比2.42, 面积348
2025-07-28 23:20:17,242 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=516 (距底部154像素区域)
2025-07-28 23:20:17,244 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 23:20:17,246 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,516), 尺寸4x13, 长宽比0.31, 面积52
2025-07-28 23:20:17,248 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(185,515), 尺寸42x13, 长宽比3.23, 面积546
2025-07-28 23:20:17,258 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=515 (距底部154像素区域)
2025-07-28 23:20:17,261 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 23:20:17,263 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(123,515), 尺寸26x14, 长宽比1.86, 面积364
2025-07-28 23:20:17,266 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=515 (距底部154像素区域)
2025-07-28 23:20:17,272 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 23:20:17,276 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,505), 尺寸8x1, 长宽比8.00, 面积8
2025-07-28 23:20:17,277 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,503), 尺寸9x1, 长宽比9.00, 面积9
2025-07-28 23:20:17,280 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,502), 尺寸8x4, 长宽比2.00, 面积32
2025-07-28 23:20:17,288 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(139,502), 尺寸6x2, 长宽比3.00, 面积12
2025-07-28 23:20:17,289 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,499), 尺寸8x3, 长宽比2.67, 面积24
2025-07-28 23:20:17,291 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(163,498), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:20:17,292 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(154,498), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:20:17,295 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,497), 尺寸1x4, 长宽比0.25, 面积4
2025-07-28 23:20:17,296 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(229,495), 尺寸29x11, 长宽比2.64, 面积319
2025-07-28 23:20:17,303 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=495 (距底部154像素区域)
2025-07-28 23:20:17,305 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 23:20:17,306 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(163,495), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 23:20:17,311 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,495), 尺寸10x10, 长宽比1.00, 面积100
2025-07-28 23:20:17,316 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=495 (距底部154像素区域)
2025-07-28 23:20:17,322 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 23:20:17,324 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(170,494), 尺寸5x11, 长宽比0.45, 面积55
2025-07-28 23:20:17,335 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(168,494), 尺寸1x7, 长宽比0.14, 面积7
2025-07-28 23:20:17,347 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,493), 尺寸7x13, 长宽比0.54, 面积91
2025-07-28 23:20:17,357 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,493), 尺寸10x1, 长宽比10.00, 面积10
2025-07-28 23:20:17,359 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,493), 尺寸41x14, 长宽比2.93, 面积574
2025-07-28 23:20:17,361 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=493 (距底部154像素区域)
2025-07-28 23:20:17,363 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 23:20:17,366 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,491), 尺寸37x37, 长宽比1.00, 面积1369
2025-07-28 23:20:17,371 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=491 (距底部154像素区域)
2025-07-28 23:20:17,373 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 23:20:17,374 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,456), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 23:20:17,375 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(247,456), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 23:20:17,376 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(244,456), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 23:20:17,377 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(203,456), 尺寸9x3, 长宽比3.00, 面积27
2025-07-28 23:20:17,379 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(208,454), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 23:20:17,380 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(203,454), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:20:17,385 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(175,454), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:20:17,387 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,454), 尺寸10x8, 长宽比1.25, 面积80
2025-07-28 23:20:17,389 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(201,453), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:20:17,390 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(158,453), 尺寸4x5, 长宽比0.80, 面积20
2025-07-28 23:20:17,392 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,452), 尺寸4x4, 长宽比1.00, 面积16
2025-07-28 23:20:17,393 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,452), 尺寸7x4, 长宽比1.75, 面积28
2025-07-28 23:20:17,394 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,452), 尺寸8x7, 长宽比1.14, 面积56
2025-07-28 23:20:17,395 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,452), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:20:17,396 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,452), 尺寸1x3, 长宽比0.33, 面积3
2025-07-28 23:20:17,398 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,452), 尺寸6x10, 长宽比0.60, 面积60
2025-07-28 23:20:17,404 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,451), 尺寸5x4, 长宽比1.25, 面积20
2025-07-28 23:20:17,406 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(159,451), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 23:20:17,406 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,451), 尺寸5x4, 长宽比1.25, 面积20
2025-07-28 23:20:17,407 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(132,451), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:20:17,408 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(123,451), 尺寸8x7, 长宽比1.14, 面积56
2025-07-28 23:20:17,410 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(171,450), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 23:20:17,411 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(230,449), 尺寸13x11, 长宽比1.18, 面积143
2025-07-28 23:20:17,412 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,449), 尺寸5x1, 长宽比5.00, 面积5
2025-07-28 23:20:17,413 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,449), 尺寸23x11, 长宽比2.09, 面积253
2025-07-28 23:20:17,420 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,448), 尺寸17x12, 长宽比1.42, 面积204
2025-07-28 23:20:17,422 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(173,448), 尺寸10x8, 长宽比1.25, 面积80
2025-07-28 23:20:17,423 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,447), 尺寸19x13, 长宽比1.46, 面积247
2025-07-28 23:20:17,425 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,447), 尺寸11x13, 长宽比0.85, 面积143
2025-07-28 23:20:17,426 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(159,447), 尺寸13x3, 长宽比4.33, 面积39
2025-07-28 23:20:17,428 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(218,435), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 23:20:17,429 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,435), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 23:20:17,430 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(152,435), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 23:20:17,436 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(253,431), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 23:20:17,438 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(186,429), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:20:17,440 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(229,427), 尺寸30x11, 长宽比2.73, 面积330
2025-07-28 23:20:17,442 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(186,427), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:20:17,444 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(188,426), 尺寸3x11, 长宽比0.27, 面积33
2025-07-28 23:20:17,445 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,426), 尺寸6x11, 长宽比0.55, 面积66
2025-07-28 23:20:17,446 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,426), 尺寸5x11, 长宽比0.45, 面积55
2025-07-28 23:20:17,449 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,426), 尺寸17x11, 长宽比1.55, 面积187
2025-07-28 23:20:17,454 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=87.1 (阈值:60)
2025-07-28 23:20:17,456 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(202,425), 尺寸9x13, 长宽比0.69, 面积117
2025-07-28 23:20:17,460 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,425), 尺寸7x13, 长宽比0.54, 面积91
2025-07-28 23:20:17,462 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(153,425), 尺寸15x13, 长宽比1.15, 面积195
2025-07-28 23:20:17,463 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=88.5 (阈值:60)
2025-07-28 23:20:17,469 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,425), 尺寸14x13, 长宽比1.08, 面积182
2025-07-28 23:20:17,476 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=78.1 (阈值:60)
2025-07-28 23:20:17,478 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,423), 尺寸38x38, 长宽比1.00, 面积1444
2025-07-28 23:20:17,482 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=71.2 (阈值:60)
2025-07-28 23:20:17,489 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,405), 尺寸726x245, 长宽比2.96, 面积177870
2025-07-28 23:20:17,491 - WeChatAutoAdd - INFO - 底部区域找到 15 个按钮候选
2025-07-28 23:20:17,493 - WeChatAutoAdd - INFO - 选择特殊位置按钮: Y=491 (距底部154像素)
2025-07-28 23:20:17,495 - WeChatAutoAdd - INFO - 在底部找到按钮: (90, 509), 尺寸: 37x37, 位置得分: 3.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-07-28 23:20:17,497 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 23:20:17,525 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_232017.png
2025-07-28 23:20:17,527 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 23:20:17,528 - WeChatAutoAdd - INFO - 开始验证按钮区域(90, 509)是否包含'添加到通讯录'文字
2025-07-28 23:20:17,532 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_232017.png
2025-07-28 23:20:17,557 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 23:20:17,559 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0509, 平均亮度=215.2, 亮度标准差=45.9
2025-07-28 23:20:17,561 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 23:20:17,562 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 23:20:17,864 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(90, 509) -> 屏幕坐标(90, 509)
2025-07-28 23:20:18,634 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 23:20:18,638 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 23:20:18,642 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:20:18,642 - modules.wechat_auto_add_simple - INFO - ✅ 18672705056 添加朋友操作执行成功
2025-07-28 23:20:18,643 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:20:18,643 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 23:20:20,645 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:20:20,645 - modules.wechat_auto_add_simple - INFO - ℹ️ 18672705056 未发现申请添加朋友窗口，可能直接添加成功
2025-07-28 23:20:20,646 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 18672705056
2025-07-28 23:20:20,648 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:20:24,547 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 3/2978
2025-07-28 23:20:24,548 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13803774710 (吴婷婷)
2025-07-28 23:20:24,549 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:20:31,129 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13803774710
2025-07-28 23:20:31,130 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 23:20:31,131 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13803774710 执行添加朋友操作...
2025-07-28 23:20:31,131 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 23:20:31,132 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 23:20:31,132 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 23:20:31,133 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 23:20:31,138 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 23:20:31,141 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 23:20:31,141 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 23:20:31,142 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 23:20:31,142 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 23:20:31,142 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 23:20:31,142 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 23:20:31,143 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 23:20:31,147 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 23:20:31,154 - WeChatAutoAdd - DEBUG - 找到微信窗口: ● main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 23:20:31,159 - WeChatAutoAdd - INFO - 共找到 2 个微信窗口
2025-07-28 23:20:31,161 - WeChatAutoAdd - INFO - 找到微信主窗口: 微信
2025-07-28 23:20:31,164 - WeChatAutoAdd - WARNING - 方法1截图失败: Error code from Windows: 18 - 没有更多文件。, 尝试方法2
2025-07-28 23:20:31,253 - WeChatAutoAdd - INFO - 使用win32方法截取窗口: 
2025-07-28 23:20:31,256 - WeChatAutoAdd - INFO - win32窗口尺寸: 1x1, 位置: (0, 0)
2025-07-28 23:20:31,257 - WeChatAutoAdd - WARNING - win32 BitBlt操作可能失败
2025-07-28 23:20:31,259 - WeChatAutoAdd - WARNING - 截图可能为纯色图像，标准差: 0.0
2025-07-28 23:20:31,261 - WeChatAutoAdd - WARNING - win32截图内容验证失败
2025-07-28 23:20:31,266 - WeChatAutoAdd - INFO - 保存win32截图: screenshots\window_capture_win32_20250728_232031.png
2025-07-28 23:20:31,269 - WeChatAutoAdd - INFO - win32截图成功
2025-07-28 23:20:31,270 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 23:20:31,271 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 23:20:31,272 - WeChatAutoAdd - INFO - 截图尺寸: 1x1
2025-07-28 23:20:31,272 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=0-1 (高度:1)
2025-07-28 23:20:31,277 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_232031.png
2025-07-28 23:20:31,283 - WeChatAutoAdd - INFO - 底部区域原始检测到 0 个轮廓
2025-07-28 23:20:31,286 - WeChatAutoAdd - WARNING - 底部245像素区域未找到符合条件的按钮
2025-07-28 23:20:31,287 - WeChatAutoAdd - WARNING - 底部区域未找到按钮
2025-07-28 23:20:31,288 - WeChatAutoAdd - ERROR - 无法找到'添加到通讯录'按钮，结束当前流程
2025-07-28 23:20:31,289 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:20:31,290 - modules.wechat_auto_add_simple - ERROR - ❌ 13803774710 添加朋友操作执行失败
2025-07-28 23:20:31,290 - modules.wechat_auto_add_simple - WARNING - ⚠️ 添加朋友失败: 13803774710 - 添加朋友操作失败，可能原因：未找到'添加朋友'窗口或'添加到通讯录'按钮
2025-07-28 23:20:31,291 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:20:35,343 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 4/2978
2025-07-28 23:20:35,343 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18644888251 (陈钦贵)
2025-07-28 23:20:35,343 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
