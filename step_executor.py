#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信自动化步骤执行器
功能：专门处理复杂的步骤执行逻辑，特别是步骤3的简单添加好友功能

版本：1.0.0
作者：AI助手
创建时间：2025-01-28
"""

import time
import logging
import traceback
from typing import Dict, List, Optional, Any
from datetime import datetime

# 导入核心模块
from modules.wechat_auto_add_simple import WeChatAutoAddFriend as SimpleAddFriend
from modules.data_manager import DataManager
from modules.window_manager import WeChatWindowManager

class StepExecutor:
    """步骤执行器"""
    
    def __init__(self, excel_file: str = "添加好友名单.xlsx"):
        self.logger = logging.getLogger(__name__)
        self.excel_file = excel_file
        self.data_manager = DataManager(excel_file)
        self.window_manager = WeChatWindowManager()
        
        # 初始化简单添加好友模块
        self.simple_add_friend = SimpleAddFriend(excel_file)
        
        self.logger.info("✅ 步骤执行器初始化完成")
    
    def execute_simple_add_friend_for_window(self, window: Dict, contacts: List[Dict]) -> Dict:
        """为指定窗口执行简单添加好友流程"""
        try:
            self.logger.info(f"📞 开始为窗口执行简单添加好友流程")
            self.logger.info(f"🖥️ 窗口信息: {window.get('title', 'Unknown')} (句柄: {window.get('hwnd', 'Unknown')})")
            self.logger.info(f"📊 待处理联系人数: {len(contacts)}")
            
            # 统计结果
            results = {
                "total_processed": 0,
                "successful": 0,
                "failed": 0,
                "skipped": 0,
                "details": []
            }
            
            # 确保窗口激活
            hwnd = window.get("hwnd")
            if hwnd:
                if not self.window_manager.activate_window(hwnd):
                    self.logger.error("❌ 无法激活微信窗口")
                    return results
                
                # 强制置顶
                self.window_manager.ensure_window_on_top(hwnd)
                time.sleep(1)
            
            # 设置简单添加好友模块的目标窗口
            self.simple_add_friend.target_window = window
            
            # 处理每个联系人
            for i, contact in enumerate(contacts):
                try:
                    self.logger.info(f"\n📍 处理联系人 {i+1}/{len(contacts)}")
                    
                    phone = contact.get("phone", "")
                    name = contact.get("name", "")
                    
                    if not phone:
                        self.logger.warning("⚠️ 联系人手机号为空，跳过")
                        results["skipped"] += 1
                        continue
                    
                    self.logger.info(f"📞 手机号: {phone}")
                    self.logger.info(f"👤 姓名: {name}")
                    
                    # 检查联系人状态
                    current_status = self.data_manager.get_contact_status(phone)
                    if current_status and current_status.get("status") in ["成功", "success"]:
                        self.logger.info(f"✅ 联系人 {phone} 已成功处理，跳过")
                        results["skipped"] += 1
                        continue
                    
                    # 更新状态为处理中
                    self.data_manager.update_contact_status(
                        phone=phone,
                        status="processing",
                        message="正在处理",
                        window_index=window.get("index", 0)
                    )
                    
                    # 强制置顶窗口（每个联系人处理前）
                    if hwnd:
                        self.window_manager.ensure_window_on_top(hwnd)
                        time.sleep(0.5)
                    
                    # 执行简单添加好友
                    result = self.simple_add_friend.process_single_contact(contact)
                    
                    results["total_processed"] += 1
                    
                    if result and result.get("success", False):
                        self.logger.info(f"✅ 联系人 {phone} 处理成功")
                        results["successful"] += 1
                        
                        # 更新状态为成功
                        self.data_manager.update_contact_status(
                            phone=phone,
                            status="success",
                            message=result.get("message", "添加成功"),
                            window_index=window.get("index", 0)
                        )
                        
                    else:
                        self.logger.error(f"❌ 联系人 {phone} 处理失败")
                        results["failed"] += 1
                        
                        # 更新状态为失败
                        error_message = result.get("message", "添加失败") if result else "处理异常"
                        self.data_manager.update_contact_status(
                            phone=phone,
                            status="error",
                            message=error_message,
                            window_index=window.get("index", 0)
                        )
                    
                    # 记录详细结果
                    results["details"].append({
                        "phone": phone,
                        "name": name,
                        "success": result.get("success", False) if result else False,
                        "message": result.get("message", "处理异常") if result else "处理异常"
                    })
                    
                    # 联系人间延迟
                    time.sleep(2)
                    
                except Exception as e:
                    self.logger.error(f"❌ 处理联系人 {contact.get('phone', 'Unknown')} 异常: {e}")
                    self.logger.error(traceback.format_exc())
                    results["failed"] += 1
                    
                    # 更新状态为失败
                    if contact.get("phone"):
                        self.data_manager.update_contact_status(
                            phone=contact["phone"],
                            status="error",
                            message=f"处理异常: {str(e)}",
                            window_index=window.get("index", 0)
                        )
            
            # 打印处理结果
            self.logger.info(f"\n📊 窗口联系人处理结果:")
            self.logger.info(f"  📞 总处理数: {results['total_processed']}")
            self.logger.info(f"  ✅ 成功数: {results['successful']}")
            self.logger.info(f"  ❌ 失败数: {results['failed']}")
            self.logger.info(f"  ⏭️ 跳过数: {results['skipped']}")
            
            if results['total_processed'] > 0:
                success_rate = results['successful'] / results['total_processed'] * 100
                self.logger.info(f"  📈 成功率: {success_rate:.1f}%")
            
            return results
            
        except Exception as e:
            self.logger.error(f"❌ 简单添加好友流程执行异常: {e}")
            self.logger.error(traceback.format_exc())
            return results
    
    def execute_batch_processing(self, windows: List[Dict], contacts: List[Dict], 
                                contacts_per_window: int = 10) -> Dict:
        """执行批量处理：将联系人分配到不同窗口"""
        try:
            self.logger.info("🔄 开始批量处理模式")
            self.logger.info(f"📊 总窗口数: {len(windows)}")
            self.logger.info(f"📞 总联系人数: {len(contacts)}")
            self.logger.info(f"📋 每窗口处理联系人数: {contacts_per_window}")
            
            # 总体统计
            total_results = {
                "total_windows": len(windows),
                "total_contacts": len(contacts),
                "processed_windows": 0,
                "successful_windows": 0,
                "total_processed": 0,
                "total_successful": 0,
                "total_failed": 0,
                "total_skipped": 0,
                "window_results": []
            }
            
            # 分配联系人到窗口
            contact_chunks = []
            for i in range(0, len(contacts), contacts_per_window):
                chunk = contacts[i:i + contacts_per_window]
                contact_chunks.append(chunk)
            
            # 确保每个窗口都有联系人处理
            while len(contact_chunks) < len(windows) and contacts:
                # 如果窗口数多于联系人块数，复制联系人块
                contact_chunks.append(contacts[:min(contacts_per_window, len(contacts))])
            
            # 为每个窗口执行处理
            for window_index, window in enumerate(windows):
                try:
                    self.logger.info(f"\n{'='*60}")
                    self.logger.info(f"🎯 处理窗口 {window_index + 1}/{len(windows)}")
                    self.logger.info(f"{'='*60}")
                    
                    # 获取该窗口的联系人
                    if window_index < len(contact_chunks):
                        window_contacts = contact_chunks[window_index]
                    else:
                        self.logger.warning(f"⚠️ 窗口 {window_index + 1} 没有分配到联系人")
                        continue
                    
                    # 添加窗口索引到窗口信息
                    window["index"] = window_index
                    
                    # 执行该窗口的联系人处理
                    window_result = self.execute_simple_add_friend_for_window(window, window_contacts)
                    
                    # 更新总体统计
                    total_results["processed_windows"] += 1
                    total_results["total_processed"] += window_result["total_processed"]
                    total_results["total_successful"] += window_result["successful"]
                    total_results["total_failed"] += window_result["failed"]
                    total_results["total_skipped"] += window_result["skipped"]
                    
                    if window_result["successful"] > 0:
                        total_results["successful_windows"] += 1
                    
                    # 记录窗口结果
                    window_result["window_index"] = window_index + 1
                    window_result["window_title"] = window.get("title", "Unknown")
                    total_results["window_results"].append(window_result)
                    
                    self.logger.info(f"✅ 窗口 {window_index + 1} 处理完成")
                    
                    # 窗口间延迟
                    if window_index < len(windows) - 1:
                        self.logger.info("⏳ 窗口切换延迟...")
                        time.sleep(3)
                    
                except Exception as e:
                    self.logger.error(f"❌ 窗口 {window_index + 1} 处理异常: {e}")
                    self.logger.error(traceback.format_exc())
            
            # 打印总体结果
            self.logger.info(f"\n{'='*80}")
            self.logger.info("📊 批量处理总体结果:")
            self.logger.info(f"  🖥️ 处理窗口数: {total_results['processed_windows']}/{total_results['total_windows']}")
            self.logger.info(f"  ✅ 成功窗口数: {total_results['successful_windows']}")
            self.logger.info(f"  📞 总处理联系人: {total_results['total_processed']}")
            self.logger.info(f"  ✅ 总成功数: {total_results['total_successful']}")
            self.logger.info(f"  ❌ 总失败数: {total_results['total_failed']}")
            self.logger.info(f"  ⏭️ 总跳过数: {total_results['total_skipped']}")
            
            if total_results['total_processed'] > 0:
                overall_success_rate = total_results['total_successful'] / total_results['total_processed'] * 100
                self.logger.info(f"  📈 总体成功率: {overall_success_rate:.1f}%")
            
            self.logger.info(f"{'='*80}")
            
            return total_results
            
        except Exception as e:
            self.logger.error(f"❌ 批量处理执行异常: {e}")
            self.logger.error(traceback.format_exc())
            return total_results
