2025-07-28 14:31:34,392 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 14:31:34,393 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 14:31:34,394 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-28 14:31:34,395 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 14:31:34,396 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-28 14:31:34,397 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 14:31:34,397 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 14:31:34,398 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 14:31:34,399 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 14:31:34,400 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 14:31:34,402 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 14:31:34,406 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 14:31:34,411 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250728_143134.log
2025-07-28 14:31:34,412 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-28 14:31:34,413 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-28 14:31:34,413 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-28 14:31:34,414 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-28 14:31:34,414 - __main__ - INFO - 📅 当前北京时间: 2025-07-28 14:31:34
2025-07-28 14:31:34,415 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-28 14:31:34,415 - __main__ - INFO - 📅 启动时间: 2025-07-28 14:31:34
2025-07-28 14:31:34,415 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-28 14:31:34,416 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 14:31:34,963 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 14:31:34,964 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 14:31:35,491 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 14:31:35,492 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 14:31:35,492 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-28 14:31:35,493 - __main__ - INFO - ✅ 找到 2 个微信窗口
2025-07-28 14:31:35,493 - __main__ - INFO -   窗口 1: 微信 (句柄: 197994)
2025-07-28 14:31:35,493 - __main__ - INFO -   窗口 2: 微信 (句柄: 525668)
2025-07-28 14:31:35,494 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-28 14:31:36,462 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-28 14:31:36,462 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-28 14:31:36,463 - __main__ - INFO - 📋 待处理联系人数: 3003
2025-07-28 14:31:36,463 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-28 14:31:36,463 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 3003
2025-07-28 14:31:36,464 - __main__ - INFO - 
============================================================
2025-07-28 14:31:36,464 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口
2025-07-28 14:31:36,464 - __main__ - INFO - ============================================================
2025-07-28 14:31:36,465 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-28 14:31:36,465 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-28 14:31:36,465 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-28 14:31:36,465 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-28 14:31:36,466 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 14:31:36,771 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 14:31:36,772 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 14:31:36,772 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 14:31:36,772 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 14:31:36,773 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 14:31:36,773 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 14:31:36,773 - modules.window_manager - INFO - 📏 当前窗口位置: (206, 225), 大小: 726x650
2025-07-28 14:31:36,774 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 14:31:36,774 - modules.window_manager - INFO - 🔄 尝试调整窗口大小和位置...
2025-07-28 14:31:37,076 - modules.window_manager - INFO - 📊 验证结果 - 当前位置: (0, 0), 实际大小: 726x650
2025-07-28 14:31:37,077 - modules.window_manager - INFO - 📊 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 14:31:37,077 - modules.window_manager - INFO - 📊 差异: 位置(0, 0), 大小(3, 0)
2025-07-28 14:31:37,077 - modules.window_manager - INFO - ✅ 窗口大小和位置验证通过
2025-07-28 14:31:37,078 - modules.window_manager - INFO - ✅ 虽然API可能报告失败，但窗口实际已调整到目标大小和位置
2025-07-28 14:31:37,078 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 14:31:37,280 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 14:31:37,280 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 14:31:37,281 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-28 14:31:37,281 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 14:31:37,584 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 14:31:37,585 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 14:31:37,585 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 14:31:37,586 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 14:31:37,586 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 14:31:37,586 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 14:31:37,587 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 14:31:37,587 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 14:31:37,587 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 14:31:37,588 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 14:31:37,790 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 14:31:37,790 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 14:31:37,793 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-28 14:31:38,094 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 14:31:38,094 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 14:31:38,095 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 14:31:38,096 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 14:31:38,096 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 14:31:38,097 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 14:31:38,097 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-28 14:31:39,097 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-28 14:31:39,098 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-28 14:31:39,098 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-28 14:31:39,099 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-28 14:31:39,099 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-28 14:31:39,099 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-28 14:31:39,100 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-28 14:31:39,100 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-28 14:31:39,300 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-28 14:31:39,308 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-28 14:31:41,689 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-28 14:31:41,689 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-28 14:31:41,693 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.9 秒...
2025-07-28 14:31:43,547 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-28 14:31:43,748 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-28 14:31:43,749 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-28 14:31:46,121 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-28 14:31:46,121 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-28 14:31:46,122 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.6 秒...
2025-07-28 14:31:47,725 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-28 14:31:47,925 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-28 14:31:47,926 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-28 14:31:50,293 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-28 14:31:50,294 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-28 14:31:50,294 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.5 秒...
2025-07-28 14:31:51,822 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-28 14:31:52,022 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-28 14:31:52,023 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-28 14:31:54,404 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-28 14:31:54,404 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-28 14:31:54,404 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.9 秒...
2025-07-28 14:31:56,280 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-28 14:31:56,481 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-28 14:31:56,482 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-28 14:31:58,854 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-28 14:31:58,854 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-28 14:31:58,854 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-28 14:31:58,855 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-28 14:31:58,855 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 14:31:58,857 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 14:31:58,857 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 14:31:58,858 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1379192, 进程: Weixin.exe)
2025-07-28 14:31:58,859 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 14:31:58,860 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 14:31:58,863 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-28 14:31:58,863 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 1379192)
2025-07-28 14:31:58,864 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1379192) - 增强版
2025-07-28 14:31:59,169 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 14:31:59,169 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 14:31:59,170 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-28 14:31:59,170 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-28 14:31:59,170 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-28 14:31:59,171 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-28 14:31:59,374 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-28 14:31:59,374 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-28 14:31:59,576 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 14:31:59,576 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 14:31:59,577 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-28 14:31:59,577 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-28 14:31:59,577 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-28 14:31:59,578 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-28 14:31:59,578 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-28 14:32:00,578 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-28 14:32:00,579 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 14:32:00,580 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 14:32:00,581 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 14:32:00,581 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1379192, 进程: Weixin.exe)
2025-07-28 14:32:00,582 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 14:32:00,582 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 14:32:00,586 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-28 14:32:00,587 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-28 14:32:00,588 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-28 14:32:00,588 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-28 14:32:00,589 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-28 14:32:00,589 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 14:32:00,896 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 14:32:00,896 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 14:32:00,897 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 14:32:00,897 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 14:32:00,898 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 14:32:00,898 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 14:32:00,898 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 14:32:00,899 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 14:32:00,899 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 14:32:00,899 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 14:32:01,102 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 14:32:01,102 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 14:32:01,105 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-28 14:32:01,406 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 14:32:01,406 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-28 14:32:02,406 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-28 14:32:02,407 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-28 14:32:02,407 - __main__ - INFO - 📊 当前窗口分配到 5 个联系人
2025-07-28 14:32:02,407 - step_executor - INFO - 📞 开始为窗口执行简单添加好友流程
2025-07-28 14:32:02,408 - step_executor - INFO - 🖥️ 窗口信息: 微信 (句柄: 197994)
2025-07-28 14:32:02,408 - step_executor - INFO - 📊 待处理联系人数: 5
2025-07-28 14:32:02,408 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 14:32:02,711 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 14:32:02,712 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 14:32:02,712 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 14:32:02,712 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 14:32:02,713 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 14:32:02,713 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 14:32:02,713 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 14:32:02,714 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 14:32:02,714 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 14:32:02,714 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 14:32:02,916 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 14:32:02,916 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 14:32:02,917 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-28 14:32:02,917 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 14:32:03,221 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 14:32:03,221 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 14:32:03,221 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 14:32:03,222 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 14:32:03,222 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 14:32:03,222 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 14:32:03,223 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 14:32:03,223 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 14:32:03,223 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 14:32:03,223 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 14:32:03,424 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 14:32:03,425 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 14:32:03,426 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-28 14:32:03,727 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 14:32:04,727 - step_executor - INFO - 
📍 处理联系人 1/5
2025-07-28 14:32:04,728 - step_executor - INFO - 📞 手机号: 13046777941
2025-07-28 14:32:04,728 - step_executor - INFO - 👤 姓名: 徐金超
2025-07-28 14:32:04,728 - step_executor - ERROR - ❌ 处理联系人 13046777941 异常: 'DataManager' object has no attribute 'get_contact_status'
2025-07-28 14:32:04,730 - step_executor - ERROR - Traceback (most recent call last):
  File "d:\程序-测试\微信7.28 - 副本 (2)\step_executor.py", line 84, in execute_simple_add_friend_for_window
    current_status = self.data_manager.get_contact_status(phone)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DataManager' object has no attribute 'get_contact_status'

2025-07-28 14:32:04,731 - step_executor - ERROR - ❌ 简单添加好友流程执行异常: 'DataManager' object has no attribute 'update_contact_status'
2025-07-28 14:32:04,732 - step_executor - ERROR - Traceback (most recent call last):
  File "d:\程序-测试\微信7.28 - 副本 (2)\step_executor.py", line 84, in execute_simple_add_friend_for_window
    current_status = self.data_manager.get_contact_status(phone)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DataManager' object has no attribute 'get_contact_status'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "d:\程序-测试\微信7.28 - 副本 (2)\step_executor.py", line 151, in execute_simple_add_friend_for_window
    self.data_manager.update_contact_status(
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DataManager' object has no attribute 'update_contact_status'. Did you mean: 'update_phone_status'?

2025-07-28 14:32:04,734 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-28 14:32:04,736 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 14:32:05,040 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 14:32:05,040 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 14:32:05,040 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 14:32:05,041 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 14:32:05,041 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 14:32:05,041 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 14:32:05,042 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 14:32:05,042 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 14:32:05,042 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 14:32:05,043 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 14:32:05,246 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 14:32:05,246 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 14:32:05,249 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-28 14:32:05,549 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 14:32:05,550 - __main__ - INFO - ✅ 步骤3：简单添加好友完成 - 无联系人处理
2025-07-28 14:32:06,551 - __main__ - INFO - 📍 当前执行步骤: IMAGE_RECOGNITION (步骤 4)
2025-07-28 14:32:06,551 - __main__ - INFO - 🖼️ 步骤4：图像识别添加 - 窗口 1
2025-07-28 14:32:06,552 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 14:32:06,555 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 14:32:06,556 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-28 14:32:06,557 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 14:32:06,558 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 14:32:06,559 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-28 14:32:06,560 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-28 14:32:07,061 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-28 14:32:07,062 - WeChatAutoAdd - INFO - 窗口位置: (0, 0), 尺寸: 328x454
2025-07-28 14:32:07,215 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差42.65, 边缘比例0.0862
2025-07-28 14:32:07,266 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_143207.png
2025-07-28 14:32:07,268 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 14:32:07,269 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 14:32:07,271 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-28 14:32:07,271 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-28 14:32:07,272 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-28 14:32:07,292 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_143207.png
2025-07-28 14:32:07,292 - WeChatAutoAdd - INFO - 底部区域原始检测到 286 个轮廓
2025-07-28 14:32:07,296 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(187,453), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,297 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(161,453), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,298 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,453), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,298 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,453), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:32:07,299 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(105,453), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,300 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(87,453), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,301 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(76,453), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,303 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(229,452), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 14:32:07,303 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(225,451), 尺寸1x3, 长宽比0.33, 面积3
2025-07-28 14:32:07,304 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,451), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 14:32:07,305 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(127,451), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,305 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(122,451), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,309 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(234,450), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:32:07,311 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(148,450), 尺寸6x4, 长宽比1.50, 面积24
2025-07-28 14:32:07,313 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(153,449), 尺寸4x5, 长宽比0.80, 面积20
2025-07-28 14:32:07,314 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(143,449), 尺寸5x5, 长宽比1.00, 面积25
2025-07-28 14:32:07,314 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(101,449), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,315 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(83,449), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 14:32:07,316 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(81,449), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,319 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(75,449), 尺寸11x5, 长宽比2.20, 面积55
2025-07-28 14:32:07,319 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(232,448), 尺寸7x6, 长宽比1.17, 面积42
2025-07-28 14:32:07,320 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(179,448), 尺寸3x6, 长宽比0.50, 面积18
2025-07-28 14:32:07,323 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,448), 尺寸1x3, 长宽比0.33, 面积3
2025-07-28 14:32:07,324 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(132,448), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:32:07,325 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,448), 尺寸14x6, 长宽比2.33, 面积84
2025-07-28 14:32:07,326 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(78,448), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:32:07,327 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(204,447), 尺寸27x7, 长宽比3.86, 面积189
2025-07-28 14:32:07,328 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(205,447), 尺寸3x1, 长宽比3.00, 面积3
2025-07-28 14:32:07,329 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(182,447), 尺寸22x7, 长宽比3.14, 面积154
2025-07-28 14:32:07,330 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(155,447), 尺寸23x7, 长宽比3.29, 面积161
2025-07-28 14:32:07,330 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(147,447), 尺寸7x1, 长宽比7.00, 面积7
2025-07-28 14:32:07,332 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(143,447), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 14:32:07,335 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(137,447), 尺寸3x7, 长宽比0.43, 面积21
2025-07-28 14:32:07,342 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(96,447), 尺寸11x7, 长宽比1.57, 面积77
2025-07-28 14:32:07,343 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(76,447), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,344 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(103,446), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,345 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(80,445), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 14:32:07,346 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(101,444), 尺寸1x4, 长宽比0.25, 面积4
2025-07-28 14:32:07,347 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(75,444), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,348 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(105,440), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:32:07,355 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(97,440), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,356 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(75,439), 尺寸5x6, 长宽比0.83, 面积30
2025-07-28 14:32:07,357 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(105,438), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:32:07,359 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(97,438), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,360 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(83,438), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:32:07,360 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(80,438), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:32:07,361 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(76,437), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,362 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(241,436), 尺寸3x1, 长宽比3.00, 面积3
2025-07-28 14:32:07,365 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(103,436), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,366 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(99,436), 尺寸8x10, 长宽比0.80, 面积80
2025-07-28 14:32:07,368 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(98,436), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,371 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(96,436), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,372 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(238,435), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:32:07,372 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(218,435), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:32:07,373 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,435), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 14:32:07,374 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(210,435), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:32:07,374 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(152,435), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 14:32:07,375 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(88,435), 尺寸3x1, 长宽比3.00, 面积3
2025-07-28 14:32:07,377 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(103,434), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,378 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(100,433), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,379 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(96,433), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:32:07,380 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(83,433), 尺寸17x21, 长宽比0.81, 面积357
2025-07-28 14:32:07,381 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(244,432), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 14:32:07,382 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(240,432), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,387 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(76,432), 尺寸5x5, 长宽比1.00, 面积25
2025-07-28 14:32:07,388 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(101,431), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,389 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(91,431), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,390 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(241,430), 尺寸2x5, 长宽比0.40, 面积10
2025-07-28 14:32:07,390 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,430), 尺寸3x5, 长宽比0.60, 面积15
2025-07-28 14:32:07,391 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(103,430), 尺寸4x4, 长宽比1.00, 面积16
2025-07-28 14:32:07,391 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(98,430), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,392 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(83,430), 尺寸4x3, 长宽比1.33, 面积12
2025-07-28 14:32:07,392 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(78,430), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:32:07,393 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(245,429), 尺寸6x8, 长宽比0.75, 面积48
2025-07-28 14:32:07,395 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(91,429), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,396 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(89,429), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,397 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(103,428), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,399 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(95,428), 尺寸2x4, 长宽比0.50, 面积8
2025-07-28 14:32:07,401 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(85,428), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,406 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(80,428), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 14:32:07,407 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(78,428), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,408 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,427), 尺寸12x10, 长宽比1.20, 面积120
2025-07-28 14:32:07,420 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(233,427), 尺寸14x10, 长宽比1.40, 面积140
2025-07-28 14:32:07,421 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,427), 尺寸7x11, 长宽比0.64, 面积77
2025-07-28 14:32:07,422 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(106,427), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:32:07,422 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(93,427), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,423 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,426), 尺寸5x11, 长宽比0.45, 面积55
2025-07-28 14:32:07,423 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,426), 尺寸17x11, 长宽比1.55, 面积187
2025-07-28 14:32:07,427 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=87.1 (阈值:60)
2025-07-28 14:32:07,432 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(104,426), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,441 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(101,426), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,443 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(98,426), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 14:32:07,445 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(95,426), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,445 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(91,426), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,446 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(88,426), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 14:32:07,448 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,426), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,449 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(83,426), 尺寸1x3, 长宽比0.33, 面积3
2025-07-28 14:32:07,451 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(81,426), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,455 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(77,426), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,456 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(185,425), 尺寸24x13, 长宽比1.85, 面积312
2025-07-28 14:32:07,458 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=82.9 (阈值:60)
2025-07-28 14:32:07,459 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,425), 尺寸7x9, 长宽比0.78, 面积63
2025-07-28 14:32:07,459 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(153,425), 尺寸15x13, 长宽比1.15, 面积195
2025-07-28 14:32:07,461 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=88.5 (阈值:60)
2025-07-28 14:32:07,462 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,425), 尺寸14x13, 长宽比1.08, 面积182
2025-07-28 14:32:07,463 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=78.1 (阈值:60)
2025-07-28 14:32:07,463 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(73,423), 尺寸37x31, 长宽比1.19, 面积1147
2025-07-28 14:32:07,464 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(99,391), 尺寸6x2, 长宽比3.00, 面积12
2025-07-28 14:32:07,465 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(231,389), 尺寸8x3, 长宽比2.67, 面积24
2025-07-28 14:32:07,466 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(224,389), 尺寸4x1, 长宽比4.00, 面积4
2025-07-28 14:32:07,467 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,389), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,468 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(239,388), 尺寸9x2, 长宽比4.50, 面积18
2025-07-28 14:32:07,470 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(224,388), 尺寸9x4, 长宽比2.25, 面积36
2025-07-28 14:32:07,473 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(124,388), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:32:07,510 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(101,388), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 14:32:07,550 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,387), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:32:07,563 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(151,387), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,564 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(230,386), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,566 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(225,386), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 14:32:07,569 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(85,386), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:32:07,570 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(232,385), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 14:32:07,571 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(175,385), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:32:07,572 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,384), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 14:32:07,592 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(170,384), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:32:07,593 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(90,384), 尺寸8x8, 长宽比1.00, 面积64
2025-07-28 14:32:07,594 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(181,382), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 14:32:07,594 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(234,381), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,595 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(226,381), 尺寸4x5, 长宽比0.80, 面积20
2025-07-28 14:32:07,595 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(221,381), 尺寸4x7, 长宽比0.57, 面积28
2025-07-28 14:32:07,596 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(180,381), 尺寸22x11, 长宽比2.00, 面积242
2025-07-28 14:32:07,596 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,381), 尺寸11x11, 长宽比1.00, 面积121
2025-07-28 14:32:07,597 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,381), 尺寸8x3, 长宽比2.67, 面积24
2025-07-28 14:32:07,599 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(91,381), 尺寸3x1, 长宽比3.00, 面积3
2025-07-28 14:32:07,601 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,379), 尺寸11x8, 长宽比1.38, 面积88
2025-07-28 14:32:07,603 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(203,379), 尺寸22x13, 长宽比1.69, 面积286
2025-07-28 14:32:07,605 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(181,379), 尺寸10x6, 长宽比1.67, 面积60
2025-07-28 14:32:07,606 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,379), 尺寸33x13, 长宽比2.54, 面积429
2025-07-28 14:32:07,607 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,379), 尺寸29x13, 长宽比2.23, 面积377
2025-07-28 14:32:07,608 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(91,379), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:32:07,608 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(91,377), 尺寸3x1, 长宽比3.00, 面积3
2025-07-28 14:32:07,609 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(99,376), 尺寸2x5, 长宽比0.40, 面积10
2025-07-28 14:32:07,610 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(90,374), 尺寸4x2, 长宽比2.00, 面积8
2025-07-28 14:32:07,610 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(19,373), 尺寸22x22, 长宽比1.00, 面积484
2025-07-28 14:32:07,612 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(92,371), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,612 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(84,370), 尺寸2x12, 长宽比0.17, 面积24
2025-07-28 14:32:07,613 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(218,367), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:32:07,614 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,367), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 14:32:07,615 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(210,367), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:32:07,615 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(152,367), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 14:32:07,616 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(99,367), 尺寸1x3, 长宽比0.33, 面积3
2025-07-28 14:32:07,618 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(95,367), 尺寸12x24, 长宽比0.50, 面积288
2025-07-28 14:32:07,619 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(85,367), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 14:32:07,621 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(252,365), 尺寸1x3, 长宽比0.33, 面积3
2025-07-28 14:32:07,622 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(96,363), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 14:32:07,623 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,362), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:32:07,623 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,359), 尺寸11x10, 长宽比1.10, 面积110
2025-07-28 14:32:07,624 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(233,359), 尺寸18x10, 长宽比1.80, 面积180
2025-07-28 14:32:07,625 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,359), 尺寸7x11, 长宽比0.64, 面积77
2025-07-28 14:32:07,626 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,358), 尺寸5x11, 长宽比0.45, 面积55
2025-07-28 14:32:07,627 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,358), 尺寸17x11, 长宽比1.55, 面积187
2025-07-28 14:32:07,628 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=87.1 (阈值:60)
2025-07-28 14:32:07,628 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(185,357), 尺寸24x13, 长宽比1.85, 面积312
2025-07-28 14:32:07,630 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=82.9 (阈值:60)
2025-07-28 14:32:07,630 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,357), 尺寸7x9, 长宽比0.78, 面积63
2025-07-28 14:32:07,631 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(153,357), 尺寸15x13, 长宽比1.15, 面积195
2025-07-28 14:32:07,633 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=88.5 (阈值:60)
2025-07-28 14:32:07,639 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,357), 尺寸14x13, 长宽比1.08, 面积182
2025-07-28 14:32:07,641 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=78.1 (阈值:60)
2025-07-28 14:32:07,643 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,355), 尺寸38x37, 长宽比1.03, 面积1406
2025-07-28 14:32:07,644 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=84.3 (阈值:60)
2025-07-28 14:32:07,645 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(29,338), 尺寸6x8, 长宽比0.75, 面积48
2025-07-28 14:32:07,647 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(31,337), 尺寸6x9, 长宽比0.67, 面积54
2025-07-28 14:32:07,648 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,337), 尺寸5x9, 长宽比0.56, 面积45
2025-07-28 14:32:07,648 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(30,332), 尺寸11x3, 长宽比3.67, 面积33
2025-07-28 14:32:07,649 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(19,332), 尺寸6x3, 长宽比2.00, 面积18
2025-07-28 14:32:07,650 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(27,325), 尺寸4x7, 长宽比0.57, 面积28
2025-07-28 14:32:07,654 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(313,323), 尺寸2x131, 长宽比0.02, 面积262
2025-07-28 14:32:07,655 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(176,323), 尺寸4x1, 长宽比4.00, 面积4
2025-07-28 14:32:07,656 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(153,323), 尺寸4x1, 长宽比4.00, 面积4
2025-07-28 14:32:07,657 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(168,321), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,658 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,321), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,659 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,321), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,660 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,320), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:32:07,661 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(247,320), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 14:32:07,662 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(244,320), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 14:32:07,663 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(227,320), 尺寸9x3, 长宽比3.00, 面积27
2025-07-28 14:32:07,663 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(236,319), 尺寸7x5, 长宽比1.40, 面积35
2025-07-28 14:32:07,664 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(89,319), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,664 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(232,318), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 14:32:07,665 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(227,318), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,665 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(199,318), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,666 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(225,317), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,666 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(176,317), 尺寸7x9, 长宽比0.78, 面积63
2025-07-28 14:32:07,668 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,317), 尺寸4x4, 长宽比1.00, 面积16
2025-07-28 14:32:07,671 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(146,317), 尺寸4x4, 长宽比1.00, 面积16
2025-07-28 14:32:07,671 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(173,316), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,672 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(150,316), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,673 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(242,315), 尺寸1x4, 长宽比0.25, 面积4
2025-07-28 14:32:07,674 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,315), 尺寸4x4, 长宽比1.00, 面积16
2025-07-28 14:32:07,674 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(168,315), 尺寸17x11, 长宽比1.55, 面积187
2025-07-28 14:32:07,675 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=315 (距底部154像素区域)
2025-07-28 14:32:07,676 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 14:32:07,678 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(168,315), 尺寸4x2, 长宽比2.00, 面积8
2025-07-28 14:32:07,678 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,315), 尺寸22x11, 长宽比2.00, 面积242
2025-07-28 14:32:07,679 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=315 (距底部154像素区域)
2025-07-28 14:32:07,680 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 14:32:07,680 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,315), 尺寸4x2, 长宽比2.00, 面积8
2025-07-28 14:32:07,681 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(126,315), 尺寸18x11, 长宽比1.64, 面积198
2025-07-28 14:32:07,682 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=315 (距底部154像素区域)
2025-07-28 14:32:07,682 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 14:32:07,683 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(195,314), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:32:07,692 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(236,313), 尺寸5x1, 长宽比5.00, 面积5
2025-07-28 14:32:07,694 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,313), 尺寸24x11, 长宽比2.18, 面积264
2025-07-28 14:32:07,696 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=313 (距底部154像素区域)
2025-07-28 14:32:07,698 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 14:32:07,699 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(218,312), 尺寸17x12, 长宽比1.42, 面积204
2025-07-28 14:32:07,700 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=312 (距底部154像素区域)
2025-07-28 14:32:07,701 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 14:32:07,703 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(197,312), 尺寸10x8, 长宽比1.25, 面积80
2025-07-28 14:32:07,706 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=312 (距底部154像素区域)
2025-07-28 14:32:07,707 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 14:32:07,707 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,312), 尺寸10x10, 长宽比1.00, 面积100
2025-07-28 14:32:07,708 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=312 (距底部154像素区域)
2025-07-28 14:32:07,708 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 14:32:07,709 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(207,311), 尺寸11x13, 长宽比0.85, 面积143
2025-07-28 14:32:07,710 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,311), 尺寸13x3, 长宽比4.33, 面积39
2025-07-28 14:32:07,711 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(93,311), 尺寸3x7, 长宽比0.43, 面积21
2025-07-28 14:32:07,712 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(96,308), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 14:32:07,712 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(93,308), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:32:07,713 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(322,303), 尺寸6x17, 长宽比0.35, 面积102
2025-07-28 14:32:07,713 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(96,302), 尺寸6x2, 长宽比3.00, 面积12
2025-07-28 14:32:07,714 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(218,299), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:32:07,714 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,299), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 14:32:07,715 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(210,299), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:32:07,716 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(98,292), 尺寸2x6, 长宽比0.33, 面积12
2025-07-28 14:32:07,723 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(96,292), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:32:07,724 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(233,291), 尺寸26x10, 长宽比2.60, 面积260
2025-07-28 14:32:07,725 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=291 (距底部154像素区域)
2025-07-28 14:32:07,726 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 14:32:07,726 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,291), 尺寸7x11, 长宽比0.64, 面积77
2025-07-28 14:32:07,728 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(136,291), 尺寸1x4, 长宽比0.25, 面积4
2025-07-28 14:32:07,729 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,290), 尺寸5x11, 长宽比0.45, 面积55
2025-07-28 14:32:07,730 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,290), 尺寸5x11, 长宽比0.45, 面积55
2025-07-28 14:32:07,730 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(99,290), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:32:07,731 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(94,290), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,731 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(202,289), 尺寸7x13, 长宽比0.54, 面积91
2025-07-28 14:32:07,732 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(185,289), 尺寸8x13, 长宽比0.62, 面积104
2025-07-28 14:32:07,734 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,289), 尺寸7x9, 长宽比0.78, 面积63
2025-07-28 14:32:07,736 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(153,289), 尺寸15x13, 长宽比1.15, 面积195
2025-07-28 14:32:07,738 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=289 (距底部154像素区域)
2025-07-28 14:32:07,739 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 14:32:07,740 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(136,289), 尺寸18x13, 长宽比1.38, 面积234
2025-07-28 14:32:07,740 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=289 (距底部154像素区域)
2025-07-28 14:32:07,741 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 14:32:07,741 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,289), 尺寸14x13, 长宽比1.08, 面积182
2025-07-28 14:32:07,743 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=289 (距底部154像素区域)
2025-07-28 14:32:07,744 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 14:32:07,744 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(89,289), 尺寸21x36, 长宽比0.58, 面积756
2025-07-28 14:32:07,745 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(93,287), 尺寸11x2, 长宽比5.50, 面积22
2025-07-28 14:32:07,745 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,287), 尺寸23x38, 长宽比0.61, 面积874
2025-07-28 14:32:07,746 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(316,282), 尺寸12x31, 长宽比0.39, 面积372
2025-07-28 14:32:07,746 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(20,279), 尺寸20x18, 长宽比1.11, 面积360
2025-07-28 14:32:07,747 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=279 (距底部154像素区域)
2025-07-28 14:32:07,748 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 14:32:07,749 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(223,254), 尺寸13x2, 长宽比6.50, 面积26
2025-07-28 14:32:07,749 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(125,254), 尺寸4x1, 长宽比4.00, 面积4
2025-07-28 14:32:07,750 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(232,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,753 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(144,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,755 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(253,252), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:32:07,755 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,252), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:32:07,756 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(247,252), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 14:32:07,756 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(191,252), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 14:32:07,757 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(316,250), 尺寸12x3, 长宽比4.00, 面积36
2025-07-28 14:32:07,757 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(244,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,758 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(162,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 14:32:07,759 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,247), 尺寸5x4, 长宽比1.25, 面积20
2025-07-28 14:32:07,760 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(178,247), 尺寸12x9, 长宽比1.33, 面积108
2025-07-28 14:32:07,761 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,246), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,761 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,246), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 14:32:07,762 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,246), 尺寸2x5, 长宽比0.40, 面积10
2025-07-28 14:32:07,762 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,244), 尺寸11x10, 长宽比1.10, 面积110
2025-07-28 14:32:07,763 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(197,244), 尺寸17x12, 长宽比1.42, 面积204
2025-07-28 14:32:07,764 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(185,244), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 14:32:07,765 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,244), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:32:07,766 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(123,244), 尺寸55x12, 长宽比4.58, 面积660
2025-07-28 14:32:07,767 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,244), 尺寸8x11, 长宽比0.73, 面积88
2025-07-28 14:32:07,770 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(232,243), 尺寸14x13, 长宽比1.08, 面积182
2025-07-28 14:32:07,772 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(209,243), 尺寸12x13, 长宽比0.92, 面积156
2025-07-28 14:32:07,772 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(245,232), 尺寸6x1, 长宽比6.00, 面积6
2025-07-28 14:32:07,773 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(241,232), 尺寸3x1, 长宽比3.00, 面积3
2025-07-28 14:32:07,773 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(132,232), 尺寸3x1, 长宽比3.00, 面积3
2025-07-28 14:32:07,773 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(152,231), 尺寸8x4, 长宽比2.00, 面积32
2025-07-28 14:32:07,774 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(124,231), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:32:07,774 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(126,230), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:32:07,775 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(239,229), 尺寸1x3, 长宽比0.33, 面积3
2025-07-28 14:32:07,776 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(19,229), 尺寸22x22, 长宽比1.00, 面积484
2025-07-28 14:32:07,777 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(124,228), 尺寸22x7, 长宽比3.14, 面积154
2025-07-28 14:32:07,778 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(241,226), 尺寸2x5, 长宽比0.40, 面积10
2025-07-28 14:32:07,778 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(139,225), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:32:07,779 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(133,225), 尺寸4x2, 长宽比2.00, 面积8
2025-07-28 14:32:07,779 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,225), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:32:07,780 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(234,224), 尺寸4x8, 长宽比0.50, 面积32
2025-07-28 14:32:07,781 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(239,223), 尺寸20x10, 长宽比2.00, 面积200
2025-07-28 14:32:07,783 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,223), 尺寸2x10, 长宽比0.20, 面积20
2025-07-28 14:32:07,789 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,223), 尺寸4x12, 长宽比0.33, 面积48
2025-07-28 14:32:07,790 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(316,222), 尺寸1x4, 长宽比0.25, 面积4
2025-07-28 14:32:07,791 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,222), 尺寸27x11, 长宽比2.45, 面积297
2025-07-28 14:32:07,796 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=96.5 (阈值:60)
2025-07-28 14:32:07,798 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(316,221), 尺寸5x17, 长宽比0.29, 面积85
2025-07-28 14:32:07,799 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,221), 尺寸11x5, 长宽比2.20, 面积55
2025-07-28 14:32:07,804 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(126,221), 尺寸6x1, 长宽比6.00, 面积6
2025-07-28 14:32:07,806 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,221), 尺寸12x8, 长宽比1.50, 面积96
2025-07-28 14:32:07,808 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=97.1 (阈值:60)
2025-07-28 14:32:07,809 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(316,218), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 14:32:07,812 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,215), 尺寸42x41, 长宽比1.02, 面积1722
2025-07-28 14:32:07,814 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(318,209), 尺寸7x20, 长宽比0.35, 面积140
2025-07-28 14:32:07,816 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(313,209), 尺寸15x114, 长宽比0.13, 面积1710
2025-07-28 14:32:07,822 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(272,209), 尺寸1x245, 长宽比0.00, 面积245
2025-07-28 14:32:07,823 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(270,209), 尺寸1x245, 长宽比0.00, 面积245
2025-07-28 14:32:07,824 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(61,209), 尺寸1x245, 长宽比0.00, 面积245
2025-07-28 14:32:07,824 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸1x245, 长宽比0.00, 面积245
2025-07-28 14:32:07,825 - WeChatAutoAdd - INFO - 底部区域找到 32 个按钮候选
2025-07-28 14:32:07,825 - WeChatAutoAdd - INFO - 选择特殊位置按钮: Y=279 (距底部154像素)
2025-07-28 14:32:07,826 - WeChatAutoAdd - INFO - 在底部找到按钮: (30, 288), 尺寸: 20x18, 位置得分: 3.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-07-28 14:32:07,826 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 14:32:07,845 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_143207.png
2025-07-28 14:32:07,846 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 14:32:07,848 - WeChatAutoAdd - INFO - 开始验证按钮区域(30, 288)是否包含'添加到通讯录'文字
2025-07-28 14:32:07,853 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_143207.png
2025-07-28 14:32:07,928 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 14:32:07,929 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0600, 平均亮度=226.2, 亮度标准差=41.5
2025-07-28 14:32:07,931 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 14:32:07,932 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 14:32:08,233 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(30, 288) -> 屏幕坐标(30, 288)
2025-07-28 14:32:09,025 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 14:32:09,041 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 14:32:09,061 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-28 14:32:09,064 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 14:32:09,409 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 14:32:09,410 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 14:32:09,410 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 14:32:09,410 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 14:32:09,411 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 14:32:09,411 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 14:32:09,411 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 14:32:09,411 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 14:32:09,412 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 14:32:09,412 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 14:32:09,613 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 14:32:09,613 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 14:32:09,615 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-28 14:32:09,915 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 14:32:09,915 - __main__ - INFO - ✅ 步骤4：图像识别添加完成
2025-07-28 14:32:10,916 - __main__ - INFO - 📍 当前执行步骤: FRIEND_REQUEST (步骤 5)
2025-07-28 14:32:10,917 - __main__ - INFO - 👥 步骤5：好友申请窗口 - 窗口 1
2025-07-28 14:32:10,917 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13046777941
2025-07-28 14:32:10,920 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-28 14:32:10,920 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\main_controller.py:389 in execute_single_window_flow
2025-07-28 14:32:10,921 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\main_controller.py:311 in execute_step_5_friend_request
2025-07-28 14:32:10,921 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-28 14:32:10,921 - modules.friend_request_window - INFO -    📱 phone: '13046777941'
2025-07-28 14:32:10,921 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-28 14:32:10,922 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-28 14:32:11,568 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-28 14:32:11,570 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-28 14:32:11,571 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-28 14:32:11,572 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 14:32:11,585 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13046777941
2025-07-28 14:32:11,585 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-28 14:32:11,587 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 14:32:11,589 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-28 14:32:11,589 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-28 14:32:11,590 - modules.friend_request_window - INFO -    📱 手机号码: 13046777941
2025-07-28 14:32:11,590 - modules.friend_request_window - INFO -    🆔 准考证: 015825120129
2025-07-28 14:32:11,591 - modules.friend_request_window - INFO -    👤 姓名: 徐金超
2025-07-28 14:32:11,591 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 14:32:11,592 - modules.friend_request_window - INFO -    📝 备注格式: '015825120129-徐金超-2025-07-28 14:32:11'
2025-07-28 14:32:11,592 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 14:32:11,593 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '015825120129-徐金超-2025-07-28 14:32:11'
2025-07-28 14:32:11,593 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 14:32:11,596 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-28 14:32:11,598 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-28 14:32:11,601 - __main__ - ERROR - ❌ 好友申请流程失败
2025-07-28 14:32:11,603 - __main__ - ERROR - ❌ 步骤 5 执行失败，终止当前窗口处理
2025-07-28 14:32:11,604 - __main__ - ERROR - ❌ 第 1 个微信窗口处理失败
2025-07-28 14:32:11,607 - __main__ - INFO - ⏳ 窗口切换延迟...
