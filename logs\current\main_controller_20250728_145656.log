2025-07-28 14:56:56,657 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 14:56:56,658 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 14:56:56,659 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-28 14:56:56,659 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 14:56:56,660 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-28 14:56:56,661 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 14:56:56,661 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 14:56:56,662 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 14:56:56,663 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 14:56:56,663 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 14:56:56,664 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 14:56:56,667 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 14:56:56,670 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250728_145656.log
2025-07-28 14:56:56,671 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-28 14:56:56,671 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-28 14:56:56,672 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-28 14:56:56,673 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-28 14:56:56,673 - __main__ - INFO - 📅 当前北京时间: 2025-07-28 14:56:56
2025-07-28 14:56:56,674 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-28 14:56:56,675 - __main__ - INFO - 📅 启动时间: 2025-07-28 14:56:56
2025-07-28 14:56:56,676 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-28 14:56:56,676 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 14:56:57,217 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 14:56:57,217 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 14:56:57,755 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 14:56:57,755 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 14:56:57,758 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-28 14:56:57,759 - __main__ - INFO - ✅ 找到 2 个微信窗口
2025-07-28 14:56:57,759 - __main__ - INFO -   窗口 1: 微信 (句柄: 197994)
2025-07-28 14:56:57,760 - __main__ - INFO -   窗口 2: 微信 (句柄: 525668)
2025-07-28 14:56:57,760 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-28 14:56:58,653 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-28 14:56:58,653 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-28 14:56:58,654 - __main__ - INFO - 📋 待处理联系人数: 3003
2025-07-28 14:56:58,654 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-28 14:56:58,655 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 3003
2025-07-28 14:56:58,655 - __main__ - INFO - 
============================================================
2025-07-28 14:56:58,655 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口
2025-07-28 14:56:58,655 - __main__ - INFO - ============================================================
2025-07-28 14:56:58,656 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-28 14:56:58,656 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-28 14:56:58,656 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-28 14:56:58,657 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-28 14:56:58,657 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 14:56:58,974 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 14:56:58,975 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 14:56:58,976 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 14:56:58,977 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 14:56:58,977 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 14:56:58,977 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 14:56:58,978 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 14:56:58,978 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 14:56:58,979 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 14:56:58,979 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 14:56:59,180 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 14:56:59,181 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 14:56:59,181 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-28 14:56:59,181 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 14:56:59,485 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 14:56:59,485 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 14:56:59,486 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 14:56:59,486 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 14:56:59,486 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 14:56:59,487 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 14:56:59,487 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 14:56:59,487 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 14:56:59,488 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 14:56:59,488 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 14:56:59,690 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 14:56:59,690 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 14:56:59,692 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-28 14:56:59,993 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 14:56:59,993 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 14:56:59,993 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 14:56:59,994 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 14:56:59,994 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 14:56:59,994 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 14:56:59,994 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-28 14:57:00,995 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-28 14:57:00,995 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-28 14:57:00,996 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-28 14:57:00,996 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-28 14:57:00,997 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-28 14:57:00,997 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-28 14:57:00,997 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-28 14:57:00,997 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-28 14:57:01,198 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-28 14:57:01,200 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-28 14:57:03,578 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-28 14:57:03,579 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-28 14:57:03,579 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.8 秒...
2025-07-28 14:57:05,404 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-28 14:57:05,604 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-28 14:57:05,605 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-28 14:57:07,973 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-28 14:57:07,974 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-28 14:57:07,974 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.4 秒...
2025-07-28 14:57:10,372 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-28 14:57:10,573 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-28 14:57:10,573 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-28 14:57:12,944 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-28 14:57:12,944 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-28 14:57:12,945 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.0 秒...
2025-07-28 14:57:14,899 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-28 14:57:15,101 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-28 14:57:15,101 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-28 14:57:17,468 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-28 14:57:17,469 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-28 14:57:17,469 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.7 秒...
2025-07-28 14:57:19,136 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-28 14:57:19,337 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-28 14:57:19,337 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-28 14:57:21,711 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-28 14:57:21,711 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-28 14:57:21,711 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-28 14:57:21,712 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-28 14:57:21,712 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 14:57:21,714 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 14:57:21,714 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 14:57:21,715 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 14:57:21,715 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 14:57:21,716 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1379228, 进程: Weixin.exe)
2025-07-28 14:57:21,719 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-28 14:57:21,719 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 1379228)
2025-07-28 14:57:21,720 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1379228) - 增强版
2025-07-28 14:57:22,025 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 14:57:22,026 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 14:57:22,026 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-28 14:57:22,026 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-28 14:57:22,027 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-28 14:57:22,027 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-28 14:57:22,230 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-28 14:57:22,231 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-28 14:57:22,432 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 14:57:22,433 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 14:57:22,433 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-28 14:57:22,433 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-28 14:57:22,434 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-28 14:57:22,434 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-28 14:57:22,434 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-28 14:57:23,435 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-28 14:57:23,435 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 14:57:23,437 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 14:57:23,438 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 14:57:23,438 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 14:57:23,439 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 14:57:23,440 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1379228, 进程: Weixin.exe)
2025-07-28 14:57:23,443 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-28 14:57:23,444 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-28 14:57:23,444 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-28 14:57:23,445 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-28 14:57:23,445 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-28 14:57:23,445 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 14:57:23,752 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 14:57:23,753 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 14:57:23,753 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 14:57:23,753 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 14:57:23,754 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 14:57:23,754 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 14:57:23,754 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 14:57:23,755 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 14:57:23,755 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 14:57:23,755 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 14:57:23,957 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 14:57:23,958 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 14:57:23,959 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-28 14:57:24,260 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 14:57:24,260 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-28 14:57:25,261 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-28 14:57:25,261 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-28 14:57:25,261 - __main__ - INFO - 🚀 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-28 14:57:25,265 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250728_145725.log
2025-07-28 14:57:25,265 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-28 14:57:25,265 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-28 14:57:25,265 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-28 14:57:25,266 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-28 14:57:25,268 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-28 14:57:25,269 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-28 14:57:25,269 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-28 14:57:25,269 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-28 14:57:25,269 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-28 14:57:25,270 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-28 14:57:25,270 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-28 14:57:25,271 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 14:57:25,271 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 1379228
2025-07-28 14:57:25,271 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1379228) - 增强版
2025-07-28 14:57:25,580 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 14:57:25,580 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 14:57:25,581 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-28 14:57:25,581 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-28 14:57:25,582 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-28 14:57:25,582 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-28 14:57:25,582 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-28 14:57:25,582 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-28 14:57:25,784 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 14:57:25,785 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 14:57:25,789 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 1379228 (API返回: None)
2025-07-28 14:57:26,089 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 14:57:26,090 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-28 14:57:26,090 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-28 14:57:26,090 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-28 14:57:26,091 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 14:57:26,091 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-28 14:57:26,092 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-28 14:57:26,096 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-28 14:57:26,096 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-28 14:57:26,582 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-28 14:57:26,582 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 14:57:26,836 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 3003 个
2025-07-28 14:57:26,837 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 3003 个 (总计: 3135 个)
2025-07-28 14:57:26,837 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/3003
2025-07-28 14:57:26,837 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13046777941 (徐金超)
2025-07-28 14:57:33,400 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13046777941
2025-07-28 14:57:33,401 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 14:57:33,401 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13046777941 执行添加朋友操作...
2025-07-28 14:57:33,401 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 14:57:33,402 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 14:57:33,402 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 14:57:33,403 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 14:57:33,411 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 8 个图片文件
2025-07-28 14:57:33,412 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 14:57:33,413 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 14:57:33,413 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 14:57:33,414 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 14:57:33,415 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 14:57:33,416 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 14:57:33,416 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 14:57:33,420 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-28 14:57:33,426 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 14:57:33,428 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 14:57:33,433 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 14:57:33,435 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-28 14:57:33,436 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-28 14:57:33,937 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-28 14:57:33,938 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-28 14:57:34,009 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差37.35, 边缘比例0.0474
2025-07-28 14:57:34,017 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_145734.png
2025-07-28 14:57:34,018 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 14:57:34,019 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 14:57:34,021 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-28 14:57:34,022 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-28 14:57:34,028 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-28 14:57:34,033 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_145734.png
2025-07-28 14:57:34,034 - WeChatAutoAdd - INFO - 底部区域原始检测到 12 个轮廓
2025-07-28 14:57:34,035 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,258), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-28 14:57:34,036 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-28 14:57:34,037 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(170,212), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:57:34,038 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,211), 尺寸4x3, 长宽比1.33, 面积12
2025-07-28 14:57:34,039 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(162,209), 尺寸7x6, 长宽比1.17, 面积42
2025-07-28 14:57:34,043 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,209), 尺寸3x5, 长宽比0.60, 面积15
2025-07-28 14:57:34,048 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(150,209), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:57:34,049 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(129,209), 尺寸19x7, 长宽比2.71, 面积133
2025-07-28 14:57:34,050 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(139,209), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 14:57:34,051 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,209), 尺寸7x2, 长宽比3.50, 面积14
2025-07-28 14:57:34,052 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(129,209), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:57:34,053 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(124,209), 尺寸5x6, 长宽比0.83, 面积30
2025-07-28 14:57:34,055 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-28 14:57:34,098 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-28 14:57:34,128 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=258
2025-07-28 14:57:34,129 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 273), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-28 14:57:34,164 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 14:57:34,178 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_145734.png
2025-07-28 14:57:34,186 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 14:57:34,197 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 273)是否包含'添加到通讯录'文字
2025-07-28 14:57:34,206 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_145734.png
2025-07-28 14:57:34,252 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 14:57:34,253 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-28 14:57:34,254 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 14:57:34,255 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 14:57:34,557 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 273) -> 屏幕坐标(1364, 273)
2025-07-28 14:57:35,326 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 14:57:35,327 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 14:57:35,328 - modules.wechat_auto_add_simple - INFO - ✅ 13046777941 添加朋友操作执行成功
2025-07-28 14:57:35,329 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 14:57:37,331 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-28 14:57:37,331 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-28 14:57:37,331 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-28 14:57:37,332 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 14:57:37,332 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 14:57:37,332 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 14:57:37,333 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 14:57:37,333 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 14:57:37,333 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-28 14:57:37,334 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13046777941
2025-07-28 14:57:37,337 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-28 14:57:37,337 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:536 in _execute_auto_add_friend
2025-07-28 14:57:37,338 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:614 in _handle_friend_request_window
2025-07-28 14:57:37,338 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-28 14:57:37,340 - modules.friend_request_window - INFO -    📱 phone: '13046777941'
2025-07-28 14:57:37,340 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-28 14:57:37,340 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-28 14:57:37,931 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-28 14:57:37,932 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-28 14:57:37,933 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-28 14:57:37,933 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 14:57:37,935 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13046777941
2025-07-28 14:57:37,935 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-28 14:57:37,936 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 14:57:37,937 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-28 14:57:37,937 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-28 14:57:37,938 - modules.friend_request_window - INFO -    📱 手机号码: 13046777941
2025-07-28 14:57:37,938 - modules.friend_request_window - INFO -    🆔 准考证: 015825120129
2025-07-28 14:57:37,940 - modules.friend_request_window - INFO -    👤 姓名: 徐金超
2025-07-28 14:57:37,940 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 14:57:37,941 - modules.friend_request_window - INFO -    📝 备注格式: '015825120129-徐金超-2025-07-28 14:57:37'
2025-07-28 14:57:37,941 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 14:57:37,942 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '015825120129-徐金超-2025-07-28 14:57:37'
2025-07-28 14:57:37,943 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 14:57:37,945 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 3803380, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-28 14:57:37,946 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 3803380)
2025-07-28 14:57:37,947 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-28 14:57:37,948 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-28 14:57:37,949 - modules.friend_request_window - INFO - 🔄 激活窗口: 3803380
2025-07-28 14:57:38,652 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-28 14:57:38,653 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-28 14:57:38,653 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-28 14:57:38,654 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:471 in _analyze_search_result
2025-07-28 14:57:38,654 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-28 14:57:38,654 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:536 in _execute_auto_add_friend
2025-07-28 14:57:38,654 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-28 14:57:38,655 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:614 in _handle_friend_request_window
2025-07-28 14:57:38,655 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-28 14:57:38,656 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1158 in execute_friend_request_flow
2025-07-28 14:57:38,656 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-28 14:57:38,657 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-28 14:57:38,657 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-28 14:57:38,657 - modules.friend_request_window - INFO -    📝 remark参数: '015825120129-徐金超-2025-07-28 14:57:37' (类型: <class 'str'>, 长度: 36)
2025-07-28 14:57:38,658 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-28 14:57:38,659 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '015825120129-徐金超-2025-07-28 14:57:37'
2025-07-28 14:57:38,660 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-28 14:57:38,660 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-28 14:57:38,661 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-28 14:57:38,661 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-28 14:57:38,661 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-28 14:57:38,661 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-28 14:57:38,662 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-28 14:57:39,576 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-28 14:57:44,821 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-28 14:57:44,822 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-28 14:57:44,822 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-28 14:57:44,823 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-28 14:57:44,825 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'main_controller.py完全没有调用modules\wechat_auto_add_si...' (前50字符)
2025-07-28 14:57:45,137 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 14:57:45,138 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 14:57:46,041 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 14:57:46,049 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 14:57:46,050 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-28 14:57:46,052 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-28 14:57:46,053 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-28 14:57:46,053 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-28 14:57:46,554 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-28 14:57:46,555 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-28 14:57:46,555 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-28 14:57:46,555 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-28 14:57:46,556 - modules.friend_request_window - INFO -    📝 内容: '015825120129-徐金超-2025-07-28 14:57:37'
2025-07-28 14:57:46,556 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-28 14:57:46,556 - modules.friend_request_window - INFO -    🔤 内容编码: b'015825120129-\xe5\xbe\x90\xe9\x87\x91\xe8\xb6\x85-2025-07-28 14:57:37'
2025-07-28 14:57:46,557 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-28 14:57:47,476 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-28 14:57:52,718 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-28 14:57:52,719 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-28 14:57:52,719 - modules.friend_request_window - INFO -    📝 原始文本: '015825120129-徐金超-2025-07-28 14:57:37'
2025-07-28 14:57:52,719 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-28 14:57:52,720 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'main_controller.py完全没有调用modules\wechat_auto_add_si...' (前50字符)
2025-07-28 14:57:53,030 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 14:57:53,031 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 14:57:53,933 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 14:57:53,945 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 14:57:53,946 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '015825120129-徐金超-2025-07-28 14:57:37'
2025-07-28 14:57:53,947 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-28 14:57:53,947 - modules.friend_request_window - INFO -    📝 已填写内容: '015825120129-徐金超-2025-07-28 14:57:37'
2025-07-28 14:57:53,947 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-28 14:57:54,448 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '015825120129-徐金超-2025-07-28 14:57:37'
2025-07-28 14:57:54,448 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-28 14:57:54,448 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-28 14:57:54,449 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-28 14:57:54,449 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-28 14:57:54,449 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-28 14:57:54,450 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-28 14:57:55,250 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-28 14:57:55,250 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-28 14:57:55,251 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-28 14:57:55,876 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 14:57:55,876 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-28 14:57:55,877 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-28 14:57:55,877 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-28 14:57:56,394 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 14:57:56,627 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 14:57:56,860 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 14:57:57,091 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 14:57:57,323 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 14:57:57,555 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 14:57:57,787 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 14:57:58,018 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 14:57:58,252 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 14:57:58,484 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 14:57:58,715 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 14:57:58,947 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 14:57:59,178 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 14:57:59,410 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 14:57:59,643 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 14:57:59,876 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 14:58:00,108 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 14:58:00,341 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 14:58:00,573 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 14:58:00,805 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 14:58:01,019 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-28 14:58:01,020 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-28 14:58:02,021 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 14:58:02,023 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-28 14:58:02,024 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-28 14:58:02,024 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-28 14:58:02,024 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-28 14:58:02,025 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 14:58:02,025 - modules.friend_request_window - INFO -    📝 备注信息: '015825120129-徐金超-2025-07-28 14:57:37'
2025-07-28 14:58:02,526 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-28 14:58:02,526 - modules.wechat_auto_add_simple - INFO - ✅ 13046777941 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-28 14:58:02,527 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 13046777941
2025-07-28 14:58:06,175 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/3003
2025-07-28 14:58:06,175 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 17671621927 (赵伟)
2025-07-28 14:58:12,751 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 17671621927
2025-07-28 14:58:12,751 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 14:58:12,751 - modules.wechat_auto_add_simple - INFO - 👥 开始为 17671621927 执行添加朋友操作...
2025-07-28 14:58:12,752 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 14:58:12,752 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 14:58:12,753 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 14:58:12,754 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 14:58:12,759 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 14:58:12,761 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 14:58:12,761 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 14:58:12,762 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 14:58:12,762 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 14:58:12,764 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 14:58:12,765 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 14:58:12,767 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 14:58:12,771 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-28 14:58:12,774 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 14:58:12,776 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 14:58:12,778 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 14:58:12,786 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-28 14:58:12,789 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-28 14:58:13,295 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-28 14:58:13,296 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-28 14:58:13,359 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差28.40, 边缘比例0.0349
2025-07-28 14:58:13,366 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_145813.png
2025-07-28 14:58:13,367 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 14:58:13,368 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 14:58:13,370 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-28 14:58:13,376 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-28 14:58:13,377 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-28 14:58:13,382 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_145813.png
2025-07-28 14:58:13,383 - WeChatAutoAdd - INFO - 底部区域原始检测到 38 个轮廓
2025-07-28 14:58:13,384 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:58:13,385 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:58:13,386 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:13,389 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-07-28 14:58:13,392 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 14:58:13,393 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 14:58:13,395 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-07-28 14:58:13,396 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:13,397 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-07-28 14:58:13,398 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-07-28 14:58:13,400 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-07-28 14:58:13,401 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:13,402 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 14:58:13,405 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:58:13,409 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:13,411 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 14:58:13,412 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 14:58:13,413 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:13,415 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:13,417 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-07-28 14:58:13,419 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:58:13,422 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-07-28 14:58:13,426 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:13,427 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-07-28 14:58:13,429 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-07-28 14:58:13,430 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-07-28 14:58:13,433 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-07-28 14:58:13,436 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-07-28 14:58:13,442 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-07-28 14:58:13,444 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-07-28 14:58:13,446 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-07-28 14:58:13,448 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-07-28 14:58:13,452 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-07-28 14:58:13,459 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-07-28 14:58:13,461 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-07-28 14:58:13,484 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-07-28 14:58:13,510 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-07-28 14:58:13,530 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-28 14:58:13,576 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-07-28 14:58:13,586 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-07-28 14:58:13,591 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-28 14:58:13,593 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 14:58:13,603 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_145813.png
2025-07-28 14:58:13,618 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 14:58:13,625 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-07-28 14:58:13,632 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_145813.png
2025-07-28 14:58:13,658 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 14:58:13,660 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-07-28 14:58:13,661 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 14:58:13,662 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 14:58:13,963 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-07-28 14:58:14,731 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 14:58:14,733 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 14:58:14,734 - modules.wechat_auto_add_simple - INFO - ✅ 17671621927 添加朋友操作执行成功
2025-07-28 14:58:14,734 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 14:58:16,735 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-28 14:58:16,736 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-28 14:58:16,736 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-28 14:58:16,737 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 14:58:16,737 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 14:58:16,737 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 14:58:16,738 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 14:58:16,738 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 14:58:16,738 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-28 14:58:16,738 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 17671621927
2025-07-28 14:58:16,739 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-28 14:58:16,739 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:536 in _execute_auto_add_friend
2025-07-28 14:58:16,740 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:614 in _handle_friend_request_window
2025-07-28 14:58:16,740 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-28 14:58:16,740 - modules.friend_request_window - INFO -    📱 phone: '17671621927'
2025-07-28 14:58:16,740 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-28 14:58:16,741 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-28 14:58:17,312 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-28 14:58:17,312 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-28 14:58:17,313 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-28 14:58:17,313 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 14:58:17,315 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 17671621927
2025-07-28 14:58:17,315 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-28 14:58:17,316 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 14:58:17,316 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-28 14:58:17,316 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-28 14:58:17,317 - modules.friend_request_window - INFO -    📱 手机号码: 17671621927
2025-07-28 14:58:17,317 - modules.friend_request_window - INFO -    🆔 准考证: 015825120130
2025-07-28 14:58:17,317 - modules.friend_request_window - INFO -    👤 姓名: 赵伟
2025-07-28 14:58:17,317 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 14:58:17,318 - modules.friend_request_window - INFO -    📝 备注格式: '015825120130-赵伟-2025-07-28 14:58:17'
2025-07-28 14:58:17,319 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 14:58:17,319 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '015825120130-赵伟-2025-07-28 14:58:17'
2025-07-28 14:58:17,319 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 14:58:17,324 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-28 14:58:17,324 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-28 14:58:17,326 - modules.wechat_auto_add_simple - INFO - ✅ 17671621927 申请添加朋友窗口处理完成: 申请添加朋友窗口处理失败: 未找到好友申请窗口
2025-07-28 14:58:17,326 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 17671621927
2025-07-28 14:58:21,008 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 3/3003
2025-07-28 14:58:21,008 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18671256139 (刘梦婷)
2025-07-28 14:58:27,567 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18671256139
2025-07-28 14:58:27,568 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 14:58:27,568 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18671256139 执行添加朋友操作...
2025-07-28 14:58:27,569 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 14:58:27,569 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 14:58:27,571 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 14:58:27,572 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 14:58:27,586 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 14:58:27,593 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 14:58:27,595 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 14:58:27,596 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 14:58:27,597 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 14:58:27,597 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 14:58:27,598 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 14:58:27,599 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 14:58:27,612 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x625
2025-07-28 14:58:27,615 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 14:58:27,617 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 14:58:27,626 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 14:58:27,630 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-28 14:58:27,632 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-28 14:58:28,135 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-28 14:58:28,136 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x625
2025-07-28 14:58:28,208 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差43.14, 边缘比例0.0624
2025-07-28 14:58:28,219 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_145828.png
2025-07-28 14:58:28,222 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 14:58:28,223 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 14:58:28,225 - WeChatAutoAdd - INFO - 截图尺寸: 328x625
2025-07-28 14:58:28,226 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=380-625 (高度:245)
2025-07-28 14:58:28,229 - WeChatAutoAdd - INFO - 特别关注区域: Y=441-501 (距底部154像素)
2025-07-28 14:58:28,235 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_145828.png
2025-07-28 14:58:28,241 - WeChatAutoAdd - INFO - 底部区域原始检测到 113 个轮廓
2025-07-28 14:58:28,242 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(254,569), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 14:58:28,245 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(171,569), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 14:58:28,246 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(242,568), 尺寸8x3, 长宽比2.67, 面积24
2025-07-28 14:58:28,249 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,567), 尺寸9x4, 长宽比2.25, 面积36
2025-07-28 14:58:28,251 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,567), 尺寸11x4, 长宽比2.75, 面积44
2025-07-28 14:58:28,257 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(262,566), 尺寸12x5, 长宽比2.40, 面积60
2025-07-28 14:58:28,259 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(263,566), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 14:58:28,261 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(246,566), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:58:28,263 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(179,566), 尺寸12x5, 长宽比2.40, 面积60
2025-07-28 14:58:28,264 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(180,566), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 14:58:28,265 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,566), 尺寸4x2, 长宽比2.00, 面积8
2025-07-28 14:58:28,266 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(241,565), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:28,268 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(236,565), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 14:58:28,273 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(243,564), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 14:58:28,276 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,562), 尺寸6x8, 长宽比0.75, 面积48
2025-07-28 14:58:28,277 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(245,562), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 14:58:28,278 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,560), 尺寸3x1, 长宽比3.00, 面积3
2025-07-28 14:58:28,281 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(247,560), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:28,282 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(245,560), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:28,284 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,560), 尺寸4x5, 长宽比0.80, 面积20
2025-07-28 14:58:28,285 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(232,560), 尺寸4x7, 长宽比0.57, 面积28
2025-07-28 14:58:28,292 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(150,560), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:28,296 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(143,559), 尺寸12x11, 长宽比1.09, 面积132
2025-07-28 14:58:28,298 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=61.1 (阈值:60)
2025-07-28 14:58:28,299 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(66,559), 尺寸8x11, 长宽比0.73, 面积88
2025-07-28 14:58:28,301 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(239,558), 尺寸35x12, 长宽比2.92, 面积420
2025-07-28 14:58:28,303 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=60.2 (阈值:60)
2025-07-28 14:58:28,310 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(226,558), 尺寸10x13, 长宽比0.77, 面积130
2025-07-28 14:58:28,312 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(146,558), 尺寸45x12, 长宽比3.75, 面积540
2025-07-28 14:58:28,314 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=62.2 (阈值:60)
2025-07-28 14:58:28,316 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(73,558), 尺寸29x13, 长宽比2.23, 面积377
2025-07-28 14:58:28,318 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=62.2 (阈值:60)
2025-07-28 14:58:28,324 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,558), 尺寸6x4, 长宽比1.50, 面积24
2025-07-28 14:58:28,326 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(239,530), 尺寸23x14, 长宽比1.64, 面积322
2025-07-28 14:58:28,328 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(156,527), 尺寸21x21, 长宽比1.00, 面积441
2025-07-28 14:58:28,331 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(73,527), 尺寸22x19, 长宽比1.16, 面积418
2025-07-28 14:58:28,333 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(233,477), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:58:28,341 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(229,476), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 14:58:28,343 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,476), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:28,345 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(223,475), 尺寸5x4, 长宽比1.25, 面积20
2025-07-28 14:58:28,346 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(210,475), 尺寸8x4, 长宽比2.00, 面积32
2025-07-28 14:58:28,349 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,475), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:58:28,350 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(70,475), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:58:28,351 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,474), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:58:28,354 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,473), 尺寸3x5, 长宽比0.60, 面积15
2025-07-28 14:58:28,358 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(196,473), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:58:28,360 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,473), 尺寸4x1, 长宽比4.00, 面积4
2025-07-28 14:58:28,361 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(56,473), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:28,362 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(140,472), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:58:28,364 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(220,471), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:28,366 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(142,471), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:28,367 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(132,471), 尺寸5x6, 长宽比0.83, 面积30
2025-07-28 14:58:28,368 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,471), 尺寸2x5, 长宽比0.40, 面积10
2025-07-28 14:58:28,376 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(66,471), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:28,378 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(48,471), 尺寸9x7, 长宽比1.29, 面积63
2025-07-28 14:58:28,379 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,470), 尺寸8x7, 长宽比1.14, 面积56
2025-07-28 14:58:28,381 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(66,469), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:28,383 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(58,468), 尺寸15x11, 长宽比1.36, 面积165
2025-07-28 14:58:28,386 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=468 (距底部154像素区域)
2025-07-28 14:58:28,389 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 14:58:28,392 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(196,467), 尺寸23x12, 长宽比1.92, 面积276
2025-07-28 14:58:28,394 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=467 (距底部154像素区域)
2025-07-28 14:58:28,395 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 14:58:28,397 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,466), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:28,398 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(49,466), 尺寸12x6, 长宽比2.00, 面积72
2025-07-28 14:58:28,400 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(217,465), 尺寸30x14, 长宽比2.14, 面积420
2025-07-28 14:58:28,402 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=465 (距底部154像素区域)
2025-07-28 14:58:28,407 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 14:58:28,408 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(207,465), 尺寸11x5, 长宽比2.20, 面积55
2025-07-28 14:58:28,410 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(197,465), 尺寸6x9, 长宽比0.67, 面积54
2025-07-28 14:58:28,411 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,465), 尺寸33x14, 长宽比2.36, 面积462
2025-07-28 14:58:28,412 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=465 (距底部154像素区域)
2025-07-28 14:58:28,414 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 14:58:28,416 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,465), 尺寸54x14, 长宽比3.86, 面积756
2025-07-28 14:58:28,420 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=465 (距底部154像素区域)
2025-07-28 14:58:28,426 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 14:58:28,428 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(62,465), 尺寸14x13, 长宽比1.08, 面积182
2025-07-28 14:58:28,429 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=465 (距底部154像素区域)
2025-07-28 14:58:28,431 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 14:58:28,433 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(184,454), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:28,435 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(163,451), 尺寸24x6, 长宽比4.00, 面积144
2025-07-28 14:58:28,436 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(185,450), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:28,441 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,450), 尺寸14x5, 长宽比2.80, 面积70
2025-07-28 14:58:28,443 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(187,448), 尺寸3x1, 长宽比3.00, 面积3
2025-07-28 14:58:28,446 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(178,448), 尺寸4x5, 长宽比0.80, 面积20
2025-07-28 14:58:28,448 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(153,447), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:28,451 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(173,446), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:28,452 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,446), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 14:58:28,456 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,446), 尺寸1x3, 长宽比0.33, 面积3
2025-07-28 14:58:28,458 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(139,446), 尺寸4x3, 长宽比1.33, 面积12
2025-07-28 14:58:28,459 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(140,446), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:28,461 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(168,445), 尺寸4x4, 长宽比1.00, 面积16
2025-07-28 14:58:28,462 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,444), 尺寸36x13, 长宽比2.77, 面积468
2025-07-28 14:58:28,465 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=444 (距底部154像素区域)
2025-07-28 14:58:28,467 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 14:58:28,470 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(152,444), 尺寸11x13, 长宽比0.85, 面积143
2025-07-28 14:58:28,475 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,444), 尺寸3x8, 长宽比0.38, 面积24
2025-07-28 14:58:28,476 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,443), 尺寸27x7, 长宽比3.86, 面积189
2025-07-28 14:58:28,477 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,443), 尺寸31x13, 长宽比2.38, 面积403
2025-07-28 14:58:28,480 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=443 (距底部154像素区域)
2025-07-28 14:58:28,482 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 14:58:28,483 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(264,438), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:28,484 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(171,435), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:28,486 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(168,435), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:58:28,490 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(262,434), 尺寸4x5, 长宽比0.80, 面积20
2025-07-28 14:58:28,492 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(84,433), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:58:28,493 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(181,432), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:58:28,496 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(63,432), 尺寸1x6, 长宽比0.17, 面积6
2025-07-28 14:58:28,498 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(58,431), 尺寸4x3, 长宽比1.33, 面积12
2025-07-28 14:58:28,499 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,430), 尺寸26x9, 长宽比2.89, 面积234
2025-07-28 14:58:28,501 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=96.9 (阈值:60)
2025-07-28 14:58:28,505 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,430), 尺寸2x5, 长宽比0.40, 面积10
2025-07-28 14:58:28,508 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(168,430), 尺寸6x4, 长宽比1.50, 面积24
2025-07-28 14:58:28,509 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(54,430), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:58:28,511 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,428), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 14:58:28,513 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(95,428), 尺寸4x3, 长宽比1.33, 面积12
2025-07-28 14:58:28,514 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(55,428), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:28,515 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(229,427), 尺寸1x4, 长宽比0.25, 面积4
2025-07-28 14:58:28,517 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(190,427), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:58:28,519 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(186,427), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 14:58:28,523 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(124,426), 尺寸10x4, 长宽比2.50, 面积40
2025-07-28 14:58:28,525 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(65,426), 尺寸38x13, 长宽比2.92, 面积494
2025-07-28 14:58:28,527 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(48,426), 尺寸16x12, 长宽比1.33, 面积192
2025-07-28 14:58:28,529 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(238,425), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 14:58:28,530 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(191,425), 尺寸70x14, 长宽比5.00, 面积980
2025-07-28 14:58:28,532 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=89.1 (阈值:60)
2025-07-28 14:58:28,533 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(184,425), 尺寸5x2, 长宽比2.50, 面积10
2025-07-28 14:58:28,534 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,425), 尺寸62x14, 长宽比4.43, 面积868
2025-07-28 14:58:28,540 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=92.9 (阈值:60)
2025-07-28 14:58:28,543 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(326,380), 尺寸2x239, 长宽比0.01, 面积478
2025-07-28 14:58:28,544 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(65,380), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:58:28,545 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(61,380), 尺寸29x3, 长宽比9.67, 面积87
2025-07-28 14:58:28,547 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(57,380), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 14:58:28,549 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(51,380), 尺寸5x3, 长宽比1.67, 面积15
2025-07-28 14:58:28,550 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(48,380), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 14:58:28,551 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,380), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-28 14:58:28,554 - WeChatAutoAdd - INFO - 底部区域找到 13 个按钮候选
2025-07-28 14:58:28,560 - WeChatAutoAdd - INFO - 选择特殊位置按钮: Y=465 (距底部154像素)
2025-07-28 14:58:28,561 - WeChatAutoAdd - INFO - 在底部找到按钮: (148, 472), 尺寸: 54x14, 位置得分: 3.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-07-28 14:58:28,564 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 14:58:28,579 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_145828.png
2025-07-28 14:58:28,581 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 14:58:28,582 - WeChatAutoAdd - INFO - 开始验证按钮区域(148, 472)是否包含'添加到通讯录'文字
2025-07-28 14:58:28,586 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_145828.png
2025-07-28 14:58:28,609 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 14:58:28,612 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.1304, 平均亮度=231.8, 亮度标准差=63.2
2025-07-28 14:58:28,613 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 14:58:28,614 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 14:58:28,916 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(148, 472) -> 屏幕坐标(1348, 472)
2025-07-28 14:58:29,689 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 14:58:29,691 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 14:58:29,692 - modules.wechat_auto_add_simple - INFO - ✅ 18671256139 添加朋友操作执行成功
2025-07-28 14:58:29,693 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 14:58:31,694 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-28 14:58:31,695 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-28 14:58:31,695 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-28 14:58:31,695 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 14:58:31,696 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 14:58:31,696 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 14:58:31,696 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 14:58:31,696 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 14:58:31,697 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-28 14:58:31,697 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18671256139
2025-07-28 14:58:31,698 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-28 14:58:31,698 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:536 in _execute_auto_add_friend
2025-07-28 14:58:31,698 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:614 in _handle_friend_request_window
2025-07-28 14:58:31,698 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-28 14:58:31,699 - modules.friend_request_window - INFO -    📱 phone: '18671256139'
2025-07-28 14:58:31,699 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-28 14:58:31,699 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-28 14:58:32,190 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-28 14:58:32,191 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-28 14:58:32,191 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-28 14:58:32,191 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 14:58:32,193 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18671256139
2025-07-28 14:58:32,193 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-28 14:58:32,194 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 14:58:32,194 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-28 14:58:32,194 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-28 14:58:32,195 - modules.friend_request_window - INFO -    📱 手机号码: 18671256139
2025-07-28 14:58:32,195 - modules.friend_request_window - INFO -    🆔 准考证: 015825120154
2025-07-28 14:58:32,196 - modules.friend_request_window - INFO -    👤 姓名: 刘梦婷
2025-07-28 14:58:32,196 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 14:58:32,197 - modules.friend_request_window - INFO -    📝 备注格式: '015825120154-刘梦婷-2025-07-28 14:58:32'
2025-07-28 14:58:32,197 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 14:58:32,198 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '015825120154-刘梦婷-2025-07-28 14:58:32'
2025-07-28 14:58:32,199 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 14:58:32,203 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-28 14:58:32,205 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-28 14:58:32,206 - modules.wechat_auto_add_simple - INFO - ✅ 18671256139 申请添加朋友窗口处理完成: 申请添加朋友窗口处理失败: 未找到好友申请窗口
2025-07-28 14:58:32,206 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 18671256139
2025-07-28 14:58:35,723 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 4/3003
2025-07-28 14:58:35,723 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15690166368 (张骞)
2025-07-28 14:58:42,281 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15690166368
2025-07-28 14:58:42,281 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 14:58:42,282 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15690166368 执行添加朋友操作...
2025-07-28 14:58:42,282 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 14:58:42,282 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 14:58:42,283 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 14:58:42,284 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 14:58:42,289 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 14:58:42,292 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 14:58:42,293 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 14:58:42,294 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 14:58:42,295 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 14:58:42,295 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 14:58:42,296 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 14:58:42,296 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 14:58:42,301 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-28 14:58:42,306 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 14:58:42,309 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 14:58:42,311 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 14:58:42,314 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-28 14:58:42,315 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-28 14:58:42,817 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-28 14:58:42,819 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-28 14:58:42,920 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差28.55, 边缘比例0.0352
2025-07-28 14:58:42,984 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_145842.png
2025-07-28 14:58:42,995 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 14:58:42,997 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 14:58:42,999 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-28 14:58:43,000 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-28 14:58:43,002 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-28 14:58:43,010 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_145843.png
2025-07-28 14:58:43,013 - WeChatAutoAdd - INFO - 底部区域原始检测到 38 个轮廓
2025-07-28 14:58:43,015 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:58:43,017 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:58:43,021 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:43,024 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-07-28 14:58:43,025 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 14:58:43,028 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 14:58:43,029 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-07-28 14:58:43,031 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:43,032 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-07-28 14:58:43,034 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-07-28 14:58:43,041 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-07-28 14:58:43,042 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:43,045 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 14:58:43,047 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:58:43,050 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:43,057 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 14:58:43,059 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 14:58:43,063 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:43,065 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:43,066 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-07-28 14:58:43,071 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:58:43,075 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-07-28 14:58:43,076 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:43,078 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-07-28 14:58:43,080 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-07-28 14:58:43,081 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-07-28 14:58:43,082 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-07-28 14:58:43,088 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-07-28 14:58:43,091 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-07-28 14:58:43,093 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-07-28 14:58:43,096 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-07-28 14:58:43,097 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-07-28 14:58:43,099 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-07-28 14:58:43,101 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-07-28 14:58:43,106 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-07-28 14:58:43,109 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-07-28 14:58:43,111 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-07-28 14:58:43,114 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-28 14:58:43,115 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-07-28 14:58:43,117 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-07-28 14:58:43,119 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-28 14:58:43,124 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 14:58:43,131 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_145843.png
2025-07-28 14:58:43,133 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 14:58:43,135 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-07-28 14:58:43,142 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_145843.png
2025-07-28 14:58:43,162 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 14:58:43,164 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-07-28 14:58:43,166 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 14:58:43,167 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 14:58:43,474 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-07-28 14:58:44,242 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 14:58:44,243 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 14:58:44,245 - modules.wechat_auto_add_simple - INFO - ✅ 15690166368 添加朋友操作执行成功
2025-07-28 14:58:44,245 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 14:58:46,247 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-28 14:58:46,247 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-28 14:58:46,247 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-28 14:58:46,247 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 14:58:46,248 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 14:58:46,248 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 14:58:46,248 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 14:58:46,249 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 14:58:46,249 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-28 14:58:46,249 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 15690166368
2025-07-28 14:58:46,250 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-28 14:58:46,250 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:536 in _execute_auto_add_friend
2025-07-28 14:58:46,250 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:614 in _handle_friend_request_window
2025-07-28 14:58:46,251 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-28 14:58:46,251 - modules.friend_request_window - INFO -    📱 phone: '15690166368'
2025-07-28 14:58:46,252 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-28 14:58:46,253 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-28 14:58:46,737 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-28 14:58:46,738 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-28 14:58:46,738 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-28 14:58:46,739 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 14:58:46,740 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 15690166368
2025-07-28 14:58:46,740 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-28 14:58:46,741 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 14:58:46,742 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-28 14:58:46,742 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-28 14:58:46,742 - modules.friend_request_window - INFO -    📱 手机号码: 15690166368
2025-07-28 14:58:46,743 - modules.friend_request_window - INFO -    🆔 准考证: 015825120158
2025-07-28 14:58:46,744 - modules.friend_request_window - INFO -    👤 姓名: 张骞
2025-07-28 14:58:46,746 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 14:58:46,747 - modules.friend_request_window - INFO -    📝 备注格式: '015825120158-张骞-2025-07-28 14:58:46'
2025-07-28 14:58:46,747 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 14:58:46,750 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '015825120158-张骞-2025-07-28 14:58:46'
2025-07-28 14:58:46,750 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 14:58:46,755 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-28 14:58:46,756 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-28 14:58:46,757 - modules.wechat_auto_add_simple - INFO - ✅ 15690166368 申请添加朋友窗口处理完成: 申请添加朋友窗口处理失败: 未找到好友申请窗口
2025-07-28 14:58:46,757 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 15690166368
2025-07-28 14:58:50,337 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 5/3003
2025-07-28 14:58:50,337 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13339987717 (彭洁)
2025-07-28 14:58:56,895 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13339987717
2025-07-28 14:58:56,896 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 14:58:56,896 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13339987717 执行添加朋友操作...
2025-07-28 14:58:56,896 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 14:58:56,896 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 14:58:56,897 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 14:58:56,899 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 14:58:56,904 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 14:58:56,907 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 14:58:56,907 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 14:58:56,909 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 14:58:56,909 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 14:58:56,910 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 14:58:56,911 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 14:58:56,912 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 14:58:56,918 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-28 14:58:56,925 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 14:58:56,927 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 14:58:56,930 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 14:58:56,933 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-28 14:58:56,939 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-28 14:58:57,441 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-28 14:58:57,443 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-28 14:58:57,498 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差28.42, 边缘比例0.0348
2025-07-28 14:58:57,507 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_145857.png
2025-07-28 14:58:57,509 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 14:58:57,511 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 14:58:57,513 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-28 14:58:57,514 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-28 14:58:57,516 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-28 14:58:57,523 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_145857.png
2025-07-28 14:58:57,525 - WeChatAutoAdd - INFO - 底部区域原始检测到 38 个轮廓
2025-07-28 14:58:57,527 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:58:57,529 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:58:57,531 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:57,533 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-07-28 14:58:57,542 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 14:58:57,547 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 14:58:57,555 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-07-28 14:58:57,557 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:57,559 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-07-28 14:58:57,561 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-07-28 14:58:57,563 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-07-28 14:58:57,573 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:57,575 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 14:58:57,578 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:58:57,580 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:57,581 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 14:58:57,590 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 14:58:57,592 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:57,594 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:57,596 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-07-28 14:58:57,599 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:58:57,604 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-07-28 14:58:57,607 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:58:57,609 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-07-28 14:58:57,612 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-07-28 14:58:57,614 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-07-28 14:58:57,616 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-07-28 14:58:57,622 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-07-28 14:58:57,624 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-07-28 14:58:57,626 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-07-28 14:58:57,631 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-07-28 14:58:57,632 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-07-28 14:58:57,639 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-07-28 14:58:57,642 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-07-28 14:58:57,646 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-07-28 14:58:57,648 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-07-28 14:58:57,653 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-07-28 14:58:57,657 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-28 14:58:57,659 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-07-28 14:58:57,663 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-07-28 14:58:57,667 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-28 14:58:57,672 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 14:58:57,680 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_145857.png
2025-07-28 14:58:57,682 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 14:58:57,684 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-07-28 14:58:57,691 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_145857.png
2025-07-28 14:58:57,711 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 14:58:57,713 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-07-28 14:58:57,715 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 14:58:57,716 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 14:58:58,022 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-07-28 14:58:58,790 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 14:58:58,792 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 14:58:58,794 - modules.wechat_auto_add_simple - INFO - ✅ 13339987717 添加朋友操作执行成功
2025-07-28 14:58:58,794 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 14:59:00,795 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-28 14:59:00,796 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-28 14:59:00,796 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-28 14:59:00,796 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 14:59:00,797 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 14:59:00,797 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 14:59:00,797 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 14:59:00,797 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 14:59:00,798 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-28 14:59:00,798 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13339987717
2025-07-28 14:59:00,799 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-28 14:59:00,799 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:536 in _execute_auto_add_friend
2025-07-28 14:59:00,799 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:614 in _handle_friend_request_window
2025-07-28 14:59:00,799 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-28 14:59:00,800 - modules.friend_request_window - INFO -    📱 phone: '13339987717'
2025-07-28 14:59:00,800 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-28 14:59:00,800 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-28 14:59:01,365 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-28 14:59:01,365 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-28 14:59:01,365 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-28 14:59:01,366 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 14:59:01,367 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13339987717
2025-07-28 14:59:01,367 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-28 14:59:01,368 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 14:59:01,368 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-28 14:59:01,369 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-28 14:59:01,369 - modules.friend_request_window - INFO -    📱 手机号码: 13339987717
2025-07-28 14:59:01,369 - modules.friend_request_window - INFO -    🆔 准考证: 015825120159
2025-07-28 14:59:01,370 - modules.friend_request_window - INFO -    👤 姓名: 彭洁
2025-07-28 14:59:01,370 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 14:59:01,371 - modules.friend_request_window - INFO -    📝 备注格式: '015825120159-彭洁-2025-07-28 14:59:01'
2025-07-28 14:59:01,371 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 14:59:01,372 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '015825120159-彭洁-2025-07-28 14:59:01'
2025-07-28 14:59:01,373 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 14:59:01,376 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-28 14:59:01,378 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-28 14:59:01,378 - modules.wechat_auto_add_simple - INFO - ✅ 13339987717 申请添加朋友窗口处理完成: 申请添加朋友窗口处理失败: 未找到好友申请窗口
2025-07-28 14:59:01,379 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 13339987717
2025-07-28 14:59:04,891 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 6/3003
2025-07-28 14:59:04,891 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13667224416 (雷慰)
2025-07-28 14:59:11,460 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13667224416
2025-07-28 14:59:11,461 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 14:59:11,461 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13667224416 执行添加朋友操作...
2025-07-28 14:59:11,461 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 14:59:11,462 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 14:59:11,462 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 14:59:11,464 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 14:59:11,469 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 14:59:11,474 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 14:59:11,476 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 14:59:11,477 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 14:59:11,477 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 14:59:11,477 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 14:59:11,478 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 14:59:11,478 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 14:59:11,482 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-28 14:59:11,487 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 14:59:11,492 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 14:59:11,495 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 14:59:11,498 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-28 14:59:11,501 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-28 14:59:12,006 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-28 14:59:12,008 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-28 14:59:12,067 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差44.38, 边缘比例0.0422
2025-07-28 14:59:12,078 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_145912.png
2025-07-28 14:59:12,081 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 14:59:12,082 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 14:59:12,089 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-28 14:59:12,092 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-28 14:59:12,094 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-28 14:59:12,100 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_145912.png
2025-07-28 14:59:12,105 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-28 14:59:12,107 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-28 14:59:12,109 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-28 14:59:12,111 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-28 14:59:12,113 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-28 14:59:12,115 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-28 14:59:12,117 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-28 14:59:12,124 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 14:59:12,133 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_145912.png
2025-07-28 14:59:12,139 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 14:59:12,140 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-28 14:59:12,145 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_145912.png
2025-07-28 14:59:12,189 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 14:59:12,277 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-28 14:59:12,301 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 14:59:12,305 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 14:59:12,609 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-28 14:59:13,378 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 14:59:13,380 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 14:59:13,382 - modules.wechat_auto_add_simple - INFO - ✅ 13667224416 添加朋友操作执行成功
2025-07-28 14:59:13,383 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 14:59:15,386 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-28 14:59:15,386 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-28 14:59:15,387 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-28 14:59:15,387 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 14:59:15,387 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 14:59:15,388 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 14:59:15,388 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 14:59:15,388 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 14:59:15,388 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-28 14:59:15,389 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13667224416
2025-07-28 14:59:15,389 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-28 14:59:15,389 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:536 in _execute_auto_add_friend
2025-07-28 14:59:15,390 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:614 in _handle_friend_request_window
2025-07-28 14:59:15,390 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-28 14:59:15,390 - modules.friend_request_window - INFO -    📱 phone: '13667224416'
2025-07-28 14:59:15,391 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-28 14:59:15,391 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-28 14:59:15,873 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-28 14:59:15,874 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-28 14:59:15,874 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-28 14:59:15,875 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 14:59:15,876 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13667224416
2025-07-28 14:59:15,876 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-28 14:59:15,876 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 14:59:15,877 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-28 14:59:15,877 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-28 14:59:15,877 - modules.friend_request_window - INFO -    📱 手机号码: 13667224416
2025-07-28 14:59:15,878 - modules.friend_request_window - INFO -    🆔 准考证: 015825120161
2025-07-28 14:59:15,878 - modules.friend_request_window - INFO -    👤 姓名: 雷慰
2025-07-28 14:59:15,878 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 14:59:15,878 - modules.friend_request_window - INFO -    📝 备注格式: '015825120161-雷慰-2025-07-28 14:59:15'
2025-07-28 14:59:15,879 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 14:59:15,879 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '015825120161-雷慰-2025-07-28 14:59:15'
2025-07-28 14:59:15,880 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 14:59:15,882 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 10422988, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-28 14:59:15,889 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 10422988)
2025-07-28 14:59:15,889 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-28 14:59:15,890 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-28 14:59:15,890 - modules.friend_request_window - INFO - 🔄 激活窗口: 10422988
2025-07-28 14:59:16,593 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-28 14:59:16,594 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-28 14:59:16,594 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-28 14:59:16,595 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:471 in _analyze_search_result
2025-07-28 14:59:16,595 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-28 14:59:16,595 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:536 in _execute_auto_add_friend
2025-07-28 14:59:16,595 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-28 14:59:16,596 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:614 in _handle_friend_request_window
2025-07-28 14:59:16,596 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-28 14:59:16,596 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1158 in execute_friend_request_flow
2025-07-28 14:59:16,597 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-28 14:59:16,597 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-28 14:59:16,597 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-28 14:59:16,597 - modules.friend_request_window - INFO -    📝 remark参数: '015825120161-雷慰-2025-07-28 14:59:15' (类型: <class 'str'>, 长度: 35)
2025-07-28 14:59:16,597 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-28 14:59:16,598 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '015825120161-雷慰-2025-07-28 14:59:15'
2025-07-28 14:59:16,598 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-28 14:59:16,598 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-28 14:59:16,598 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-28 14:59:16,599 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-28 14:59:16,599 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-28 14:59:16,600 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-28 14:59:16,600 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-28 14:59:17,522 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-28 14:59:22,762 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-28 14:59:22,763 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-28 14:59:22,763 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-28 14:59:22,763 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-28 14:59:22,764 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'main_controller.py完全没有调用modules\wechat_auto_add_si...' (前50字符)
2025-07-28 14:59:23,073 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 14:59:23,073 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 14:59:23,976 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 14:59:23,986 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 14:59:23,986 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-28 14:59:23,987 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-28 14:59:23,988 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-28 14:59:23,989 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-28 14:59:24,489 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-28 14:59:24,490 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-28 14:59:24,490 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-28 14:59:24,490 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-28 14:59:24,490 - modules.friend_request_window - INFO -    📝 内容: '015825120161-雷慰-2025-07-28 14:59:15'
2025-07-28 14:59:24,491 - modules.friend_request_window - INFO -    📏 内容长度: 35 字符
2025-07-28 14:59:24,491 - modules.friend_request_window - INFO -    🔤 内容编码: b'015825120161-\xe9\x9b\xb7\xe6\x85\xb0-2025-07-28 14:59:15'
2025-07-28 14:59:24,491 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-28 14:59:25,402 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-28 14:59:30,647 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-28 14:59:30,648 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-28 14:59:30,648 - modules.friend_request_window - INFO -    📝 原始文本: '015825120161-雷慰-2025-07-28 14:59:15'
2025-07-28 14:59:30,649 - modules.friend_request_window - INFO -    📏 文本长度: 35 字符
2025-07-28 14:59:30,649 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'main_controller.py完全没有调用modules\wechat_auto_add_si...' (前50字符)
2025-07-28 14:59:30,959 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 14:59:30,959 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 14:59:31,862 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 14:59:31,873 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 14:59:31,873 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '015825120161-雷慰-2025-07-28 14:59:15'
2025-07-28 14:59:31,874 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-28 14:59:31,875 - modules.friend_request_window - INFO -    📝 已填写内容: '015825120161-雷慰-2025-07-28 14:59:15'
2025-07-28 14:59:31,875 - modules.friend_request_window - INFO -    � 内容长度: 35 字符
2025-07-28 14:59:32,376 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '015825120161-雷慰-2025-07-28 14:59:15'
2025-07-28 14:59:32,377 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-28 14:59:32,377 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-28 14:59:32,377 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-28 14:59:32,377 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-28 14:59:32,378 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-28 14:59:32,378 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-28 14:59:33,179 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-28 14:59:33,179 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-28 14:59:33,179 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-28 14:59:33,785 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 14:59:33,785 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-28 14:59:33,785 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-28 14:59:33,786 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-28 14:59:34,287 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-28 14:59:34,289 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-28 14:59:34,290 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-28 14:59:34,290 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-28 14:59:34,290 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-28 14:59:34,291 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-28 14:59:34,291 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-28 14:59:34,291 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-28 14:59:34,291 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-28 14:59:34,292 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-28 14:59:34,292 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-28 14:59:34,292 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-28 14:59:34,293 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-28 14:59:34,293 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-28 14:59:34,294 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-28 14:59:34,295 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-28 14:59:34,295 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-28 14:59:34,296 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-28 14:59:34,296 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-28 14:59:34,798 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-28 14:59:34,798 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-28 14:59:34,799 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-28 14:59:34,799 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-28 14:59:34,799 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-28 14:59:34,799 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-28 14:59:34,800 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-28 14:59:34,800 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-28 14:59:35,727 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-28 14:59:35,727 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-28 14:59:35,728 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-28 14:59:35,728 - modules.friend_request_window - INFO - 🔄 关闭错误相关窗口...
2025-07-28 14:59:35,746 - modules.friend_request_window - INFO - ✅ 频率错误处理完成
2025-07-28 14:59:35,747 - modules.friend_request_window - INFO - ✅ 原有频率错误处理成功，已切换到微信2
2025-07-28 14:59:35,747 - modules.friend_request_window - WARNING - ⚠️ 检测到并处理了频率错误，已切换到微信2
2025-07-28 14:59:35,748 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-28 14:59:35,748 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-28 14:59:35,749 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 14:59:35,749 - modules.friend_request_window - INFO -    📝 备注信息: '015825120161-雷慰-2025-07-28 14:59:15'
2025-07-28 14:59:36,250 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-28 14:59:36,251 - modules.wechat_auto_add_simple - INFO - ✅ 13667224416 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-28 14:59:36,251 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 13667224416
2025-07-28 14:59:39,762 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 7/3003
2025-07-28 14:59:39,763 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13618662031 (陈平星)
2025-07-28 14:59:46,328 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13618662031
2025-07-28 14:59:46,328 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 14:59:46,328 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13618662031 执行添加朋友操作...
2025-07-28 14:59:46,329 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 14:59:46,329 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 14:59:46,330 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 14:59:46,332 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 14:59:46,339 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 14:59:46,342 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 14:59:46,342 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 14:59:46,342 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 14:59:46,343 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 14:59:46,343 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 14:59:46,343 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 14:59:46,343 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 14:59:46,349 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-28 14:59:46,355 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 14:59:46,359 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 14:59:46,362 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 14:59:46,368 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-28 14:59:46,371 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-28 14:59:46,874 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-28 14:59:46,876 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-28 14:59:46,932 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差39.01, 边缘比例0.0362
2025-07-28 14:59:46,942 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_145946.png
2025-07-28 14:59:46,947 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 14:59:46,958 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 14:59:46,964 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-28 14:59:46,969 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-28 14:59:46,972 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-28 14:59:46,977 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_145946.png
2025-07-28 14:59:46,979 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-28 14:59:46,987 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-28 14:59:46,994 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-28 14:59:46,996 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-28 14:59:47,001 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-28 14:59:47,004 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-28 14:59:47,007 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-28 14:59:47,010 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 14:59:47,018 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_145947.png
2025-07-28 14:59:47,022 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 14:59:47,027 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-28 14:59:47,031 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_145947.png
2025-07-28 14:59:47,055 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 14:59:47,058 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-28 14:59:47,060 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 14:59:47,062 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 14:59:47,372 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-28 14:59:48,151 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 14:59:48,153 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 14:59:48,155 - modules.wechat_auto_add_simple - INFO - ✅ 13618662031 添加朋友操作执行成功
2025-07-28 14:59:48,155 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 14:59:50,157 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-28 14:59:50,157 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-28 14:59:50,157 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-28 14:59:50,158 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 14:59:50,158 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 14:59:50,159 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 14:59:50,159 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 14:59:50,159 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 14:59:50,160 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-28 14:59:50,160 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13618662031
2025-07-28 14:59:50,160 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-28 14:59:50,161 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:536 in _execute_auto_add_friend
2025-07-28 14:59:50,161 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:614 in _handle_friend_request_window
2025-07-28 14:59:50,161 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-28 14:59:50,162 - modules.friend_request_window - INFO -    📱 phone: '13618662031'
2025-07-28 14:59:50,162 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-28 14:59:50,162 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-28 14:59:50,645 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-28 14:59:50,646 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-28 14:59:50,646 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-28 14:59:50,646 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 14:59:50,648 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13618662031
2025-07-28 14:59:50,648 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-28 14:59:50,648 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 14:59:50,649 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-28 14:59:50,649 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-28 14:59:50,649 - modules.friend_request_window - INFO -    📱 手机号码: 13618662031
2025-07-28 14:59:50,650 - modules.friend_request_window - INFO -    🆔 准考证: 015825120162
2025-07-28 14:59:50,650 - modules.friend_request_window - INFO -    👤 姓名: 陈平星
2025-07-28 14:59:50,650 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 14:59:50,651 - modules.friend_request_window - INFO -    📝 备注格式: '015825120162-陈平星-2025-07-28 14:59:50'
2025-07-28 14:59:50,651 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 14:59:50,651 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '015825120162-陈平星-2025-07-28 14:59:50'
2025-07-28 14:59:50,652 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 14:59:50,654 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 2231318, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-28 14:59:50,658 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 2231318)
2025-07-28 14:59:50,659 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-28 14:59:50,659 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-28 14:59:50,660 - modules.friend_request_window - INFO - 🔄 激活窗口: 2231318
2025-07-28 14:59:51,362 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-28 14:59:51,362 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-28 14:59:51,363 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-28 14:59:51,363 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:471 in _analyze_search_result
2025-07-28 14:59:51,363 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-28 14:59:51,364 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:536 in _execute_auto_add_friend
2025-07-28 14:59:51,364 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-28 14:59:51,364 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:614 in _handle_friend_request_window
2025-07-28 14:59:51,365 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-28 14:59:51,365 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1158 in execute_friend_request_flow
2025-07-28 14:59:51,365 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-28 14:59:51,365 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-28 14:59:51,366 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-28 14:59:51,366 - modules.friend_request_window - INFO -    📝 remark参数: '015825120162-陈平星-2025-07-28 14:59:50' (类型: <class 'str'>, 长度: 36)
2025-07-28 14:59:51,366 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-28 14:59:51,366 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '015825120162-陈平星-2025-07-28 14:59:50'
2025-07-28 14:59:51,367 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-28 14:59:51,367 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-28 14:59:51,367 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-28 14:59:51,367 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-28 14:59:51,368 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-28 14:59:51,368 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-28 14:59:51,368 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-28 14:59:52,285 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-28 14:59:57,529 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-28 14:59:57,529 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-28 14:59:57,530 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-28 14:59:57,530 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-28 14:59:57,531 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'main_controller.py完全没有调用modules\wechat_auto_add_si...' (前50字符)
2025-07-28 14:59:57,838 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 14:59:57,839 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 14:59:58,741 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 14:59:58,750 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 14:59:58,750 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-28 14:59:58,751 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-28 14:59:58,752 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-28 14:59:58,752 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-28 14:59:59,253 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-28 14:59:59,253 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-28 14:59:59,253 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-28 14:59:59,254 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-28 14:59:59,254 - modules.friend_request_window - INFO -    📝 内容: '015825120162-陈平星-2025-07-28 14:59:50'
2025-07-28 14:59:59,254 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-28 14:59:59,255 - modules.friend_request_window - INFO -    🔤 内容编码: b'015825120162-\xe9\x99\x88\xe5\xb9\xb3\xe6\x98\x9f-2025-07-28 14:59:50'
2025-07-28 14:59:59,255 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-28 15:00:00,167 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-28 15:00:05,413 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-28 15:00:05,414 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-28 15:00:05,414 - modules.friend_request_window - INFO -    📝 原始文本: '015825120162-陈平星-2025-07-28 14:59:50'
2025-07-28 15:00:05,414 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-28 15:00:05,415 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'main_controller.py完全没有调用modules\wechat_auto_add_si...' (前50字符)
2025-07-28 15:00:05,726 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 15:00:05,726 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 15:00:06,632 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 15:00:06,644 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 15:00:06,644 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '015825120162-陈平星-2025-07-28 14:59:50'
2025-07-28 15:00:06,645 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-28 15:00:06,646 - modules.friend_request_window - INFO -    📝 已填写内容: '015825120162-陈平星-2025-07-28 14:59:50'
2025-07-28 15:00:06,646 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-28 15:00:07,147 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '015825120162-陈平星-2025-07-28 14:59:50'
2025-07-28 15:00:07,148 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-28 15:00:07,149 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-28 15:00:07,149 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-28 15:00:07,149 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-28 15:00:07,150 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-28 15:00:07,150 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-28 15:00:07,950 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-28 15:00:07,951 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-28 15:00:07,951 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-28 15:00:08,566 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:00:08,567 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-28 15:00:08,567 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-28 15:00:08,567 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-28 15:00:09,083 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:00:09,313 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:00:09,560 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:00:09,791 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:00:10,021 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:00:10,254 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:00:10,487 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:00:10,720 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:00:10,954 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:00:11,189 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:00:11,421 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:00:11,655 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:00:11,895 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:00:12,127 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:00:12,362 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:00:12,592 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:00:12,825 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:00:13,056 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:00:13,287 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:00:13,517 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:00:13,735 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-28 15:00:13,736 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-28 15:00:14,737 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 15:00:14,739 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-28 15:00:14,740 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-28 15:00:14,740 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-28 15:00:14,740 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-28 15:00:14,740 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 15:00:14,741 - modules.friend_request_window - INFO -    📝 备注信息: '015825120162-陈平星-2025-07-28 14:59:50'
2025-07-28 15:00:15,241 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-28 15:00:15,242 - modules.wechat_auto_add_simple - INFO - ✅ 13618662031 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-28 15:00:15,242 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 13618662031
2025-07-28 15:00:18,838 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 8/3003
2025-07-28 15:00:18,839 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18771980680 (卢雅丽)
2025-07-28 15:00:25,409 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18771980680
2025-07-28 15:00:25,409 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 15:00:25,410 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18771980680 执行添加朋友操作...
2025-07-28 15:00:25,410 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 15:00:25,410 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 15:00:25,411 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 15:00:25,414 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 15:00:25,418 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 15:00:25,423 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 15:00:25,424 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 15:00:25,425 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 15:00:25,426 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 15:00:25,426 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 15:00:25,426 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 15:00:25,427 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 15:00:25,432 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-28 15:00:25,436 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 15:00:25,439 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 15:00:25,442 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 15:00:25,445 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-28 15:00:25,452 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-28 15:00:25,955 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-28 15:00:25,957 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-28 15:00:26,010 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-07-28 15:00:26,012 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-07-28 15:00:26,024 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_150026.png
2025-07-28 15:00:26,027 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 15:00:26,029 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 15:00:26,033 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-28 15:00:26,036 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-28 15:00:26,038 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-28 15:00:26,044 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_150026.png
2025-07-28 15:00:26,052 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-28 15:00:26,054 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-28 15:00:26,057 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-28 15:00:26,059 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-28 15:00:26,065 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-28 15:00:26,068 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-28 15:00:26,070 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-28 15:00:26,073 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 15:00:26,082 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_150026.png
2025-07-28 15:00:26,085 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 15:00:26,087 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-28 15:00:26,092 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_150026.png
2025-07-28 15:00:26,116 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 15:00:26,119 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-28 15:00:26,122 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 15:00:26,125 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 15:00:26,433 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-28 15:00:27,203 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 15:00:27,205 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 15:00:27,207 - modules.wechat_auto_add_simple - INFO - ✅ 18771980680 添加朋友操作执行成功
2025-07-28 15:00:27,208 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 15:00:29,210 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-28 15:00:29,210 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-28 15:00:29,211 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-28 15:00:29,211 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 15:00:29,211 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 15:00:29,212 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 15:00:29,212 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 15:00:29,212 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 15:00:29,213 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-28 15:00:29,213 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18771980680
2025-07-28 15:00:29,214 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-28 15:00:29,214 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:536 in _execute_auto_add_friend
2025-07-28 15:00:29,216 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:614 in _handle_friend_request_window
2025-07-28 15:00:29,217 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-28 15:00:29,219 - modules.friend_request_window - INFO -    📱 phone: '18771980680'
2025-07-28 15:00:29,219 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-28 15:00:29,221 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-28 15:00:29,707 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-28 15:00:29,707 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-28 15:00:29,708 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-28 15:00:29,708 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 15:00:29,709 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18771980680
2025-07-28 15:00:29,709 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-28 15:00:29,710 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 15:00:29,710 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-28 15:00:29,711 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-28 15:00:29,711 - modules.friend_request_window - INFO -    📱 手机号码: 18771980680
2025-07-28 15:00:29,711 - modules.friend_request_window - INFO -    🆔 准考证: 015825120164
2025-07-28 15:00:29,711 - modules.friend_request_window - INFO -    👤 姓名: 卢雅丽
2025-07-28 15:00:29,712 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 15:00:29,712 - modules.friend_request_window - INFO -    📝 备注格式: '015825120164-卢雅丽-2025-07-28 15:00:29'
2025-07-28 15:00:29,712 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 15:00:29,713 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '015825120164-卢雅丽-2025-07-28 15:00:29'
2025-07-28 15:00:29,713 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 15:00:29,718 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-28 15:00:29,718 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-28 15:00:29,720 - modules.wechat_auto_add_simple - INFO - ✅ 18771980680 申请添加朋友窗口处理完成: 申请添加朋友窗口处理失败: 未找到好友申请窗口
2025-07-28 15:00:29,720 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 18771980680
2025-07-28 15:00:33,243 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 9/3003
2025-07-28 15:00:33,243 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 17707167456 (李珂)
2025-07-28 15:00:39,811 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 17707167456
2025-07-28 15:00:39,813 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 15:00:39,814 - modules.wechat_auto_add_simple - INFO - 👥 开始为 17707167456 执行添加朋友操作...
2025-07-28 15:00:39,814 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 15:00:39,814 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 15:00:39,815 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 15:00:39,818 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 15:00:39,822 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 15:00:39,826 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 15:00:39,826 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 15:00:39,827 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 15:00:39,827 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 15:00:39,830 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 15:00:39,831 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 15:00:39,831 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 15:00:39,838 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-28 15:00:39,841 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 15:00:39,843 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 15:00:39,851 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 15:00:39,854 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-28 15:00:39,857 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-28 15:00:40,360 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-28 15:00:40,363 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-28 15:00:40,429 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差29.13, 边缘比例0.0409
2025-07-28 15:00:40,444 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_150040.png
2025-07-28 15:00:40,451 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 15:00:40,455 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 15:00:40,458 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-28 15:00:40,460 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-28 15:00:40,467 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-28 15:00:40,474 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_150040.png
2025-07-28 15:00:40,477 - WeChatAutoAdd - INFO - 底部区域原始检测到 32 个轮廓
2025-07-28 15:00:40,485 - WeChatAutoAdd - INFO - 重要轮廓: 位置(117,236), 尺寸93x31, 长宽比3.00, 已知特征:False
2025-07-28 15:00:40,489 - WeChatAutoAdd - INFO - 发现目标候选: 位置(117,236), 尺寸93x31
2025-07-28 15:00:40,491 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(306,209), 尺寸1x86, 长宽比0.01, 面积86
2025-07-28 15:00:40,495 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(242,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-28 15:00:40,501 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(238,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-28 15:00:40,505 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(231,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-28 15:00:40,513 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,209), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:00:40,526 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(221,209), 尺寸4x1, 长宽比4.00, 面积4
2025-07-28 15:00:40,537 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-28 15:00:40,543 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(210,209), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:00:40,573 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(202,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-28 15:00:40,688 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,209), 尺寸7x1, 长宽比7.00, 面积7
2025-07-28 15:00:40,716 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(190,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-28 15:00:40,720 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(187,209), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:00:40,724 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(180,209), 尺寸4x1, 长宽比4.00, 面积4
2025-07-28 15:00:40,727 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(175,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-28 15:00:40,734 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,209), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:00:40,736 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(158,209), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 15:00:40,739 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(154,209), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:00:40,741 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(141,209), 尺寸10x1, 长宽比10.00, 面积10
2025-07-28 15:00:40,747 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-28 15:00:40,751 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(129,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-28 15:00:40,754 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(118,209), 尺寸5x1, 长宽比5.00, 面积5
2025-07-28 15:00:40,758 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(104,209), 尺寸11x1, 长宽比11.00, 面积11
2025-07-28 15:00:40,763 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(100,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-28 15:00:40,768 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(94,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-28 15:00:40,770 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(89,209), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:00:40,774 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(85,209), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:00:40,777 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(81,209), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:00:40,784 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(73,209), 尺寸6x1, 长宽比6.00, 面积6
2025-07-28 15:00:40,786 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(23,209), 尺寸282x146, 长宽比1.93, 面积41172
2025-07-28 15:00:40,791 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,209), 尺寸2x88, 长宽比0.02, 面积176
2025-07-28 15:00:40,793 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-28 15:00:40,800 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-28 15:00:40,802 - WeChatAutoAdd - INFO - 选择目标候选按钮: Y=236, 符合'添加到通讯录'特征
2025-07-28 15:00:40,806 - WeChatAutoAdd - INFO - 在底部找到按钮: (163, 251), 尺寸: 93x31, 位置得分: 1.0, 目标候选: True, 绿色按钮: False, 特殊位置: False
2025-07-28 15:00:40,809 - WeChatAutoAdd - INFO - 检测到高置信度按钮，跳过文字验证
2025-07-28 15:00:40,822 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_150040.png
2025-07-28 15:00:40,825 - WeChatAutoAdd - INFO - 检测到高置信度按钮（已知特征/绿色按钮），跳过文字验证
2025-07-28 15:00:40,827 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 15:00:41,134 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(163, 251) -> 屏幕坐标(1363, 251)
2025-07-28 15:00:42,072 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 15:00:42,087 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 15:00:42,120 - modules.wechat_auto_add_simple - INFO - ✅ 17707167456 添加朋友操作执行成功
2025-07-28 15:00:42,121 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 15:00:44,123 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-28 15:00:44,123 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-28 15:00:44,124 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-28 15:00:44,124 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 15:00:44,124 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 15:00:44,125 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 15:00:44,125 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 15:00:44,125 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 15:00:44,126 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-28 15:00:44,126 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 17707167456
2025-07-28 15:00:44,127 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-28 15:00:44,133 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:536 in _execute_auto_add_friend
2025-07-28 15:00:44,135 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:614 in _handle_friend_request_window
2025-07-28 15:00:44,135 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-28 15:00:44,135 - modules.friend_request_window - INFO -    📱 phone: '17707167456'
2025-07-28 15:00:44,136 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-28 15:00:44,136 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
