2025-07-29 12:50:47,175 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 12:50:47,176 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 12:50:47,176 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-29 12:50:47,177 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 12:50:47,179 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-29 12:50:47,179 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 12:50:47,179 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 12:50:47,180 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 12:50:47,181 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 12:50:47,181 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 12:50:47,183 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:50:47,202 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 12:50:47,206 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250729_125047.log
2025-07-29 12:50:47,208 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-29 12:50:47,209 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-29 12:50:47,209 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-29 12:50:47,210 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-29 12:50:47,214 - __main__ - INFO - 📅 当前北京时间: 2025-07-29 20:50:47
2025-07-29 12:50:47,217 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-29 12:50:47,218 - __main__ - INFO - 📅 启动时间: 2025-07-29 20:50:47
2025-07-29 12:50:47,219 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-29 12:50:47,221 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 12:50:47,224 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 12:50:47,225 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 12:50:47,226 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 12:50:47,231 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 12:50:47,243 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-29 12:50:47,244 - __main__ - INFO - ✅ 找到 2 个微信窗口
2025-07-29 12:50:47,244 - __main__ - INFO -   窗口 1: 微信 (句柄: 197994)
2025-07-29 12:50:47,247 - __main__ - INFO -   窗口 2: 微信 (句柄: 525668)
2025-07-29 12:50:47,248 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-29 12:50:48,423 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-29 12:50:48,424 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-29 12:50:48,424 - __main__ - INFO - 📋 待处理联系人数: 2960
2025-07-29 12:50:48,425 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-29 12:50:48,425 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2960
2025-07-29 12:50:48,425 - __main__ - INFO - 
============================================================
2025-07-29 12:50:48,425 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口
2025-07-29 12:50:48,426 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-29 12:50:48,426 - __main__ - INFO - ============================================================
2025-07-29 12:50:48,426 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-29 12:50:48,427 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-29 12:50:48,427 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-29 12:50:48,427 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-29 12:50:48,428 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 12:50:48,851 - modules.window_manager - INFO - ✅ 临时置顶策略成功
2025-07-29 12:50:48,852 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 12:50:48,852 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 12:50:48,853 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 12:50:48,853 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 12:50:48,853 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 12:50:48,854 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 12:50:48,854 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 12:50:48,854 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 12:50:48,855 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 12:50:49,056 - modules.window_manager - INFO - ✅ 窗口可见且非最小化（部分成功）
2025-07-29 12:50:49,057 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 12:50:49,057 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-29 12:50:49,057 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 12:50:49,469 - modules.window_manager - INFO - ✅ 临时置顶策略成功
2025-07-29 12:50:49,469 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 12:50:49,470 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 12:50:49,470 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 12:50:49,470 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 12:50:49,470 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 12:50:49,471 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 12:50:49,471 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 12:50:49,471 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 12:50:49,472 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 12:50:49,675 - modules.window_manager - INFO - ✅ 窗口可见且非最小化（部分成功）
2025-07-29 12:50:49,676 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 12:50:49,679 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-29 12:50:49,980 - modules.window_manager - WARNING - ⚠️ 窗口可能未完全置于最前面 (当前前台: 525668)
2025-07-29 12:50:49,980 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 12:50:49,980 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 12:50:49,981 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 12:50:49,981 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 12:50:49,982 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 12:50:49,985 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-29 12:50:49,986 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-29 12:50:50,987 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-29 12:50:50,987 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-29 12:50:50,987 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-29 12:50:50,988 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-29 12:50:50,988 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-29 12:50:50,989 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-29 12:50:50,989 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-29 12:50:50,990 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-29 12:50:51,191 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-29 12:50:51,192 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-29 12:50:53,573 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-29 12:50:53,575 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-29 12:50:53,575 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.1 秒...
2025-07-29 12:50:55,698 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-29 12:50:55,906 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-29 12:50:55,911 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-29 12:50:58,325 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-29 12:50:58,326 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-29 12:50:58,326 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.5 秒...
2025-07-29 12:51:00,834 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-29 12:51:01,036 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-29 12:51:01,037 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-29 12:51:03,420 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-29 12:51:03,420 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-29 12:51:03,421 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.8 秒...
2025-07-29 12:51:05,238 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-29 12:51:05,440 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-29 12:51:05,440 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-29 12:51:07,819 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-29 12:51:07,819 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-29 12:51:07,820 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.6 秒...
2025-07-29 12:51:10,371 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-29 12:51:10,572 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-29 12:51:10,572 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-29 12:51:12,952 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-29 12:51:12,952 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-29 12:51:12,953 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 12:51:12,954 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 12:51:12,955 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 12:51:12,957 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 12:51:12,957 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 12:51:12,961 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 12:51:12,963 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 12:51:12,972 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 2362446, 进程: Weixin.exe)
2025-07-29 12:51:12,975 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-29 12:51:12,975 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 2362446)
2025-07-29 12:51:12,976 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 2362446) - 增强版
2025-07-29 12:51:13,280 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 12:51:13,283 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 12:51:13,284 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 12:51:13,284 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 12:51:13,285 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-29 12:51:13,285 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 12:51:13,490 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-29 12:51:13,490 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 12:51:13,692 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 12:51:13,693 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 12:51:13,693 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-29 12:51:13,693 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-29 12:51:13,694 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-29 12:51:13,694 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-29 12:51:13,694 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-29 12:51:14,695 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-29 12:51:14,695 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 12:51:14,697 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 12:51:14,699 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 12:51:14,700 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 12:51:14,701 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 12:51:14,703 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 2362446, 进程: Weixin.exe)
2025-07-29 12:51:14,704 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-29 12:51:14,706 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-29 12:51:14,708 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-29 12:51:14,708 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-29 12:51:14,708 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-29 12:51:14,709 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 12:51:15,020 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 12:51:15,021 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 12:51:15,021 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 12:51:15,021 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 12:51:15,022 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 12:51:15,022 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 12:51:15,022 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 12:51:15,022 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 12:51:15,023 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 12:51:15,023 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 12:51:15,225 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 12:51:15,225 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 12:51:15,226 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-29 12:51:15,527 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 12:51:15,527 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-29 12:51:15,527 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-29 12:51:16,528 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-29 12:51:16,529 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-29 12:51:16,529 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-29 12:51:16,534 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250729_125116.log
2025-07-29 12:51:16,535 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-29 12:51:16,535 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-29 12:51:16,535 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-29 12:51:16,536 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-29 12:51:16,537 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-29 12:51:16,538 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-29 12:51:16,538 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-29 12:51:16,539 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-29 12:51:16,539 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-29 12:51:16,539 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-29 12:51:16,539 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-29 12:51:16,540 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 12:51:16,541 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 2362446
2025-07-29 12:51:16,541 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 2362446) - 增强版
2025-07-29 12:51:16,853 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 12:51:16,854 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 12:51:16,855 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 12:51:16,855 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 12:51:16,855 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-29 12:51:16,856 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 12:51:16,856 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-29 12:51:16,857 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 12:51:17,058 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 12:51:17,059 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 12:51:17,062 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 2362446 (API返回: None)
2025-07-29 12:51:17,363 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 12:51:17,363 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-29 12:51:17,365 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-29 12:51:17,366 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-29 12:51:17,367 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 12:51:17,367 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-29 12:51:17,367 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-29 12:51:17,372 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-29 12:51:17,372 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-29 12:51:17,829 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-29 12:51:17,829 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 12:51:18,154 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2960 个
2025-07-29 12:51:18,155 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2960 个 (总计: 3135 个)
2025-07-29 12:51:18,155 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 10 个联系人后切换
2025-07-29 12:51:18,155 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {}
2025-07-29 12:51:18,156 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:51:18,156 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-29 12:51:18,156 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2960
2025-07-29 12:51:18,157 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 17704006423 (刘林蓓)
2025-07-29 12:51:18,158 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:51:24,866 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 17704006423
2025-07-29 12:51:24,867 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 12:51:24,867 - modules.wechat_auto_add_simple - INFO - 👥 开始为 17704006423 执行添加朋友操作...
2025-07-29 12:51:24,867 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 12:51:24,868 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 12:51:24,868 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 12:51:24,869 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 12:51:24,874 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-29 12:51:24,877 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 12:51:24,878 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 12:51:24,878 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 12:51:24,878 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 12:51:24,879 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 12:51:24,879 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 12:51:24,879 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 12:51:24,887 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 12:51:24,889 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 12:51:24,890 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 12:51:24,891 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 12:51:24,892 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-29 12:51:24,894 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 12:51:25,395 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 12:51:25,398 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 12:51:25,492 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差28.51, 边缘比例0.0350
2025-07-29 12:51:25,501 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_125125.png
2025-07-29 12:51:25,505 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 12:51:25,507 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 12:51:25,508 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 12:51:25,508 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 12:51:25,510 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 12:51:25,517 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_125125.png
2025-07-29 12:51:25,520 - WeChatAutoAdd - INFO - 底部区域原始检测到 38 个轮廓
2025-07-29 12:51:25,522 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 12:51:25,523 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 12:51:25,524 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:51:25,526 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-07-29 12:51:25,527 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-07-29 12:51:25,528 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-07-29 12:51:25,529 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-07-29 12:51:25,535 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:51:25,538 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-07-29 12:51:25,539 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-07-29 12:51:25,540 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-07-29 12:51:25,541 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:51:25,542 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-07-29 12:51:25,544 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 12:51:25,546 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:51:25,550 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-29 12:51:25,552 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-29 12:51:25,554 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:51:25,556 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:51:25,557 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-07-29 12:51:25,559 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-07-29 12:51:25,562 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-07-29 12:51:25,568 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 12:51:25,568 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-07-29 12:51:25,570 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-07-29 12:51:25,570 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-07-29 12:51:25,571 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-07-29 12:51:25,572 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-07-29 12:51:25,574 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-07-29 12:51:25,575 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-07-29 12:51:25,576 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-07-29 12:51:25,578 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-07-29 12:51:25,579 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-07-29 12:51:25,585 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-07-29 12:51:25,587 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-07-29 12:51:25,591 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-07-29 12:51:25,592 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-07-29 12:51:25,593 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 12:51:25,596 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-07-29 12:51:25,603 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-07-29 12:51:25,604 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-29 12:51:25,607 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-29 12:51:25,621 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_125125.png
2025-07-29 12:51:25,623 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-29 12:51:25,624 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-07-29 12:51:25,629 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250729_125125.png
2025-07-29 12:51:25,745 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-29 12:51:25,746 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-07-29 12:51:25,749 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-29 12:51:25,750 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 12:51:26,054 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-07-29 12:51:26,834 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 12:51:26,836 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 12:51:26,921 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:51:26,923 - modules.wechat_auto_add_simple - INFO - ✅ 17704006423 添加朋友操作执行成功
2025-07-29 12:51:26,924 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:51:26,924 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 12:51:28,926 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 12:51:28,927 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 12:51:28,927 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 12:51:28,927 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 12:51:28,928 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 12:51:28,928 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 12:51:28,928 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 12:51:28,932 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 12:51:28,936 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 12:51:28,936 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 17704006423
2025-07-29 12:51:28,942 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 12:51:28,943 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-29 12:51:28,944 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:695 in _handle_friend_request_window
2025-07-29 12:51:28,944 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 12:51:28,945 - modules.friend_request_window - INFO -    📱 phone: '17704006423'
2025-07-29 12:51:28,945 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 12:51:28,946 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 12:51:29,492 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-29 12:51:29,492 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 12:51:29,492 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 12:51:29,493 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 12:51:29,494 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 17704006423
2025-07-29 12:51:29,494 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 12:51:29,495 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 12:51:29,499 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 12:51:29,499 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 12:51:29,502 - modules.friend_request_window - INFO -    📱 手机号码: 17704006423
2025-07-29 12:51:29,502 - modules.friend_request_window - INFO -    🆔 准考证: 014325110087
2025-07-29 12:51:29,503 - modules.friend_request_window - INFO -    👤 姓名: 刘林蓓
2025-07-29 12:51:29,504 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 12:51:29,505 - modules.friend_request_window - INFO -    📝 备注格式: '014325110087-刘林蓓-2025-07-29 20:51:29'
2025-07-29 12:51:29,506 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 12:51:29,507 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110087-刘林蓓-2025-07-29 20:51:29'
2025-07-29 12:51:29,510 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 12:51:29,512 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-29 12:51:29,523 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-29 12:51:29,523 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:51:29,524 - modules.wechat_auto_add_simple - INFO - ✅ 17704006423 申请添加朋友窗口处理完成: 申请添加朋友窗口处理失败: 未找到好友申请窗口
2025-07-29 12:51:29,524 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 17704006423
2025-07-29 12:51:29,525 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:51:33,356 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2960
2025-07-29 12:51:33,356 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13886107212 (黄欣)
2025-07-29 12:51:33,357 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:51:39,935 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13886107212
2025-07-29 12:51:39,936 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 12:51:39,937 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13886107212 执行添加朋友操作...
2025-07-29 12:51:39,937 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 12:51:39,937 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 12:51:39,940 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 12:51:39,956 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 12:51:39,970 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-29 12:51:39,972 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 12:51:39,972 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 12:51:39,973 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 12:51:39,973 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 12:51:39,975 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 12:51:39,977 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 12:51:39,978 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 12:51:39,988 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 12:51:39,990 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 12:51:39,994 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 12:51:40,004 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 12:51:40,006 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-29 12:51:40,009 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 12:51:40,510 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 12:51:40,515 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 12:51:40,594 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差39.04, 边缘比例0.0422
2025-07-29 12:51:40,627 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_125140.png
2025-07-29 12:51:40,636 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 12:51:40,642 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 12:51:40,644 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 12:51:40,645 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 12:51:40,654 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 12:51:40,668 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_125140.png
2025-07-29 12:51:40,671 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-29 12:51:40,672 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,244), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-29 12:51:40,675 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-29 12:51:40,677 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 12:51:40,685 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-29 12:51:40,686 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-07-29 12:51:40,689 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 259), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-29 12:51:40,692 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-29 12:51:40,723 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_125140.png
2025-07-29 12:51:40,738 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-29 12:51:40,743 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 259)是否包含'添加到通讯录'文字
2025-07-29 12:51:40,753 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250729_125140.png
2025-07-29 12:51:40,778 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-29 12:51:40,786 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-29 12:51:40,789 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-29 12:51:40,792 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 12:51:41,094 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 259) -> 屏幕坐标(1364, 259)
2025-07-29 12:51:41,883 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 12:51:41,885 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 12:51:41,888 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:51:41,889 - modules.wechat_auto_add_simple - INFO - ✅ 13886107212 添加朋友操作执行成功
2025-07-29 12:51:41,890 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 12:51:41,891 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 12:51:43,920 - __main__ - INFO - ⏹️ 用户中断程序执行
