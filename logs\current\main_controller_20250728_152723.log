2025-07-28 15:27:23,206 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 15:27:23,207 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 15:27:23,207 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-28 15:27:23,208 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 15:27:23,209 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-28 15:27:23,210 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 15:27:23,211 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 15:27:23,213 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 15:27:23,213 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 15:27:23,214 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 15:27:23,214 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:27:23,219 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 15:27:23,222 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250728_152723.log
2025-07-28 15:27:23,230 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-28 15:27:23,231 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-28 15:27:23,232 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-28 15:27:23,233 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-28 15:27:23,236 - __main__ - INFO - 📅 当前北京时间: 2025-07-28 15:27:23
2025-07-28 15:27:23,237 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-28 15:27:23,239 - __main__ - INFO - 📅 启动时间: 2025-07-28 15:27:23
2025-07-28 15:27:23,242 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-28 15:27:23,243 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 15:27:23,774 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 15:27:23,774 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 15:27:24,322 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 15:27:24,322 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 15:27:24,324 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-28 15:27:24,325 - __main__ - INFO - ✅ 找到 2 个微信窗口
2025-07-28 15:27:24,325 - __main__ - INFO -   窗口 1: 微信 (句柄: 197994)
2025-07-28 15:27:24,325 - __main__ - INFO -   窗口 2: 微信 (句柄: 525668)
2025-07-28 15:27:24,325 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-28 15:27:25,468 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-28 15:27:25,468 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-28 15:27:25,468 - __main__ - INFO - 📋 待处理联系人数: 2993
2025-07-28 15:27:25,469 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-28 15:27:25,469 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2993
2025-07-28 15:27:25,469 - __main__ - INFO - 
============================================================
2025-07-28 15:27:25,469 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口
2025-07-28 15:27:25,470 - __main__ - INFO - ============================================================
2025-07-28 15:27:25,470 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-28 15:27:25,470 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-28 15:27:25,470 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-28 15:27:25,471 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-28 15:27:25,471 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 15:27:25,791 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 15:27:25,792 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 15:27:25,792 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 15:27:25,792 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 15:27:25,793 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 15:27:25,793 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 15:27:25,793 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 15:27:25,793 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 15:27:25,794 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 15:27:25,794 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 15:27:25,996 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 15:27:25,997 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 15:27:25,997 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-28 15:27:25,997 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 15:27:26,301 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 15:27:26,301 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 15:27:26,302 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 15:27:26,303 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 15:27:26,303 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 15:27:26,304 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 15:27:26,304 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 15:27:26,304 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 15:27:26,305 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 15:27:26,305 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 15:27:26,507 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 15:27:26,507 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 15:27:26,508 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-28 15:27:26,809 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 15:27:26,810 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 15:27:26,810 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 15:27:26,810 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 15:27:26,811 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 15:27:26,811 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 15:27:26,811 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-28 15:27:27,813 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-28 15:27:27,814 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-28 15:27:27,814 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-28 15:27:27,815 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-28 15:27:27,815 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-28 15:27:27,816 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-28 15:27:27,816 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-28 15:27:27,817 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-28 15:27:28,018 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-28 15:27:28,019 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-28 15:27:30,401 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-28 15:27:30,403 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-28 15:27:30,405 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.7 秒...
2025-07-28 15:27:33,136 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-28 15:27:33,337 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-28 15:27:33,337 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-28 15:27:35,715 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-28 15:27:35,716 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-28 15:27:35,716 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.0 秒...
2025-07-28 15:27:37,698 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-28 15:27:37,899 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-28 15:27:37,899 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-28 15:27:40,282 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-28 15:27:40,284 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-28 15:27:40,284 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.7 秒...
2025-07-28 15:27:42,023 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-28 15:27:42,224 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-28 15:27:42,225 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-28 15:27:44,598 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-28 15:27:44,599 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-28 15:27:44,599 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.7 秒...
2025-07-28 15:27:47,332 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-28 15:27:47,534 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-28 15:27:47,535 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-28 15:27:49,936 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-28 15:27:49,937 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-28 15:27:49,937 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-28 15:27:49,937 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-28 15:27:49,938 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 15:27:49,939 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 15:27:49,940 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 15:27:49,940 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 15:27:49,941 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 15:27:49,942 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 6685774, 进程: Weixin.exe)
2025-07-28 15:27:49,945 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-28 15:27:49,946 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 6685774)
2025-07-28 15:27:49,952 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 6685774) - 增强版
2025-07-28 15:27:50,256 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 15:27:50,256 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 15:27:50,257 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-28 15:27:50,257 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-28 15:27:50,257 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-28 15:27:50,258 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-28 15:27:50,463 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-28 15:27:50,463 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-28 15:27:50,666 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 15:27:50,666 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 15:27:50,666 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-28 15:27:50,667 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-28 15:27:50,667 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-28 15:27:50,667 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-28 15:27:50,667 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-28 15:27:51,668 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-28 15:27:51,669 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 15:27:51,670 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 15:27:51,670 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 15:27:51,671 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 15:27:51,671 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 15:27:51,673 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 6685774, 进程: Weixin.exe)
2025-07-28 15:27:51,675 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-28 15:27:51,676 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-28 15:27:51,677 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-28 15:27:51,678 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-28 15:27:51,679 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-28 15:27:51,679 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 15:27:51,987 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 15:27:51,987 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 15:27:51,988 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 15:27:51,988 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 15:27:51,988 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 15:27:51,989 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 15:27:51,989 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 15:27:51,989 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 15:27:51,990 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 15:27:51,990 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 15:27:52,191 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 15:27:52,192 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 15:27:52,193 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-28 15:27:52,494 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 15:27:52,495 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-28 15:27:53,496 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-28 15:27:53,497 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-28 15:27:53,498 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-28 15:27:53,501 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250728_152753.log
2025-07-28 15:27:53,501 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-28 15:27:53,504 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-28 15:27:53,504 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-28 15:27:53,505 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-28 15:27:53,507 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-28 15:27:53,508 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-28 15:27:53,508 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-28 15:27:53,509 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-28 15:27:53,512 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-28 15:27:53,513 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-28 15:27:53,513 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-28 15:27:53,515 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 15:27:53,516 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 6685774
2025-07-28 15:27:53,517 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 6685774) - 增强版
2025-07-28 15:27:53,825 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 15:27:53,825 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 15:27:53,826 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-28 15:27:53,826 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-28 15:27:53,826 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-28 15:27:53,827 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-28 15:27:53,827 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-28 15:27:53,828 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-28 15:27:54,030 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 15:27:54,030 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 15:27:54,033 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 6685774 (API返回: None)
2025-07-28 15:27:54,334 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 15:27:54,334 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-28 15:27:54,334 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-28 15:27:54,335 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-28 15:27:54,336 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 15:27:54,336 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-28 15:27:54,337 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-28 15:27:54,342 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-28 15:27:54,343 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-28 15:27:54,889 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-28 15:27:54,890 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 15:27:55,192 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2993 个
2025-07-28 15:27:55,193 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2993 个 (总计: 3135 个)
2025-07-28 15:27:55,193 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:27:55,194 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-28 15:27:55,194 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2993
2025-07-28 15:27:55,195 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15915374076 (赵婷婷)
2025-07-28 15:28:01,777 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15915374076
2025-07-28 15:28:01,777 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 15:28:01,777 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15915374076 执行添加朋友操作...
2025-07-28 15:28:01,778 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 15:28:01,778 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 15:28:01,779 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 15:28:01,779 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 15:28:01,784 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 15:28:01,785 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 15:28:01,786 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 15:28:01,786 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 15:28:01,787 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 15:28:01,788 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 15:28:01,788 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 15:28:01,794 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 15:28:01,803 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-28 15:28:01,804 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 15:28:01,805 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 15:28:01,806 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 15:28:01,808 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-28 15:28:01,809 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-28 15:28:02,314 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-28 15:28:02,315 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-28 15:28:02,393 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差36.50, 边缘比例0.0348
2025-07-28 15:28:02,403 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_152802.png
2025-07-28 15:28:02,404 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 15:28:02,406 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 15:28:02,416 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-28 15:28:02,417 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-28 15:28:02,417 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-28 15:28:02,422 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_152802.png
2025-07-28 15:28:02,425 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-28 15:28:02,430 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-28 15:28:02,435 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-28 15:28:02,436 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-28 15:28:02,437 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-28 15:28:02,437 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-28 15:28:02,438 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-28 15:28:02,441 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 15:28:02,449 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_152802.png
2025-07-28 15:28:02,452 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 15:28:02,454 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-28 15:28:02,457 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_152802.png
2025-07-28 15:28:02,504 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 15:28:02,505 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-28 15:28:02,506 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 15:28:02,506 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 15:28:02,807 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-28 15:28:03,581 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 15:28:03,582 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 15:28:03,584 - modules.wechat_auto_add_simple - INFO - ✅ 15915374076 添加朋友操作执行成功
2025-07-28 15:28:03,584 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 15:28:05,586 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-28 15:28:05,587 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-28 15:28:05,587 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-28 15:28:05,587 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 15:28:05,588 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 15:28:05,588 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 15:28:05,588 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 15:28:05,588 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 15:28:05,589 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-28 15:28:05,589 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 15915374076
2025-07-28 15:28:05,593 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-28 15:28:05,594 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:536 in _execute_auto_add_friend
2025-07-28 15:28:05,595 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:614 in _handle_friend_request_window
2025-07-28 15:28:05,597 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-28 15:28:05,598 - modules.friend_request_window - INFO -    📱 phone: '15915374076'
2025-07-28 15:28:05,601 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-28 15:28:05,602 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-28 15:28:06,180 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-28 15:28:06,181 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-28 15:28:06,181 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-28 15:28:06,182 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 15:28:06,183 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 15915374076
2025-07-28 15:28:06,184 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-28 15:28:06,184 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 15:28:06,185 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-28 15:28:06,186 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-28 15:28:06,186 - modules.friend_request_window - INFO -    📱 手机号码: 15915374076
2025-07-28 15:28:06,187 - modules.friend_request_window - INFO -    🆔 准考证: 015825120168
2025-07-28 15:28:06,187 - modules.friend_request_window - INFO -    👤 姓名: 赵婷婷
2025-07-28 15:28:06,187 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 15:28:06,188 - modules.friend_request_window - INFO -    📝 备注格式: '015825120168-赵婷婷-2025-07-28 15:28:06'
2025-07-28 15:28:06,188 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 15:28:06,188 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '015825120168-赵婷婷-2025-07-28 15:28:06'
2025-07-28 15:28:06,189 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 15:28:06,190 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 2165976, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-28 15:28:06,191 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 2165976)
2025-07-28 15:28:06,191 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-28 15:28:06,191 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-28 15:28:06,192 - modules.friend_request_window - INFO - 🔄 激活窗口: 2165976
2025-07-28 15:28:06,896 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-28 15:28:06,897 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-28 15:28:06,897 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-28 15:28:06,898 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:471 in _analyze_search_result
2025-07-28 15:28:06,898 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-28 15:28:06,898 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:536 in _execute_auto_add_friend
2025-07-28 15:28:06,898 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-28 15:28:06,899 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:614 in _handle_friend_request_window
2025-07-28 15:28:06,899 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-28 15:28:06,899 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1158 in execute_friend_request_flow
2025-07-28 15:28:06,899 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-28 15:28:06,900 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-28 15:28:06,900 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-28 15:28:06,900 - modules.friend_request_window - INFO -    📝 remark参数: '015825120168-赵婷婷-2025-07-28 15:28:06' (类型: <class 'str'>, 长度: 36)
2025-07-28 15:28:06,900 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-28 15:28:06,901 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '015825120168-赵婷婷-2025-07-28 15:28:06'
2025-07-28 15:28:06,901 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-28 15:28:06,902 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-28 15:28:06,902 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-28 15:28:06,902 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-28 15:28:06,903 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-28 15:28:06,903 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-28 15:28:06,903 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-28 15:28:07,814 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-28 15:28:13,059 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-28 15:28:13,060 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-28 15:28:13,060 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-28 15:28:13,060 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-28 15:28:13,062 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '关闭当前微信窗口 (坐标 700, 16)...' (前50字符)
2025-07-28 15:28:13,374 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 15:28:13,374 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 15:28:14,278 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 15:28:14,284 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 15:28:14,284 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-28 15:28:14,285 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-28 15:28:14,286 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-28 15:28:14,286 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-28 15:28:14,787 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-28 15:28:14,788 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-28 15:28:14,788 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-28 15:28:14,788 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-28 15:28:14,788 - modules.friend_request_window - INFO -    📝 内容: '015825120168-赵婷婷-2025-07-28 15:28:06'
2025-07-28 15:28:14,789 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-28 15:28:14,789 - modules.friend_request_window - INFO -    🔤 内容编码: b'015825120168-\xe8\xb5\xb5\xe5\xa9\xb7\xe5\xa9\xb7-2025-07-28 15:28:06'
2025-07-28 15:28:14,789 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-28 15:28:15,696 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-28 15:28:20,940 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-28 15:28:20,941 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-28 15:28:20,941 - modules.friend_request_window - INFO -    📝 原始文本: '015825120168-赵婷婷-2025-07-28 15:28:06'
2025-07-28 15:28:20,941 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-28 15:28:20,943 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '关闭当前微信窗口 (坐标 700, 16)...' (前50字符)
2025-07-28 15:28:21,255 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 15:28:21,256 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 15:28:22,159 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 15:28:22,168 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 15:28:22,168 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '015825120168-赵婷婷-2025-07-28 15:28:06'
2025-07-28 15:28:22,169 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-28 15:28:22,169 - modules.friend_request_window - INFO -    📝 已填写内容: '015825120168-赵婷婷-2025-07-28 15:28:06'
2025-07-28 15:28:22,169 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-28 15:28:22,670 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '015825120168-赵婷婷-2025-07-28 15:28:06'
2025-07-28 15:28:22,671 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-28 15:28:22,671 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-28 15:28:22,671 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-28 15:28:22,671 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-28 15:28:22,672 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-28 15:28:22,672 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-28 15:28:23,473 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-28 15:28:23,473 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-28 15:28:23,473 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-28 15:28:24,079 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:28:24,080 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-28 15:28:24,080 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-28 15:28:24,081 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-28 15:28:24,597 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:28:24,827 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:28:25,063 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:28:25,304 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:28:25,535 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:28:25,766 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:28:25,996 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:28:26,231 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:28:26,464 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:28:26,701 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:28:26,929 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:28:27,167 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:28:27,399 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:28:27,636 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:28:27,886 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:28:28,122 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:28:28,357 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:28:28,597 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:28:28,830 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:28:29,064 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 15:28:29,279 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-28 15:28:29,280 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-28 15:28:30,281 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 15:28:30,284 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-28 15:28:30,284 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-28 15:28:30,284 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-28 15:28:30,284 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-28 15:28:30,285 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 15:28:30,285 - modules.friend_request_window - INFO -    📝 备注信息: '015825120168-赵婷婷-2025-07-28 15:28:06'
2025-07-28 15:28:30,785 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-28 15:28:30,786 - modules.wechat_auto_add_simple - INFO - ✅ 15915374076 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-28 15:28:30,787 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 15915374076
2025-07-28 15:28:34,459 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2993
2025-07-28 15:28:34,459 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 17630534991 (李航)
2025-07-28 15:28:41,059 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 17630534991
2025-07-28 15:28:41,060 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 15:28:41,060 - modules.wechat_auto_add_simple - INFO - 👥 开始为 17630534991 执行添加朋友操作...
2025-07-28 15:28:41,060 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 15:28:41,061 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 15:28:41,062 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 15:28:41,062 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 15:28:41,067 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 15:28:41,068 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 15:28:41,069 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 15:28:41,070 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 15:28:41,070 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 15:28:41,070 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 15:28:41,072 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 15:28:41,073 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 15:28:41,079 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-28 15:28:41,083 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 15:28:41,085 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 15:28:41,087 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 15:28:41,090 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-28 15:28:41,097 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-28 15:28:41,599 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-28 15:28:41,601 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-28 15:28:41,664 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差38.13, 边缘比例0.0376
2025-07-28 15:28:41,672 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_152841.png
2025-07-28 15:28:41,675 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 15:28:41,679 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 15:28:41,680 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-28 15:28:41,682 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-28 15:28:41,682 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-28 15:28:41,687 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_152841.png
2025-07-28 15:28:41,689 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-28 15:28:41,690 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-28 15:28:41,693 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-28 15:28:41,696 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-28 15:28:41,697 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-28 15:28:41,698 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-28 15:28:41,699 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-28 15:28:41,701 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 15:28:41,709 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_152841.png
2025-07-28 15:28:41,711 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 15:28:41,713 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-28 15:28:41,718 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_152841.png
2025-07-28 15:28:41,743 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 15:28:41,745 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-28 15:28:41,748 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 15:28:41,748 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 15:28:42,051 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-28 15:28:42,828 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 15:28:42,835 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 15:28:42,839 - modules.wechat_auto_add_simple - INFO - ✅ 17630534991 添加朋友操作执行成功
2025-07-28 15:28:42,840 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 15:28:44,843 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-28 15:28:44,844 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-28 15:28:44,844 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-28 15:28:44,845 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 15:28:44,845 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 15:28:44,846 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 15:28:44,846 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 15:28:44,846 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 15:28:44,847 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-28 15:28:44,847 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 17630534991
2025-07-28 15:28:44,848 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-28 15:28:44,848 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:536 in _execute_auto_add_friend
2025-07-28 15:28:44,848 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:614 in _handle_friend_request_window
2025-07-28 15:28:44,849 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-28 15:28:44,850 - modules.friend_request_window - INFO -    📱 phone: '17630534991'
2025-07-28 15:28:44,851 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-28 15:28:44,852 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-28 15:28:45,463 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-28 15:28:45,464 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-28 15:28:45,465 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-28 15:28:45,465 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 15:28:45,466 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 17630534991
2025-07-28 15:28:45,467 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-28 15:28:45,468 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 15:28:45,468 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-28 15:28:45,468 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-28 15:28:45,469 - modules.friend_request_window - INFO -    📱 手机号码: 17630534991
2025-07-28 15:28:45,469 - modules.friend_request_window - INFO -    🆔 准考证: 015825120169
2025-07-28 15:28:45,470 - modules.friend_request_window - INFO -    👤 姓名: 李航
2025-07-28 15:28:45,470 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 15:28:45,471 - modules.friend_request_window - INFO -    📝 备注格式: '015825120169-李航-2025-07-28 15:28:45'
2025-07-28 15:28:45,471 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 15:28:45,472 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '015825120169-李航-2025-07-28 15:28:45'
2025-07-28 15:28:45,472 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 15:28:45,473 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 3868646, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-28 15:28:45,480 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 3868646)
2025-07-28 15:28:45,481 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-28 15:28:45,482 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-28 15:28:45,482 - modules.friend_request_window - INFO - 🔄 激活窗口: 3868646
2025-07-28 15:28:46,186 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-28 15:28:46,187 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-28 15:28:46,187 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-28 15:28:46,188 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:471 in _analyze_search_result
2025-07-28 15:28:46,188 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-28 15:28:46,188 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:536 in _execute_auto_add_friend
2025-07-28 15:28:46,188 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-28 15:28:46,189 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:614 in _handle_friend_request_window
2025-07-28 15:28:46,189 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-28 15:28:46,189 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1158 in execute_friend_request_flow
2025-07-28 15:28:46,189 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-28 15:28:46,190 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-28 15:28:46,190 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-28 15:28:46,192 - modules.friend_request_window - INFO -    📝 remark参数: '015825120169-李航-2025-07-28 15:28:45' (类型: <class 'str'>, 长度: 35)
2025-07-28 15:28:46,193 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-28 15:28:46,193 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '015825120169-李航-2025-07-28 15:28:45'
2025-07-28 15:28:46,193 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-28 15:28:46,194 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-28 15:28:46,194 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-28 15:28:46,194 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-28 15:28:46,195 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-28 15:28:46,195 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-28 15:28:46,195 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-28 15:28:47,112 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-28 15:28:52,360 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-28 15:28:52,360 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-28 15:28:52,360 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-28 15:28:52,361 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-28 15:28:52,361 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '关闭当前微信窗口 (坐标 700, 16)...' (前50字符)
2025-07-28 15:28:52,669 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 15:28:52,670 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 15:28:53,572 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 15:28:53,581 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 15:28:53,583 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-28 15:28:53,583 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-28 15:28:53,583 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-28 15:28:53,584 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-28 15:28:54,085 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-28 15:28:54,085 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-28 15:28:54,086 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-28 15:28:54,086 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-28 15:28:54,086 - modules.friend_request_window - INFO -    📝 内容: '015825120169-李航-2025-07-28 15:28:45'
2025-07-28 15:28:54,087 - modules.friend_request_window - INFO -    📏 内容长度: 35 字符
2025-07-28 15:28:54,087 - modules.friend_request_window - INFO -    🔤 内容编码: b'015825120169-\xe6\x9d\x8e\xe8\x88\xaa-2025-07-28 15:28:45'
2025-07-28 15:28:54,087 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-28 15:28:54,993 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-28 15:29:00,283 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-28 15:29:00,284 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-28 15:29:00,284 - modules.friend_request_window - INFO -    📝 原始文本: '015825120169-李航-2025-07-28 15:28:45'
2025-07-28 15:29:00,285 - modules.friend_request_window - INFO -    📏 文本长度: 35 字符
2025-07-28 15:29:00,285 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '关闭当前微信窗口 (坐标 700, 16)...' (前50字符)
2025-07-28 15:29:00,593 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 15:29:00,594 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 15:29:01,496 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 15:29:01,508 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 15:29:01,511 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '015825120169-李航-2025-07-28 15:28:45'
2025-07-28 15:29:01,512 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-28 15:29:01,515 - modules.friend_request_window - INFO -    📝 已填写内容: '015825120169-李航-2025-07-28 15:28:45'
2025-07-28 15:29:01,516 - modules.friend_request_window - INFO -    � 内容长度: 35 字符
2025-07-28 15:29:02,016 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '015825120169-李航-2025-07-28 15:28:45'
2025-07-28 15:29:02,017 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-28 15:29:02,017 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-28 15:29:02,017 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-28 15:29:02,018 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-28 15:29:02,018 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-28 15:29:02,018 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-28 15:29:02,819 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-28 15:29:02,819 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-28 15:29:02,820 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-28 15:29:03,450 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 15:29:03,451 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-28 15:29:03,451 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-28 15:29:03,452 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-28 15:29:03,953 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-28 15:29:03,956 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-28 15:29:03,956 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-28 15:29:03,957 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-28 15:29:03,957 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-28 15:29:03,957 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-28 15:29:03,958 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-28 15:29:03,958 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-28 15:29:03,958 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-28 15:29:03,961 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-28 15:29:03,968 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-28 15:29:03,969 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-28 15:29:03,969 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-28 15:29:03,970 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-28 15:29:03,970 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-28 15:29:03,971 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-28 15:29:03,971 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-28 15:29:03,971 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-28 15:29:03,972 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-28 15:29:04,474 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-28 15:29:04,476 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-28 15:29:04,476 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-28 15:29:04,477 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-28 15:29:04,478 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-28 15:29:04,478 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-28 15:29:04,479 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-28 15:29:04,479 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-28 15:29:05,413 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-28 15:29:05,414 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-28 15:29:05,414 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-28 15:29:05,415 - modules.friend_request_window - INFO - 🔄 步骤2: 关闭添加朋友窗口...
2025-07-28 15:29:05,415 - modules.friend_request_window - INFO - 🎯 使用指定坐标 (1504, 13) 关闭添加朋友窗口...
2025-07-28 15:29:05,916 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui点击坐标 (1504, 13)...
2025-07-28 15:29:07,029 - modules.friend_request_window - INFO - ✅ pyautogui点击添加朋友窗口关闭按钮成功
2025-07-28 15:29:07,030 - modules.friend_request_window - INFO - 🔄 步骤3: 关闭当前微信窗口...
2025-07-28 15:29:07,030 - modules.friend_request_window - INFO - 🎯 使用指定坐标 (700, 16) 关闭当前微信窗口...
2025-07-28 15:29:07,531 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui点击坐标 (700, 16)...
2025-07-28 15:29:08,642 - modules.friend_request_window - INFO - ✅ pyautogui点击当前微信窗口关闭按钮成功
2025-07-28 15:29:08,643 - modules.friend_request_window - INFO - 🔄 关闭错误相关窗口...
2025-07-28 15:29:08,660 - modules.friend_request_window - INFO - ✅ 频率错误处理完成
2025-07-28 15:29:08,661 - modules.friend_request_window - INFO - 🔄 频率错误处理完成，需要重新开始流程
2025-07-28 15:29:08,661 - modules.friend_request_window - INFO - 🔄 已设置重新开始标志
2025-07-28 15:29:08,662 - modules.friend_request_window - INFO - ✅ 原有频率错误处理成功，已切换到微信2
2025-07-28 15:29:08,662 - modules.friend_request_window - WARNING - ⚠️ 检测到并处理了频率错误，已切换到微信2
2025-07-28 15:29:08,665 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-28 15:29:08,665 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-28 15:29:08,666 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 15:29:08,667 - modules.friend_request_window - INFO -    📝 备注信息: '015825120169-李航-2025-07-28 15:28:45'
2025-07-28 15:29:09,168 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-28 15:29:09,168 - modules.wechat_auto_add_simple - INFO - ✅ 17630534991 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-28 15:29:09,169 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 17630534991
2025-07-28 15:29:12,911 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 3/2993
2025-07-28 15:29:12,912 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15210772820 (王柳杨)
2025-07-28 15:29:19,575 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15210772820
2025-07-28 15:29:19,576 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 15:29:19,576 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15210772820 执行添加朋友操作...
2025-07-28 15:29:19,576 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 15:29:19,577 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 15:29:19,578 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 15:29:19,580 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 15:29:19,584 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 15:29:19,585 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 15:29:19,585 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 15:29:19,586 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 15:29:19,586 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 15:29:19,586 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 15:29:19,586 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 15:29:19,586 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 15:29:19,590 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 15:29:19,594 - WeChatAutoAdd - DEBUG - 找到微信窗口: ● main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 15:29:19,596 - WeChatAutoAdd - INFO - 共找到 2 个微信窗口
2025-07-28 15:29:19,597 - WeChatAutoAdd - INFO - 找到微信主窗口: 微信
2025-07-28 15:29:20,100 - WeChatAutoAdd - INFO - 目标窗口: 微信
2025-07-28 15:29:20,102 - WeChatAutoAdd - INFO - 窗口位置: (0, 0), 尺寸: 726x650
2025-07-28 15:29:20,186 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差37.27, 边缘比例0.0602
2025-07-28 15:29:20,205 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_152920.png
2025-07-28 15:29:20,210 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 15:29:20,212 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 15:29:20,216 - WeChatAutoAdd - INFO - 截图尺寸: 726x650
2025-07-28 15:29:20,217 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=405-650 (高度:245)
2025-07-28 15:29:20,218 - WeChatAutoAdd - INFO - 特别关注区域: Y=466-526 (距底部154像素)
2025-07-28 15:29:20,228 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_152920.png
2025-07-28 15:29:20,233 - WeChatAutoAdd - INFO - 底部区域原始检测到 92 个轮廓
2025-07-28 15:29:20,235 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,649), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:29:20,249 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,612), 尺寸17x1, 长宽比17.00, 面积17
2025-07-28 15:29:20,250 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,610), 尺寸18x1, 长宽比18.00, 面积18
2025-07-28 15:29:20,252 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,604), 尺寸18x3, 长宽比6.00, 面积54
2025-07-28 15:29:20,254 - WeChatAutoAdd - INFO - 重要轮廓: 位置(614,602), 尺寸96x32, 长宽比3.00, 已知特征:False
2025-07-28 15:29:20,262 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度96 (需要70-95)
2025-07-28 15:29:20,263 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,601), 尺寸18x1, 长宽比18.00, 面积18
2025-07-28 15:29:20,264 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,599), 尺寸17x1, 长宽比17.00, 面积17
2025-07-28 15:29:20,265 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,547), 尺寸16x22, 长宽比0.73, 面积352
2025-07-28 15:29:20,274 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(161,526), 尺寸4x1, 长宽比4.00, 面积4
2025-07-28 15:29:20,277 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,524), 尺寸4x2, 长宽比2.00, 面积8
2025-07-28 15:29:20,279 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,523), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:29:20,280 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,522), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 15:29:20,282 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(161,522), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 15:29:20,284 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,521), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 15:29:20,285 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(155,521), 尺寸5x7, 长宽比0.71, 面积35
2025-07-28 15:29:20,287 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(143,521), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:29:20,290 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(146,520), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:29:20,293 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(179,519), 尺寸10x7, 长宽比1.43, 面积70
2025-07-28 15:29:20,294 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(162,519), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 15:29:20,295 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(196,518), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:29:20,297 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,518), 尺寸1x5, 长宽比0.20, 面积5
2025-07-28 15:29:20,299 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,518), 尺寸4x2, 长宽比2.00, 面积8
2025-07-28 15:29:20,300 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(150,518), 尺寸1x9, 长宽比0.11, 面积9
2025-07-28 15:29:20,301 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(180,516), 尺寸10x2, 长宽比5.00, 面积20
2025-07-28 15:29:20,303 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(155,516), 尺寸29x12, 长宽比2.42, 面积348
2025-07-28 15:29:20,304 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=516 (距底部154像素区域)
2025-07-28 15:29:20,309 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 15:29:20,311 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,516), 尺寸4x13, 长宽比0.31, 面积52
2025-07-28 15:29:20,312 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(185,515), 尺寸42x13, 长宽比3.23, 面积546
2025-07-28 15:29:20,314 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=515 (距底部154像素区域)
2025-07-28 15:29:20,316 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 15:29:20,317 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(123,515), 尺寸26x14, 长宽比1.86, 面积364
2025-07-28 15:29:20,319 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=515 (距底部154像素区域)
2025-07-28 15:29:20,320 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 15:29:20,321 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,505), 尺寸8x1, 长宽比8.00, 面积8
2025-07-28 15:29:20,326 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,503), 尺寸9x1, 长宽比9.00, 面积9
2025-07-28 15:29:20,329 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,502), 尺寸8x4, 长宽比2.00, 面积32
2025-07-28 15:29:20,332 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(139,502), 尺寸6x2, 长宽比3.00, 面积12
2025-07-28 15:29:20,333 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,499), 尺寸8x3, 长宽比2.67, 面积24
2025-07-28 15:29:20,335 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(163,498), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:29:20,336 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(154,498), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:29:20,337 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,497), 尺寸1x4, 长宽比0.25, 面积4
2025-07-28 15:29:20,341 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(229,495), 尺寸29x11, 长宽比2.64, 面积319
2025-07-28 15:29:20,344 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=495 (距底部154像素区域)
2025-07-28 15:29:20,346 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 15:29:20,348 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(163,495), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 15:29:20,349 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,495), 尺寸10x10, 长宽比1.00, 面积100
2025-07-28 15:29:20,350 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=495 (距底部154像素区域)
2025-07-28 15:29:20,352 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 15:29:20,353 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(170,494), 尺寸5x11, 长宽比0.45, 面积55
2025-07-28 15:29:20,354 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(168,494), 尺寸1x7, 长宽比0.14, 面积7
2025-07-28 15:29:20,362 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,493), 尺寸7x13, 长宽比0.54, 面积91
2025-07-28 15:29:20,364 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,493), 尺寸10x1, 长宽比10.00, 面积10
2025-07-28 15:29:20,366 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,493), 尺寸41x14, 长宽比2.93, 面积574
2025-07-28 15:29:20,368 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=493 (距底部154像素区域)
2025-07-28 15:29:20,369 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 15:29:20,370 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,491), 尺寸37x37, 长宽比1.00, 面积1369
2025-07-28 15:29:20,374 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=491 (距底部154像素区域)
2025-07-28 15:29:20,378 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 15:29:20,379 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,456), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:29:20,380 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(247,456), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:29:20,381 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(244,456), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 15:29:20,383 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(203,456), 尺寸9x3, 长宽比3.00, 面积27
2025-07-28 15:29:20,384 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(208,454), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 15:29:20,385 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(203,454), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:29:20,386 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(175,454), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:29:20,388 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,454), 尺寸10x8, 长宽比1.25, 面积80
2025-07-28 15:29:20,393 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(201,453), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:29:20,394 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(158,453), 尺寸4x5, 长宽比0.80, 面积20
2025-07-28 15:29:20,395 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,452), 尺寸4x4, 长宽比1.00, 面积16
2025-07-28 15:29:20,396 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,452), 尺寸7x4, 长宽比1.75, 面积28
2025-07-28 15:29:20,398 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,452), 尺寸8x7, 长宽比1.14, 面积56
2025-07-28 15:29:20,400 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,452), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:29:20,401 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,452), 尺寸1x3, 长宽比0.33, 面积3
2025-07-28 15:29:20,402 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,452), 尺寸6x10, 长宽比0.60, 面积60
2025-07-28 15:29:20,403 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,451), 尺寸5x4, 长宽比1.25, 面积20
2025-07-28 15:29:20,405 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(159,451), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:29:20,412 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,451), 尺寸5x4, 长宽比1.25, 面积20
2025-07-28 15:29:20,416 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(132,451), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:29:20,417 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(123,451), 尺寸8x7, 长宽比1.14, 面积56
2025-07-28 15:29:20,418 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(171,450), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:29:20,419 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(230,449), 尺寸13x11, 长宽比1.18, 面积143
2025-07-28 15:29:20,421 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,449), 尺寸5x1, 长宽比5.00, 面积5
2025-07-28 15:29:20,444 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,449), 尺寸23x11, 长宽比2.09, 面积253
2025-07-28 15:29:20,448 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,448), 尺寸17x12, 长宽比1.42, 面积204
2025-07-28 15:29:20,454 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(173,448), 尺寸10x8, 长宽比1.25, 面积80
2025-07-28 15:29:20,461 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,447), 尺寸19x13, 长宽比1.46, 面积247
2025-07-28 15:29:20,463 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,447), 尺寸11x13, 长宽比0.85, 面积143
2025-07-28 15:29:20,465 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(159,447), 尺寸13x3, 长宽比4.33, 面积39
2025-07-28 15:29:20,469 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(218,435), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:29:20,470 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,435), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 15:29:20,477 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(152,435), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 15:29:20,478 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(253,431), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 15:29:20,480 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(186,429), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:29:20,482 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(229,427), 尺寸30x11, 长宽比2.73, 面积330
2025-07-28 15:29:20,484 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(186,427), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:29:20,485 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(188,426), 尺寸3x11, 长宽比0.27, 面积33
2025-07-28 15:29:20,486 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,426), 尺寸6x11, 长宽比0.55, 面积66
2025-07-28 15:29:20,490 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,426), 尺寸5x11, 长宽比0.45, 面积55
2025-07-28 15:29:20,493 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,426), 尺寸17x11, 长宽比1.55, 面积187
2025-07-28 15:29:20,494 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=87.1 (阈值:60)
2025-07-28 15:29:20,496 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(202,425), 尺寸9x13, 长宽比0.69, 面积117
2025-07-28 15:29:20,497 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,425), 尺寸7x13, 长宽比0.54, 面积91
2025-07-28 15:29:20,499 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(153,425), 尺寸15x13, 长宽比1.15, 面积195
2025-07-28 15:29:20,501 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=88.5 (阈值:60)
2025-07-28 15:29:20,502 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,425), 尺寸14x13, 长宽比1.08, 面积182
2025-07-28 15:29:20,509 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=78.1 (阈值:60)
2025-07-28 15:29:20,512 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,423), 尺寸38x38, 长宽比1.00, 面积1444
2025-07-28 15:29:20,514 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=71.2 (阈值:60)
2025-07-28 15:29:20,517 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,405), 尺寸725x245, 长宽比2.96, 面积177625
2025-07-28 15:29:20,518 - WeChatAutoAdd - INFO - 底部区域找到 15 个按钮候选
2025-07-28 15:29:20,520 - WeChatAutoAdd - INFO - 选择特殊位置按钮: Y=491 (距底部154像素)
2025-07-28 15:29:20,525 - WeChatAutoAdd - INFO - 在底部找到按钮: (90, 509), 尺寸: 37x37, 位置得分: 3.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-07-28 15:29:20,527 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 15:29:20,547 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_152920.png
2025-07-28 15:29:20,549 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 15:29:20,550 - WeChatAutoAdd - INFO - 开始验证按钮区域(90, 509)是否包含'添加到通讯录'文字
2025-07-28 15:29:20,553 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_152920.png
2025-07-28 15:29:20,575 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 15:29:20,577 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0509, 平均亮度=215.2, 亮度标准差=45.9
2025-07-28 15:29:20,578 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 15:29:20,579 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 15:29:20,881 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(90, 509) -> 屏幕坐标(90, 509)
2025-07-28 15:29:21,658 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 15:29:21,660 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 15:29:21,662 - modules.wechat_auto_add_simple - INFO - ✅ 15210772820 添加朋友操作执行成功
2025-07-28 15:29:21,662 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 15:29:23,664 - modules.wechat_auto_add_simple - INFO - ℹ️ 15210772820 未发现申请添加朋友窗口，可能直接添加成功
2025-07-28 15:29:23,665 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 15210772820
2025-07-28 15:29:27,300 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 4/2993
2025-07-28 15:29:27,301 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15903614248 (余长江)
2025-07-28 15:29:33,993 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15903614248
2025-07-28 15:29:33,993 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 15:29:33,994 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15903614248 执行添加朋友操作...
2025-07-28 15:29:33,994 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 15:29:33,994 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 15:29:33,995 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 15:29:33,996 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 15:29:34,001 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 15:29:34,002 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 15:29:34,002 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 15:29:34,003 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 15:29:34,003 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 15:29:34,003 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 15:29:34,003 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 15:29:34,003 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 15:29:34,008 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 15:29:34,010 - WeChatAutoAdd - DEBUG - 找到微信窗口: ● main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 15:29:34,017 - WeChatAutoAdd - INFO - 共找到 2 个微信窗口
2025-07-28 15:29:34,024 - WeChatAutoAdd - INFO - 找到微信主窗口: 微信
2025-07-28 15:29:34,527 - WeChatAutoAdd - INFO - 目标窗口: 微信
2025-07-28 15:29:34,530 - WeChatAutoAdd - INFO - 窗口位置: (0, 0), 尺寸: 726x650
2025-07-28 15:29:34,608 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差30.62, 边缘比例0.0392
2025-07-28 15:29:34,625 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_152934.png
2025-07-28 15:29:34,627 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 15:29:34,629 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 15:29:34,631 - WeChatAutoAdd - INFO - 截图尺寸: 726x650
2025-07-28 15:29:34,634 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=405-650 (高度:245)
2025-07-28 15:29:34,636 - WeChatAutoAdd - INFO - 特别关注区域: Y=466-526 (距底部154像素)
2025-07-28 15:29:34,646 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_152934.png
2025-07-28 15:29:34,649 - WeChatAutoAdd - INFO - 底部区域原始检测到 161 个轮廓
2025-07-28 15:29:34,650 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,649), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:29:34,651 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(75,642), 尺寸3x5, 长宽比0.60, 面积15
2025-07-28 15:29:34,653 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(83,641), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:29:34,657 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(78,640), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:29:34,660 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(218,639), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:29:34,661 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,639), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 15:29:34,662 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(210,639), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:29:34,664 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(106,638), 尺寸4x3, 长宽比1.33, 面积12
2025-07-28 15:29:34,666 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(97,638), 尺寸8x3, 长宽比2.67, 面积24
2025-07-28 15:29:34,667 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,638), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:29:34,669 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(91,637), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:29:34,670 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(83,636), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:29:34,675 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(247,635), 尺寸4x6, 长宽比0.67, 面积24
2025-07-28 15:29:34,680 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(242,631), 尺寸17x11, 长宽比1.55, 面积187
2025-07-28 15:29:34,683 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(233,631), 尺寸10x9, 长宽比1.11, 面积90
2025-07-28 15:29:34,684 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(231,631), 尺寸6x10, 长宽比0.60, 面积60
2025-07-28 15:29:34,685 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(80,631), 尺寸4x16, 长宽比0.25, 面积64
2025-07-28 15:29:34,687 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,630), 尺寸6x11, 长宽比0.55, 面积66
2025-07-28 15:29:34,694 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,630), 尺寸5x11, 长宽比0.45, 面积55
2025-07-28 15:29:34,696 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,630), 尺寸8x11, 长宽比0.73, 面积88
2025-07-28 15:29:34,699 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,630), 尺寸9x11, 长宽比0.82, 面积99
2025-07-28 15:29:34,701 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(185,629), 尺寸24x13, 长宽比1.85, 面积312
2025-07-28 15:29:34,704 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=84.3 (阈值:60)
2025-07-28 15:29:34,709 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,629), 尺寸15x13, 长宽比1.15, 面积195
2025-07-28 15:29:34,711 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=83.6 (阈值:60)
2025-07-28 15:29:34,713 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,629), 尺寸14x13, 长宽比1.08, 面积182
2025-07-28 15:29:34,714 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=78.1 (阈值:60)
2025-07-28 15:29:34,716 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,612), 尺寸17x1, 长宽比17.00, 面积17
2025-07-28 15:29:34,718 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,610), 尺寸18x1, 长宽比18.00, 面积18
2025-07-28 15:29:34,719 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,604), 尺寸18x3, 长宽比6.00, 面积54
2025-07-28 15:29:34,720 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,601), 尺寸18x1, 长宽比18.00, 面积18
2025-07-28 15:29:34,726 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,599), 尺寸17x1, 长宽比17.00, 面积17
2025-07-28 15:29:34,728 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(200,594), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:29:34,730 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(188,593), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:29:34,731 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,592), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:29:34,733 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(245,592), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 15:29:34,734 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(243,592), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:29:34,735 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(241,592), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:29:34,737 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(124,592), 尺寸4x4, 长宽比1.00, 面积16
2025-07-28 15:29:34,742 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,591), 尺寸4x3, 长宽比1.33, 面积12
2025-07-28 15:29:34,745 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,590), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 15:29:34,747 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(181,589), 尺寸1x5, 长宽比0.20, 面积5
2025-07-28 15:29:34,748 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(127,589), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:29:34,751 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(226,588), 尺寸4x6, 长宽比0.67, 面积24
2025-07-28 15:29:34,752 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(224,588), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 15:29:34,753 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,588), 尺寸3x1, 长宽比3.00, 面积3
2025-07-28 15:29:34,761 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(146,588), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 15:29:34,763 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(143,588), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:29:34,765 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(191,587), 尺寸6x8, 长宽比0.75, 面积48
2025-07-28 15:29:34,767 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(179,585), 尺寸11x9, 长宽比1.22, 面积99
2025-07-28 15:29:34,769 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(147,585), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 15:29:34,775 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(198,584), 尺寸27x11, 长宽比2.45, 面积297
2025-07-28 15:29:34,779 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(122,584), 尺寸45x12, 长宽比3.75, 面积540
2025-07-28 15:29:34,784 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,584), 尺寸4x11, 长宽比0.36, 面积44
2025-07-28 15:29:34,786 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,583), 尺寸11x13, 长宽比0.85, 面积143
2025-07-28 15:29:34,794 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(153,583), 尺寸8x11, 长宽比0.73, 面积88
2025-07-28 15:29:34,801 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(147,583), 尺寸7x3, 长宽比2.33, 面积21
2025-07-28 15:29:34,803 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(143,583), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 15:29:34,812 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,583), 尺寸13x11, 长宽比1.18, 面积143
2025-07-28 15:29:34,815 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(221,571), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:29:34,819 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(216,571), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 15:29:34,820 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,570), 尺寸7x4, 长宽比1.75, 面积28
2025-07-28 15:29:34,828 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(126,570), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 15:29:34,830 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(205,568), 尺寸2x5, 长宽比0.40, 面积10
2025-07-28 15:29:34,832 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(133,568), 尺寸2x5, 长宽比0.40, 面积10
2025-07-28 15:29:34,834 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(171,565), 尺寸4x4, 长宽比1.00, 面积16
2025-07-28 15:29:34,836 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(254,564), 尺寸4x8, 长宽比0.50, 面积32
2025-07-28 15:29:34,840 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(206,564), 尺寸8x10, 长宽比0.80, 面积80
2025-07-28 15:29:34,845 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,564), 尺寸6x10, 长宽比0.60, 面积60
2025-07-28 15:29:34,847 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(242,563), 尺寸11x11, 长宽比1.00, 面积121
2025-07-28 15:29:34,849 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(233,563), 尺寸10x9, 长宽比1.11, 面积90
2025-07-28 15:29:34,852 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(231,563), 尺寸6x10, 长宽比0.60, 面积60
2025-07-28 15:29:34,853 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(141,563), 尺寸10x11, 长宽比0.91, 面积110
2025-07-28 15:29:34,859 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(199,562), 尺寸6x11, 长宽比0.55, 面积66
2025-07-28 15:29:34,862 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(196,562), 尺寸5x6, 长宽比0.83, 面积30
2025-07-28 15:29:34,865 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(191,562), 尺寸7x11, 长宽比0.64, 面积77
2025-07-28 15:29:34,867 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(151,562), 尺寸28x11, 长宽比2.55, 面积308
2025-07-28 15:29:34,869 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=78.7 (阈值:60)
2025-07-28 15:29:34,870 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(120,561), 尺寸12x12, 长宽比1.00, 面积144
2025-07-28 15:29:34,875 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=78.6 (阈值:60)
2025-07-28 15:29:34,877 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,559), 尺寸38x38, 长宽比1.00, 面积1444
2025-07-28 15:29:34,879 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=71.2 (阈值:60)
2025-07-28 15:29:34,881 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,547), 尺寸16x22, 长宽比0.73, 面积352
2025-07-28 15:29:34,884 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(161,526), 尺寸4x1, 长宽比4.00, 面积4
2025-07-28 15:29:34,885 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,524), 尺寸4x2, 长宽比2.00, 面积8
2025-07-28 15:29:34,888 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,523), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:29:34,892 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,522), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 15:29:34,894 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(161,522), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 15:29:34,896 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,521), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 15:29:34,897 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(155,521), 尺寸5x7, 长宽比0.71, 面积35
2025-07-28 15:29:34,899 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(143,521), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:29:34,901 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(146,520), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:29:34,902 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(179,519), 尺寸10x7, 长宽比1.43, 面积70
2025-07-28 15:29:34,905 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(162,519), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 15:29:34,910 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(196,518), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:29:34,911 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,518), 尺寸1x5, 长宽比0.20, 面积5
2025-07-28 15:29:34,912 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,518), 尺寸4x2, 长宽比2.00, 面积8
2025-07-28 15:29:34,914 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(150,518), 尺寸1x9, 长宽比0.11, 面积9
2025-07-28 15:29:34,915 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(180,516), 尺寸10x2, 长宽比5.00, 面积20
2025-07-28 15:29:34,916 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(155,516), 尺寸29x12, 长宽比2.42, 面积348
2025-07-28 15:29:34,919 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=516 (距底部154像素区域)
2025-07-28 15:29:34,925 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 15:29:34,927 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,516), 尺寸4x13, 长宽比0.31, 面积52
2025-07-28 15:29:34,930 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(185,515), 尺寸42x13, 长宽比3.23, 面积546
2025-07-28 15:29:34,933 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=515 (距底部154像素区域)
2025-07-28 15:29:34,934 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 15:29:34,936 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(123,515), 尺寸26x14, 长宽比1.86, 面积364
2025-07-28 15:29:34,943 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=515 (距底部154像素区域)
2025-07-28 15:29:34,949 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 15:29:34,951 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,505), 尺寸8x1, 长宽比8.00, 面积8
2025-07-28 15:29:34,952 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,503), 尺寸9x1, 长宽比9.00, 面积9
2025-07-28 15:29:34,956 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,502), 尺寸8x4, 长宽比2.00, 面积32
2025-07-28 15:29:34,967 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(139,502), 尺寸6x2, 长宽比3.00, 面积12
2025-07-28 15:29:34,969 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,499), 尺寸8x3, 长宽比2.67, 面积24
2025-07-28 15:29:34,976 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(163,498), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:29:34,979 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(154,498), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:29:34,983 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,497), 尺寸1x4, 长宽比0.25, 面积4
2025-07-28 15:29:34,985 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(229,495), 尺寸29x11, 长宽比2.64, 面积319
2025-07-28 15:29:34,994 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=495 (距底部154像素区域)
2025-07-28 15:29:34,996 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 15:29:35,002 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(163,495), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 15:29:35,004 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,495), 尺寸10x10, 长宽比1.00, 面积100
2025-07-28 15:29:35,012 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=495 (距底部154像素区域)
2025-07-28 15:29:35,015 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 15:29:35,017 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(170,494), 尺寸5x11, 长宽比0.45, 面积55
2025-07-28 15:29:35,020 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(168,494), 尺寸1x7, 长宽比0.14, 面积7
2025-07-28 15:29:35,029 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(161,493), 尺寸6x13, 长宽比0.46, 面积78
2025-07-28 15:29:35,031 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,493), 尺寸10x1, 长宽比10.00, 面积10
2025-07-28 15:29:35,032 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,493), 尺寸41x14, 长宽比2.93, 面积574
2025-07-28 15:29:35,036 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=493 (距底部154像素区域)
2025-07-28 15:29:35,043 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 15:29:35,050 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,491), 尺寸37x37, 长宽比1.00, 面积1369
2025-07-28 15:29:35,053 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=491 (距底部154像素区域)
2025-07-28 15:29:35,059 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 15:29:35,061 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,456), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:29:35,064 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(247,456), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:29:35,067 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(244,456), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 15:29:35,069 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(203,456), 尺寸9x3, 长宽比3.00, 面积27
2025-07-28 15:29:35,075 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(208,454), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 15:29:35,078 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(203,454), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:29:35,079 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(175,454), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:29:35,081 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,454), 尺寸10x8, 长宽比1.25, 面积80
2025-07-28 15:29:35,084 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(201,453), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:29:35,086 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(158,453), 尺寸4x5, 长宽比0.80, 面积20
2025-07-28 15:29:35,093 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,452), 尺寸4x4, 长宽比1.00, 面积16
2025-07-28 15:29:35,094 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,452), 尺寸7x4, 长宽比1.75, 面积28
2025-07-28 15:29:35,096 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,452), 尺寸8x7, 长宽比1.14, 面积56
2025-07-28 15:29:35,099 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,452), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:29:35,100 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,452), 尺寸1x3, 长宽比0.33, 面积3
2025-07-28 15:29:35,101 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,452), 尺寸6x10, 长宽比0.60, 面积60
2025-07-28 15:29:35,106 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,451), 尺寸5x4, 长宽比1.25, 面积20
2025-07-28 15:29:35,110 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(159,451), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 15:29:35,111 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,451), 尺寸5x4, 长宽比1.25, 面积20
2025-07-28 15:29:35,115 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(132,451), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:29:35,116 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(123,451), 尺寸8x7, 长宽比1.14, 面积56
2025-07-28 15:29:35,118 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(171,450), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:29:35,119 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(230,449), 尺寸13x11, 长宽比1.18, 面积143
2025-07-28 15:29:35,127 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,449), 尺寸5x1, 长宽比5.00, 面积5
2025-07-28 15:29:35,129 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,449), 尺寸23x11, 长宽比2.09, 面积253
2025-07-28 15:29:35,132 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,448), 尺寸17x12, 长宽比1.42, 面积204
2025-07-28 15:29:35,134 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(173,448), 尺寸10x8, 长宽比1.25, 面积80
2025-07-28 15:29:35,136 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,447), 尺寸19x13, 长宽比1.46, 面积247
2025-07-28 15:29:35,144 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,447), 尺寸11x13, 长宽比0.85, 面积143
2025-07-28 15:29:35,146 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(159,447), 尺寸13x3, 长宽比4.33, 面积39
2025-07-28 15:29:35,150 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(218,435), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 15:29:35,152 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,435), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 15:29:35,154 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(152,435), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 15:29:35,160 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(253,431), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 15:29:35,163 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(186,429), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:29:35,166 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(229,427), 尺寸30x11, 长宽比2.73, 面积330
2025-07-28 15:29:35,169 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(186,427), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 15:29:35,174 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(188,426), 尺寸3x11, 长宽比0.27, 面积33
2025-07-28 15:29:35,177 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,426), 尺寸6x11, 长宽比0.55, 面积66
2025-07-28 15:29:35,179 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,426), 尺寸5x11, 长宽比0.45, 面积55
2025-07-28 15:29:35,182 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,426), 尺寸17x11, 长宽比1.55, 面积187
2025-07-28 15:29:35,183 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=87.1 (阈值:60)
2025-07-28 15:29:35,185 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(202,425), 尺寸9x13, 长宽比0.69, 面积117
2025-07-28 15:29:35,187 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,425), 尺寸7x13, 长宽比0.54, 面积91
2025-07-28 15:29:35,195 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(153,425), 尺寸15x13, 长宽比1.15, 面积195
2025-07-28 15:29:35,199 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=88.5 (阈值:60)
2025-07-28 15:29:35,201 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,425), 尺寸14x13, 长宽比1.08, 面积182
2025-07-28 15:29:35,206 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=78.1 (阈值:60)
2025-07-28 15:29:35,213 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,423), 尺寸38x38, 长宽比1.00, 面积1444
2025-07-28 15:29:35,215 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=71.2 (阈值:60)
2025-07-28 15:29:35,218 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,405), 尺寸725x245, 长宽比2.96, 面积177625
2025-07-28 15:29:35,220 - WeChatAutoAdd - INFO - 底部区域找到 22 个按钮候选
2025-07-28 15:29:35,224 - WeChatAutoAdd - INFO - 选择特殊位置按钮: Y=491 (距底部154像素)
2025-07-28 15:29:35,226 - WeChatAutoAdd - INFO - 在底部找到按钮: (90, 509), 尺寸: 37x37, 位置得分: 3.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-07-28 15:29:35,229 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 15:29:35,250 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_152935.png
2025-07-28 15:29:35,252 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 15:29:35,253 - WeChatAutoAdd - INFO - 开始验证按钮区域(90, 509)是否包含'添加到通讯录'文字
2025-07-28 15:29:35,262 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_152935.png
2025-07-28 15:29:35,283 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 15:29:35,285 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0508, 平均亮度=227.0, 亮度标准差=45.1
2025-07-28 15:29:35,287 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 15:29:35,293 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 15:29:35,595 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(90, 509) -> 屏幕坐标(90, 509)
2025-07-28 15:29:36,396 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 15:29:36,398 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 15:29:36,399 - modules.wechat_auto_add_simple - INFO - ✅ 15903614248 添加朋友操作执行成功
2025-07-28 15:29:36,400 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 15:29:38,401 - modules.wechat_auto_add_simple - INFO - ℹ️ 15903614248 未发现申请添加朋友窗口，可能直接添加成功
2025-07-28 15:29:38,402 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 15903614248
2025-07-28 15:29:42,064 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 5/2993
2025-07-28 15:29:42,064 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13659817026 (龚翩翩)
2025-07-28 15:29:48,638 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13659817026
2025-07-28 15:29:48,639 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 15:29:48,639 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13659817026 执行添加朋友操作...
2025-07-28 15:29:48,639 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 15:29:48,640 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 15:29:48,641 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 15:29:48,642 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 15:29:48,648 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 15:29:48,651 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 15:29:48,651 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 15:29:48,652 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 15:29:48,652 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 15:29:48,652 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 15:29:48,652 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 15:29:48,653 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 15:29:48,667 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 15:29:48,679 - WeChatAutoAdd - DEBUG - 找到微信窗口: ● main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 15:29:48,682 - WeChatAutoAdd - INFO - 共找到 2 个微信窗口
2025-07-28 15:29:48,686 - WeChatAutoAdd - INFO - 找到微信主窗口: 微信
2025-07-28 15:29:48,696 - WeChatAutoAdd - WARNING - 方法1截图失败: Error code from Windows: 18 - 没有更多文件。, 尝试方法2
2025-07-28 15:29:48,914 - WeChatAutoAdd - INFO - 使用win32方法截取窗口: 
2025-07-28 15:29:48,919 - WeChatAutoAdd - INFO - win32窗口尺寸: 1x1, 位置: (0, 0)
2025-07-28 15:29:48,930 - WeChatAutoAdd - WARNING - win32 BitBlt操作可能失败
2025-07-28 15:29:48,933 - WeChatAutoAdd - WARNING - 截图可能为纯色图像，标准差: 0.0
2025-07-28 15:29:48,934 - WeChatAutoAdd - WARNING - win32截图内容验证失败
2025-07-28 15:29:48,941 - WeChatAutoAdd - INFO - 保存win32截图: screenshots\window_capture_win32_20250728_152948.png
2025-07-28 15:29:48,944 - WeChatAutoAdd - INFO - win32截图成功
2025-07-28 15:29:48,946 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 15:29:48,950 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 15:29:48,952 - WeChatAutoAdd - INFO - 截图尺寸: 1x1
2025-07-28 15:29:48,958 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=0-1 (高度:1)
2025-07-28 15:29:48,963 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_152948.png
2025-07-28 15:29:48,965 - WeChatAutoAdd - INFO - 底部区域原始检测到 0 个轮廓
2025-07-28 15:29:48,967 - WeChatAutoAdd - WARNING - 底部245像素区域未找到符合条件的按钮
2025-07-28 15:29:48,968 - WeChatAutoAdd - WARNING - 底部区域未找到按钮
2025-07-28 15:29:48,974 - WeChatAutoAdd - ERROR - 无法找到'添加到通讯录'按钮，结束当前流程
2025-07-28 15:29:48,977 - modules.wechat_auto_add_simple - ERROR - ❌ 13659817026 添加朋友操作执行失败
2025-07-28 15:29:48,977 - modules.wechat_auto_add_simple - WARNING - ⚠️ 添加朋友失败: 13659817026 - 添加朋友操作失败，可能原因：未找到'添加朋友'窗口或'添加到通讯录'按钮
2025-07-28 15:29:53,240 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 6/2993
2025-07-28 15:29:53,240 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 19232700797 (叶子浩)
