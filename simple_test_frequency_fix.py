#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化测试频率错误处理器修复
"""

import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_import():
    """测试导入"""
    try:
        print("🧪 测试导入 frequency_error_handler...")
        from modules.frequency_error_handler import FrequencyErrorHandler
        print("✅ 导入成功")
        
        print("🔧 测试初始化...")
        handler = FrequencyErrorHandler()
        print("✅ 初始化成功")
        
        print("🔍 检查新方法...")
        if hasattr(handler, '_close_add_friend_window_by_coordinates'):
            print("✅ _close_add_friend_window_by_coordinates 方法存在")
        else:
            print("❌ _close_add_friend_window_by_coordinates 方法不存在")
            
        if hasattr(handler, '_close_current_wechat_window_by_coordinates'):
            print("✅ _close_current_wechat_window_by_coordinates 方法存在")
        else:
            print("❌ _close_current_wechat_window_by_coordinates 方法不存在")
        
        print("🎉 测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

if __name__ == "__main__":
    test_import()
