#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信自动化添加好友启动脚本
功能：提供用户友好的启动界面和选项

版本：1.0.0
作者：AI助手
创建时间：2025-01-28
"""

import sys
from pathlib import Path
from datetime import datetime, timezone, timedelta

def print_banner():
    """打印程序横幅"""
    print("=" * 80)
    print("🚀 微信自动化添加好友系统 v1.0.0")
    print("=" * 80)
    print("功能特性:")
    print("✅ 严格6步骤顺序执行：窗口管理→主界面→简单添加→图像识别→好友申请→频率处理")
    print("✅ 单窗口完整性：每个微信窗口必须完成全部6个步骤后才能切换到下一个窗口")
    print("✅ 循环处理机制：支持多个微信窗口的循环处理")
    print("✅ 强制置顶：每完成一个步骤后强制置顶当前操作的微信窗口")
    print("✅ 智能错误处理：异常捕获、自动重试、详细日志记录")
    print("✅ 实时进度跟踪：显示当前处理的微信窗口和执行步骤")
    print("✅ 配置和数据管理：从Excel读取联系人信息，支持参数配置")
    print("✅ 结果统计：统计成功添加、失败、跳过的好友数量")
    print("✅ 实时北京时间：所有操作都使用北京在线时间")
    print("=" * 80)

def check_requirements():
    """检查运行环境和必要文件"""
    print("🔍 检查运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ Python版本过低，需要Python 3.7或更高版本")
        return False
    print(f"✅ Python版本: {sys.version.split()[0]}")
    
    # 检查必要文件
    required_files = [
        "main_controller.py",
        "step_executor.py",
        "config.json",
        "添加好友名单.xlsx"
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
        else:
            print(f"✅ 文件存在: {file}")
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"  - {file}")
        return False
    
    # 检查modules目录
    if not Path("modules").exists():
        print("❌ 缺少modules目录")
        return False
    print("✅ modules目录存在")
    
    # 检查日志目录
    log_dir = Path("logs/current")
    log_dir.mkdir(parents=True, exist_ok=True)
    print("✅ 日志目录已准备")
    
    return True

def show_excel_requirements():
    """显示Excel文件要求"""
    print("\n📋 Excel文件要求:")
    print("文件名: 添加好友名单.xlsx")
    print("必须包含以下列:")
    print("  - 手机号码: 待添加好友的手机号")
    print("  - 姓名: 联系人姓名")
    print("  - 准考证: 准考证号（用于生成备注）")
    print("  - 验证信息: 添加好友时的验证消息")
    print("  - 处理状态: 处理状态（待处理/成功/失败）")
    print("  - 处理结果: 处理结果详情")
    print("  - 处理时间: 处理时间（北京时间）")
    print("  - 微信窗口: 处理该联系人的微信窗口编号")
    print("  - 重试次数: 重试次数统计")

def show_usage_instructions():
    """显示使用说明"""
    print("\n📖 使用说明:")
    print("1. 确保微信已登录并打开")
    print("2. 确保Excel文件格式正确且包含待处理联系人")
    print("3. 程序将自动检测所有微信窗口")
    print("4. 每个微信窗口将严格按照6个步骤顺序执行")
    print("5. 程序会自动强制置顶当前操作的微信窗口")
    print("6. 处理结果将实时更新到Excel文件中")
    print("7. 详细日志将保存在logs/current目录中")

def get_user_confirmation():
    """获取用户确认"""
    print("\n⚠️ 重要提醒:")
    print("1. 请确保微信已登录且处于正常状态")
    print("2. 程序运行期间请勿手动操作微信")
    print("3. 建议在网络稳定的环境下运行")
    print("4. 程序会自动处理频率限制等错误")
    print("5. 可以随时按Ctrl+C中断程序")
    
    while True:
        choice = input("\n是否开始执行自动化流程？(y/n): ").strip().lower()
        if choice in ['y', 'yes', '是']:
            return True
        elif choice in ['n', 'no', '否']:
            return False
        else:
            print("请输入 y 或 n")

def main():
    """主函数"""
    try:
        # 打印横幅
        print_banner()
        
        # 显示当前时间（修复：使用正确的北京时区）
        beijing_tz = timezone(timedelta(hours=8))
        beijing_time = datetime.now(beijing_tz).strftime('%Y-%m-%d %H:%M:%S')
        print(f"📅 当前北京时间: {beijing_time}")
        
        # 检查运行环境
        if not check_requirements():
            print("\n❌ 环境检查失败，程序退出")
            input("按回车键退出...")
            return False
        
        print("\n✅ 环境检查通过")
        
        # 显示Excel要求
        show_excel_requirements()
        
        # 显示使用说明
        show_usage_instructions()
        
        # 获取用户确认
        if not get_user_confirmation():
            print("\n👋 用户取消执行，程序退出")
            return False
        
        print("\n🚀 开始启动微信自动化系统...")
        print("=" * 80)
        
        # 导入并运行主控制器
        try:
            from main_controller import main as run_main_controller
            success = run_main_controller()
            
            if success:
                print("\n🎉 程序执行完成!")
                print("📊 请查看Excel文件和日志文件获取详细结果")
            else:
                print("\n❌ 程序执行失败!")
                print("📄 请查看日志文件获取错误详情")
            
            return success
            
        except ImportError as e:
            print(f"\n❌ 导入主控制器失败: {e}")
            print("请确保main_controller.py文件存在且格式正确")
            return False
        
        except Exception as e:
            print(f"\n❌ 程序执行异常: {e}")
            return False
    
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断程序")
        return False
    
    except Exception as e:
        print(f"\n❌ 启动脚本异常: {e}")
        return False
    
    finally:
        input("\n按回车键退出...")

if __name__ == "__main__":
    main()
