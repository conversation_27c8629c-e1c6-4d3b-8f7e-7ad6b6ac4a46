2025-07-28 23:32:23,294 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 23:32:23,295 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 23:32:23,295 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-28 23:32:23,296 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 23:32:23,297 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-28 23:32:23,297 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 23:32:23,297 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 23:32:23,298 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 23:32:23,299 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 23:32:23,300 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 23:32:23,300 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:32:23,309 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 23:32:23,330 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250728_233223.log
2025-07-28 23:32:23,348 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-28 23:32:23,357 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-28 23:32:23,358 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-28 23:32:23,364 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-28 23:32:23,367 - __main__ - INFO - 📅 当前北京时间: 2025-07-29 07:32:23
2025-07-28 23:32:23,368 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-28 23:32:23,369 - __main__ - INFO - 📅 启动时间: 2025-07-29 07:32:23
2025-07-28 23:32:23,372 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-28 23:32:23,378 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 23:32:23,930 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 23:32:23,976 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 23:32:24,544 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 23:32:24,545 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 23:32:24,548 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-28 23:32:24,548 - __main__ - INFO - ✅ 找到 2 个微信窗口
2025-07-28 23:32:24,549 - __main__ - INFO -   窗口 1: 微信 (句柄: 197994)
2025-07-28 23:32:24,549 - __main__ - INFO -   窗口 2: 微信 (句柄: 525668)
2025-07-28 23:32:24,549 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-28 23:32:25,542 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-28 23:32:25,542 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-28 23:32:25,542 - __main__ - INFO - 📋 待处理联系人数: 2968
2025-07-28 23:32:25,543 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-28 23:32:25,543 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2968
2025-07-28 23:32:25,543 - __main__ - INFO - 
============================================================
2025-07-28 23:32:25,544 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口
2025-07-28 23:32:25,544 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-28 23:32:25,544 - __main__ - INFO - ============================================================
2025-07-28 23:32:25,545 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-28 23:32:25,545 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-28 23:32:25,545 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-28 23:32:25,545 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-28 23:32:25,546 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 23:32:25,865 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 23:32:25,866 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 23:32:25,866 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 23:32:25,866 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 23:32:25,866 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 23:32:25,867 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 23:32:25,867 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 23:32:25,867 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 23:32:25,868 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 23:32:25,868 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 23:32:26,071 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 23:32:26,071 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 23:32:26,071 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-28 23:32:26,072 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 23:32:26,376 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 23:32:26,376 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 23:32:26,377 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 23:32:26,377 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 23:32:26,377 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 23:32:26,377 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 23:32:26,378 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 23:32:26,378 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 23:32:26,378 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 23:32:26,379 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 23:32:26,581 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 23:32:26,581 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 23:32:26,583 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-28 23:32:26,884 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 23:32:26,885 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 23:32:26,886 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 23:32:26,887 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 23:32:26,888 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 23:32:26,888 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 23:32:26,888 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-28 23:32:26,888 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-28 23:32:27,889 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-28 23:32:27,889 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-28 23:32:27,890 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-28 23:32:27,890 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-28 23:32:27,890 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-28 23:32:27,891 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-28 23:32:27,891 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-28 23:32:27,891 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-28 23:32:28,092 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-28 23:32:28,093 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-28 23:32:30,472 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-28 23:32:30,474 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-28 23:32:30,475 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.5 秒...
2025-07-28 23:32:32,998 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-28 23:32:33,198 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-28 23:32:33,199 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-28 23:32:35,572 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-28 23:32:35,573 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-28 23:32:35,573 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.6 秒...
2025-07-28 23:32:38,163 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-28 23:32:38,364 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-28 23:32:38,365 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-28 23:32:40,739 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-28 23:32:40,739 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-28 23:32:40,740 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-28 23:32:43,044 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-28 23:32:43,245 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-28 23:32:43,246 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-28 23:32:45,621 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-28 23:32:45,622 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-28 23:32:45,622 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.8 秒...
2025-07-28 23:32:47,419 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-28 23:32:47,620 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-28 23:32:47,622 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-28 23:32:50,005 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-28 23:32:50,006 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-28 23:32:50,006 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-28 23:32:50,007 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-28 23:32:50,007 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 23:32:50,009 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 23:32:50,009 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 23:32:50,010 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 23:32:50,011 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 23:32:50,013 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 2689744, 进程: Weixin.exe)
2025-07-28 23:32:50,018 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-28 23:32:50,019 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 2689744)
2025-07-28 23:32:50,022 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 2689744) - 增强版
2025-07-28 23:32:50,326 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 23:32:50,326 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 23:32:50,327 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-28 23:32:50,327 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-28 23:32:50,327 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-28 23:32:50,328 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-28 23:32:50,533 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-28 23:32:50,533 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-28 23:32:50,735 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 23:32:50,736 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 23:32:50,736 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-28 23:32:50,737 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-28 23:32:50,737 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-28 23:32:50,737 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-28 23:32:50,737 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-28 23:32:51,738 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-28 23:32:51,738 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 23:32:51,740 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 23:32:51,740 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 23:32:51,741 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 23:32:51,742 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 23:32:51,743 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 2689744, 进程: Weixin.exe)
2025-07-28 23:32:51,746 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-28 23:32:51,747 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-28 23:32:51,747 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-28 23:32:51,747 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-28 23:32:51,748 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-28 23:32:51,748 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 23:32:52,056 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 23:32:52,057 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 23:32:52,057 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 23:32:52,058 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 23:32:52,058 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 23:32:52,059 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 23:32:52,059 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 23:32:52,060 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 23:32:52,060 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 23:32:52,061 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 23:32:52,262 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 23:32:52,263 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 23:32:52,264 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-28 23:32:52,565 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 23:32:52,566 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-28 23:32:52,566 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-28 23:32:53,566 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-28 23:32:53,569 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-28 23:32:53,570 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-28 23:32:53,573 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250728_233253.log
2025-07-28 23:32:53,575 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-28 23:32:53,576 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-28 23:32:53,577 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-28 23:32:53,578 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-28 23:32:53,580 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-28 23:32:53,582 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-28 23:32:53,583 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-28 23:32:53,594 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-28 23:32:53,595 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-28 23:32:53,596 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-28 23:32:53,597 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-28 23:32:53,598 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 23:32:53,599 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 2689744
2025-07-28 23:32:53,604 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 2689744) - 增强版
2025-07-28 23:32:53,919 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 23:32:53,920 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 23:32:53,921 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-28 23:32:53,922 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-28 23:32:53,928 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-28 23:32:53,930 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-28 23:32:53,946 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-28 23:32:53,949 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-28 23:32:54,156 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 23:32:54,157 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 23:32:54,159 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 2689744 (API返回: None)
2025-07-28 23:32:54,460 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 23:32:54,461 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-28 23:32:54,461 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-28 23:32:54,462 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-28 23:32:54,463 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 23:32:54,464 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-28 23:32:54,464 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-28 23:32:54,471 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-28 23:32:54,476 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-28 23:32:54,937 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-28 23:32:54,939 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 23:32:55,316 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2968 个
2025-07-28 23:32:55,317 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2968 个 (总计: 3135 个)
2025-07-28 23:32:55,319 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 10 个联系人后切换
2025-07-28 23:32:55,320 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {}
2025-07-28 23:32:55,321 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:32:55,323 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-28 23:32:55,324 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2968
2025-07-28 23:32:55,325 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18680683915 (陈浪)
2025-07-28 23:32:55,325 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:33:01,934 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18680683915
2025-07-28 23:33:01,936 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 23:33:01,937 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18680683915 执行添加朋友操作...
2025-07-28 23:33:01,938 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 23:33:01,938 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 23:33:01,939 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 23:33:01,941 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 23:33:01,948 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 3 个图片文件
2025-07-28 23:33:01,953 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 23:33:01,954 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 23:33:01,955 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 23:33:01,955 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 23:33:01,956 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 23:33:01,957 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 23:33:01,961 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 23:33:01,964 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-28 23:33:01,969 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 23:33:01,972 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 23:33:01,974 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 23:33:01,977 - WeChatAutoAdd - DEBUG - 找到微信窗口: 申请添加朋友 - 360x660
2025-07-28 23:33:01,980 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-07-28 23:33:01,981 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-28 23:33:02,484 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-28 23:33:02,486 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-28 23:33:02,566 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差42.80, 边缘比例0.0428
2025-07-28 23:33:02,575 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_233302.png
2025-07-28 23:33:02,576 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 23:33:02,578 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 23:33:02,579 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-28 23:33:02,579 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-28 23:33:02,580 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-28 23:33:02,585 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_233302.png
2025-07-28 23:33:02,589 - WeChatAutoAdd - INFO - 底部区域原始检测到 26 个轮廓
2025-07-28 23:33:02,592 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,323), 尺寸128x30, 长宽比4.27, 已知特征:True
2025-07-28 23:33:02,593 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=323 (距底部154像素区域)
2025-07-28 23:33:02,594 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 23:33:02,595 - WeChatAutoAdd - INFO - 发现已知按钮特征候选: 位置(100,323), 尺寸128x30
2025-07-28 23:33:02,596 - WeChatAutoAdd - INFO - 发现已知按钮特征: 位置(100,323), 尺寸128x30
2025-07-28 23:33:02,597 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(81,259), 尺寸8x4, 长宽比2.00, 面积32
2025-07-28 23:33:02,598 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,258), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 23:33:02,600 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(49,257), 尺寸1x5, 长宽比0.20, 面积5
2025-07-28 23:33:02,604 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(47,257), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:33:02,605 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(71,256), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 23:33:02,609 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,255), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 23:33:02,611 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(73,254), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:33:02,612 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:33:02,613 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,253), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 23:33:02,615 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(55,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:33:02,619 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(120,249), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-28 23:33:02,625 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=63.1 (阈值:60)
2025-07-28 23:33:02,627 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(52,249), 尺寸39x14, 长宽比2.79, 面积546
2025-07-28 23:33:02,630 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(51,249), 尺寸25x12, 长宽比2.08, 面积300
2025-07-28 23:33:02,632 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(48,249), 尺寸13x7, 长宽比1.86, 面积91
2025-07-28 23:33:02,634 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(200,236), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:33:02,639 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(186,236), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:33:02,640 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(196,234), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 23:33:02,642 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(182,234), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 23:33:02,643 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(199,233), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 23:33:02,648 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(185,233), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 23:33:02,657 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(156,233), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:33:02,658 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(158,231), 尺寸61x13, 长宽比4.69, 面积793
2025-07-28 23:33:02,659 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=89.9 (阈值:60)
2025-07-28 23:33:02,662 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,231), 尺寸13x12, 长宽比1.08, 面积156
2025-07-28 23:33:02,663 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=68.6 (阈值:60)
2025-07-28 23:33:02,664 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,230), 尺寸28x14, 长宽比2.00, 面积392
2025-07-28 23:33:02,665 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=95.2 (阈值:60)
2025-07-28 23:33:02,666 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-28 23:33:02,673 - WeChatAutoAdd - INFO - 底部区域找到 3 个按钮候选
2025-07-28 23:33:02,674 - WeChatAutoAdd - INFO - 选择已知按钮特征: Y=323, 很可能是'添加到通讯录'按钮
2025-07-28 23:33:02,675 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 338), 尺寸: 128x30, 位置得分: 5.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-07-28 23:33:02,677 - WeChatAutoAdd - INFO - 检测到高置信度按钮，跳过文字验证
2025-07-28 23:33:02,690 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_233302.png
2025-07-28 23:33:02,691 - WeChatAutoAdd - INFO - 检测到高置信度按钮（已知特征/绿色按钮），跳过文字验证
2025-07-28 23:33:02,693 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 23:33:02,995 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 338) -> 屏幕坐标(1364, 338)
2025-07-28 23:33:03,803 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 23:33:03,804 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 23:33:03,805 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:33:03,806 - modules.wechat_auto_add_simple - INFO - ✅ 18680683915 添加朋友操作执行成功
2025-07-28 23:33:03,806 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:33:03,806 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 23:33:05,808 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-28 23:33:05,808 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-28 23:33:05,808 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-28 23:33:05,809 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 23:33:05,809 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 23:33:05,809 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 23:33:05,810 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 23:33:05,810 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 23:33:05,811 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-28 23:33:05,811 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18680683915
2025-07-28 23:33:05,815 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-28 23:33:05,815 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 23:33:05,815 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:695 in _handle_friend_request_window
2025-07-28 23:33:05,819 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-28 23:33:05,820 - modules.friend_request_window - INFO -    📱 phone: '18680683915'
2025-07-28 23:33:05,821 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-28 23:33:05,822 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-28 23:33:06,303 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-28 23:33:06,303 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-28 23:33:06,304 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-28 23:33:06,304 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 23:33:06,305 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18680683915
2025-07-28 23:33:06,306 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-28 23:33:06,306 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 23:33:06,310 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-28 23:33:06,310 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-28 23:33:06,310 - modules.friend_request_window - INFO -    📱 手机号码: 18680683915
2025-07-28 23:33:06,311 - modules.friend_request_window - INFO -    🆔 准考证: 014325110079
2025-07-28 23:33:06,311 - modules.friend_request_window - INFO -    👤 姓名: 陈浪
2025-07-28 23:33:06,311 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 23:33:06,311 - modules.friend_request_window - INFO -    📝 备注格式: '014325110079-陈浪-2025-07-29 07:33:06'
2025-07-28 23:33:06,312 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 23:33:06,312 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110079-陈浪-2025-07-29 07:33:06'
2025-07-28 23:33:06,312 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 23:33:06,313 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 8587954, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-28 23:33:06,314 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 8587954)
2025-07-28 23:33:06,315 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-28 23:33:06,315 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-28 23:33:06,316 - modules.friend_request_window - INFO - 🔄 激活窗口: 8587954
2025-07-28 23:33:07,022 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-28 23:33:07,022 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-28 23:33:07,023 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-28 23:33:07,023 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:499 in _analyze_search_result
2025-07-28 23:33:07,023 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-28 23:33:07,024 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 23:33:07,024 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-28 23:33:07,024 - modules.friend_request_window - INFO -    3. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:695 in _handle_friend_request_window
2025-07-28 23:33:07,024 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-28 23:33:07,025 - modules.friend_request_window - INFO -    4. D:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-28 23:33:07,025 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-28 23:33:07,025 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-28 23:33:07,025 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-28 23:33:07,026 - modules.friend_request_window - INFO -    📝 remark参数: '014325110079-陈浪-2025-07-29 07:33:06' (类型: <class 'str'>, 长度: 35)
2025-07-28 23:33:07,026 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-28 23:33:07,026 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110079-陈浪-2025-07-29 07:33:06'
2025-07-28 23:33:07,027 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-28 23:33:07,027 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-28 23:33:07,027 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-28 23:33:07,028 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-28 23:33:07,028 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-28 23:33:07,029 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-28 23:33:07,029 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-28 23:33:07,938 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-28 23:33:13,209 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-28 23:33:13,209 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-28 23:33:13,210 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-28 23:33:13,210 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-28 23:33:13,212 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'PS D:\程序-测试\微信7.28 - 副本 (2)> python main_controlle...' (前50字符)
2025-07-28 23:33:13,526 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 23:33:13,526 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 23:33:14,430 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 23:33:14,440 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 23:33:14,440 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-28 23:33:14,440 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-28 23:33:14,441 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-28 23:33:14,441 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-28 23:33:14,942 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-28 23:33:14,943 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-28 23:33:14,943 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-28 23:33:14,943 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-28 23:33:14,943 - modules.friend_request_window - INFO -    📝 内容: '014325110079-陈浪-2025-07-29 07:33:06'
2025-07-28 23:33:14,944 - modules.friend_request_window - INFO -    📏 内容长度: 35 字符
2025-07-28 23:33:14,944 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110079-\xe9\x99\x88\xe6\xb5\xaa-2025-07-29 07:33:06'
2025-07-28 23:33:14,944 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-28 23:33:15,854 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-28 23:33:21,114 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-28 23:33:21,115 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-28 23:33:21,116 - modules.friend_request_window - INFO -    📝 原始文本: '014325110079-陈浪-2025-07-29 07:33:06'
2025-07-28 23:33:21,117 - modules.friend_request_window - INFO -    📏 文本长度: 35 字符
2025-07-28 23:33:21,118 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'PS D:\程序-测试\微信7.28 - 副本 (2)> python main_controlle...' (前50字符)
2025-07-28 23:33:21,428 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 23:33:21,429 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 23:33:22,331 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 23:33:22,345 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 23:33:22,345 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110079-陈浪-2025-07-29 07:33:06'
2025-07-28 23:33:22,346 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-28 23:33:22,347 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110079-陈浪-2025-07-29 07:33:06'
2025-07-28 23:33:22,347 - modules.friend_request_window - INFO -    � 内容长度: 35 字符
2025-07-28 23:33:22,848 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110079-陈浪-2025-07-29 07:33:06'
2025-07-28 23:33:22,848 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-28 23:33:22,851 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-28 23:33:22,851 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-28 23:33:22,851 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-28 23:33:22,852 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-28 23:33:22,853 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-28 23:33:23,653 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-28 23:33:23,654 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-28 23:33:23,655 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-28 23:33:24,271 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:33:24,272 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-28 23:33:24,272 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-28 23:33:24,272 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-28 23:33:24,791 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:33:25,025 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:33:25,262 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:33:25,495 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:33:25,727 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:33:25,967 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:33:26,206 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:33:26,492 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:33:26,770 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:33:27,002 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:33:27,238 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:33:27,471 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:33:27,704 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:33:27,938 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:33:28,173 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:33:28,416 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:33:28,658 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:33:28,893 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:33:29,126 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:33:29,342 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-28 23:33:29,343 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-28 23:33:30,344 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 23:33:30,347 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-28 23:33:30,347 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-28 23:33:30,347 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-28 23:33:30,348 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-28 23:33:30,354 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 23:33:30,355 - modules.friend_request_window - INFO -    📝 备注信息: '014325110079-陈浪-2025-07-29 07:33:06'
2025-07-28 23:33:30,856 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-28 23:33:30,857 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:33:30,857 - modules.wechat_auto_add_simple - INFO - ✅ 18680683915 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-28 23:33:30,857 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 18680683915
2025-07-28 23:33:30,858 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:33:34,489 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2968
2025-07-28 23:33:34,490 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15326352540 (刘建涛)
2025-07-28 23:33:34,490 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:33:41,067 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15326352540
2025-07-28 23:33:41,067 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 23:33:41,068 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15326352540 执行添加朋友操作...
2025-07-28 23:33:41,068 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 23:33:41,068 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 23:33:41,069 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 23:33:41,070 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 23:33:41,074 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 3 个图片文件
2025-07-28 23:33:41,077 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 23:33:41,077 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 23:33:41,078 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 23:33:41,078 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 23:33:41,078 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 23:33:41,079 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 23:33:41,079 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 23:33:41,083 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-28 23:33:41,084 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 23:33:41,087 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 23:33:41,089 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 23:33:41,103 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-28 23:33:41,105 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-28 23:33:41,607 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-28 23:33:41,610 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-28 23:33:41,692 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差37.56, 边缘比例0.0435
2025-07-28 23:33:41,700 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_233341.png
2025-07-28 23:33:41,702 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 23:33:41,703 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 23:33:41,704 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-28 23:33:41,705 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-28 23:33:41,706 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-28 23:33:41,711 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_233341.png
2025-07-28 23:33:41,713 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-28 23:33:41,716 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-28 23:33:41,720 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-28 23:33:41,721 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-28 23:33:41,721 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-28 23:33:41,723 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-28 23:33:41,724 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-28 23:33:41,725 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 23:33:41,735 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_233341.png
2025-07-28 23:33:41,738 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 23:33:41,739 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-28 23:33:41,746 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_233341.png
2025-07-28 23:33:41,805 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 23:33:41,807 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-28 23:33:41,808 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 23:33:41,810 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 23:33:42,114 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-28 23:33:42,924 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 23:33:42,925 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 23:33:42,926 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:33:42,926 - modules.wechat_auto_add_simple - INFO - ✅ 15326352540 添加朋友操作执行成功
2025-07-28 23:33:42,926 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:33:42,927 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 23:33:44,928 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-28 23:33:44,929 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-28 23:33:44,929 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-28 23:33:44,929 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 23:33:44,930 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 23:33:44,930 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 23:33:44,930 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 23:33:44,933 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 23:33:44,934 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-28 23:33:44,934 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 15326352540
2025-07-28 23:33:44,936 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-28 23:33:44,937 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 23:33:44,938 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:695 in _handle_friend_request_window
2025-07-28 23:33:44,940 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-28 23:33:44,941 - modules.friend_request_window - INFO -    📱 phone: '15326352540'
2025-07-28 23:33:44,943 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-28 23:33:44,943 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-28 23:33:45,395 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-28 23:33:45,395 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-28 23:33:45,396 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-28 23:33:45,396 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 23:33:45,399 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 15326352540
2025-07-28 23:33:45,399 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-28 23:33:45,401 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 23:33:45,402 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-28 23:33:45,402 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-28 23:33:45,403 - modules.friend_request_window - INFO -    📱 手机号码: 15326352540
2025-07-28 23:33:45,403 - modules.friend_request_window - INFO -    🆔 准考证: 014325110080
2025-07-28 23:33:45,403 - modules.friend_request_window - INFO -    👤 姓名: 刘建涛
2025-07-28 23:33:45,404 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 23:33:45,404 - modules.friend_request_window - INFO -    📝 备注格式: '014325110080-刘建涛-2025-07-29 07:33:45'
2025-07-28 23:33:45,404 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 23:33:45,404 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110080-刘建涛-2025-07-29 07:33:45'
2025-07-28 23:33:45,405 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 23:33:45,406 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 8194272, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-28 23:33:45,408 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 8194272)
2025-07-28 23:33:45,408 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-28 23:33:45,408 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-28 23:33:45,408 - modules.friend_request_window - INFO - 🔄 激活窗口: 8194272
2025-07-28 23:33:46,111 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-28 23:33:46,111 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-28 23:33:46,112 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-28 23:33:46,112 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:499 in _analyze_search_result
2025-07-28 23:33:46,113 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-28 23:33:46,113 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 23:33:46,113 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-28 23:33:46,114 - modules.friend_request_window - INFO -    3. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:695 in _handle_friend_request_window
2025-07-28 23:33:46,115 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-28 23:33:46,116 - modules.friend_request_window - INFO -    4. D:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-28 23:33:46,116 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-28 23:33:46,117 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-28 23:33:46,117 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-28 23:33:46,119 - modules.friend_request_window - INFO -    📝 remark参数: '014325110080-刘建涛-2025-07-29 07:33:45' (类型: <class 'str'>, 长度: 36)
2025-07-28 23:33:46,119 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-28 23:33:46,120 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110080-刘建涛-2025-07-29 07:33:45'
2025-07-28 23:33:46,120 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-28 23:33:46,120 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-28 23:33:46,121 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-28 23:33:46,121 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-28 23:33:46,121 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-28 23:33:46,121 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-28 23:33:46,122 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-28 23:33:47,036 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-28 23:33:52,288 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-28 23:33:52,288 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-28 23:33:52,289 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-28 23:33:52,289 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-28 23:33:52,290 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '014325110079-陈浪-2025-07-29 07:33:06...' (前50字符)
2025-07-28 23:33:52,603 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 23:33:52,603 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 23:33:53,522 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 23:33:53,536 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 23:33:53,538 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-28 23:33:53,539 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-28 23:33:53,539 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-28 23:33:53,540 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-28 23:33:54,042 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-28 23:33:54,042 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-28 23:33:54,042 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-28 23:33:54,043 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-28 23:33:54,043 - modules.friend_request_window - INFO -    📝 内容: '014325110080-刘建涛-2025-07-29 07:33:45'
2025-07-28 23:33:54,043 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-28 23:33:54,044 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110080-\xe5\x88\x98\xe5\xbb\xba\xe6\xb6\x9b-2025-07-29 07:33:45'
2025-07-28 23:33:54,044 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-28 23:33:54,951 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-28 23:34:00,197 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-28 23:34:00,198 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-28 23:34:00,198 - modules.friend_request_window - INFO -    📝 原始文本: '014325110080-刘建涛-2025-07-29 07:33:45'
2025-07-28 23:34:00,198 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-28 23:34:00,199 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '014325110079-陈浪-2025-07-29 07:33:06...' (前50字符)
2025-07-28 23:34:00,508 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 23:34:00,508 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 23:34:01,410 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 23:34:01,421 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 23:34:01,421 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110080-刘建涛-2025-07-29 07:33:45'
2025-07-28 23:34:01,422 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-28 23:34:01,423 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110080-刘建涛-2025-07-29 07:33:45'
2025-07-28 23:34:01,424 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-28 23:34:01,924 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110080-刘建涛-2025-07-29 07:33:45'
2025-07-28 23:34:01,925 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-28 23:34:01,925 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-28 23:34:01,925 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-28 23:34:01,926 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-28 23:34:01,926 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-28 23:34:01,926 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-28 23:34:02,727 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-28 23:34:02,727 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-28 23:34:02,728 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-28 23:34:03,352 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:34:03,384 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-28 23:34:03,414 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-28 23:34:03,433 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-28 23:34:03,960 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:04,192 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:04,424 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:04,666 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:04,898 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:05,132 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:05,363 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:05,596 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:05,827 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:06,060 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:06,294 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:06,526 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:06,760 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:06,991 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:07,223 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:07,454 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:07,689 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:07,925 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:08,159 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:08,392 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:08,608 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-28 23:34:08,609 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-28 23:34:09,609 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 23:34:09,612 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-28 23:34:09,612 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-28 23:34:09,612 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-28 23:34:09,613 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-28 23:34:09,613 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 23:34:09,613 - modules.friend_request_window - INFO -    📝 备注信息: '014325110080-刘建涛-2025-07-29 07:33:45'
2025-07-28 23:34:10,114 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-28 23:34:10,115 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:34:10,116 - modules.wechat_auto_add_simple - INFO - ✅ 15326352540 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-28 23:34:10,116 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 15326352540
2025-07-28 23:34:10,117 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:34:13,370 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 3/2968
2025-07-28 23:34:13,370 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18627291913 (余超)
2025-07-28 23:34:13,370 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:34:19,941 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18627291913
2025-07-28 23:34:19,942 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 23:34:19,942 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18627291913 执行添加朋友操作...
2025-07-28 23:34:19,942 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 23:34:19,943 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 23:34:19,943 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 23:34:19,944 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 23:34:19,954 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 23:34:19,960 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 23:34:19,960 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 23:34:19,961 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 23:34:19,962 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 23:34:19,963 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 23:34:19,963 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 23:34:19,972 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 23:34:19,976 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-28 23:34:19,985 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 23:34:19,988 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 23:34:20,008 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 23:34:20,023 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-28 23:34:20,024 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-28 23:34:20,526 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-28 23:34:20,528 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-28 23:34:20,606 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差34.00, 边缘比例0.0373
2025-07-28 23:34:20,614 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_233420.png
2025-07-28 23:34:20,617 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 23:34:20,618 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 23:34:20,619 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-28 23:34:20,620 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-28 23:34:20,621 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-28 23:34:20,628 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_233420.png
2025-07-28 23:34:20,631 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-28 23:34:20,633 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-28 23:34:20,637 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-28 23:34:20,640 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-28 23:34:20,641 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-28 23:34:20,642 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-28 23:34:20,643 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-28 23:34:20,644 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 23:34:20,654 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_233420.png
2025-07-28 23:34:20,656 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 23:34:20,657 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-28 23:34:20,661 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_233420.png
2025-07-28 23:34:20,696 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 23:34:20,701 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-28 23:34:20,704 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 23:34:20,705 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 23:34:21,006 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-28 23:34:21,815 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 23:34:21,816 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 23:34:21,817 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:34:21,818 - modules.wechat_auto_add_simple - INFO - ✅ 18627291913 添加朋友操作执行成功
2025-07-28 23:34:21,818 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:34:21,818 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 23:34:23,820 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-28 23:34:23,820 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-28 23:34:23,821 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-28 23:34:23,821 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 23:34:23,821 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 23:34:23,821 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 23:34:23,822 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 23:34:23,822 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 23:34:23,822 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-28 23:34:23,822 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18627291913
2025-07-28 23:34:23,823 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-28 23:34:23,823 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 23:34:23,824 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:695 in _handle_friend_request_window
2025-07-28 23:34:23,824 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-28 23:34:23,824 - modules.friend_request_window - INFO -    📱 phone: '18627291913'
2025-07-28 23:34:23,825 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-28 23:34:23,825 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-28 23:34:24,219 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-28 23:34:24,220 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-28 23:34:24,220 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-28 23:34:24,220 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 23:34:24,221 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18627291913
2025-07-28 23:34:24,222 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-28 23:34:24,222 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 23:34:24,223 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-28 23:34:24,223 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-28 23:34:24,223 - modules.friend_request_window - INFO -    📱 手机号码: 18627291913
2025-07-28 23:34:24,223 - modules.friend_request_window - INFO -    🆔 准考证: 014325110081
2025-07-28 23:34:24,224 - modules.friend_request_window - INFO -    👤 姓名: 余超
2025-07-28 23:34:24,224 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 23:34:24,224 - modules.friend_request_window - INFO -    📝 备注格式: '014325110081-余超-2025-07-29 07:34:24'
2025-07-28 23:34:24,225 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 23:34:24,225 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110081-余超-2025-07-29 07:34:24'
2025-07-28 23:34:24,225 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 23:34:24,227 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 5769916, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-28 23:34:24,230 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 5769916)
2025-07-28 23:34:24,232 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-28 23:34:24,232 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-28 23:34:24,233 - modules.friend_request_window - INFO - 🔄 激活窗口: 5769916
2025-07-28 23:34:24,936 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-28 23:34:24,936 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-28 23:34:24,937 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-28 23:34:24,937 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:499 in _analyze_search_result
2025-07-28 23:34:24,937 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-28 23:34:24,937 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 23:34:24,938 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-28 23:34:24,938 - modules.friend_request_window - INFO -    3. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:695 in _handle_friend_request_window
2025-07-28 23:34:24,938 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-28 23:34:24,939 - modules.friend_request_window - INFO -    4. D:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-28 23:34:24,939 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-28 23:34:24,939 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-28 23:34:24,939 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-28 23:34:24,940 - modules.friend_request_window - INFO -    📝 remark参数: '014325110081-余超-2025-07-29 07:34:24' (类型: <class 'str'>, 长度: 35)
2025-07-28 23:34:24,940 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-28 23:34:24,940 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110081-余超-2025-07-29 07:34:24'
2025-07-28 23:34:24,941 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-28 23:34:24,941 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-28 23:34:24,941 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-28 23:34:24,942 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-28 23:34:24,943 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-28 23:34:24,943 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-28 23:34:24,943 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-28 23:34:25,853 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-28 23:34:31,128 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-28 23:34:31,128 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-28 23:34:31,128 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-28 23:34:31,129 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-28 23:34:31,129 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '014325110079-陈浪-2025-07-29 07:33:06...' (前50字符)
2025-07-28 23:34:31,437 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 23:34:31,438 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 23:34:32,340 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 23:34:32,352 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 23:34:32,354 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-28 23:34:32,359 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-28 23:34:32,363 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-28 23:34:32,364 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-28 23:34:32,865 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-28 23:34:32,865 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-28 23:34:32,865 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-28 23:34:32,866 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-28 23:34:32,866 - modules.friend_request_window - INFO -    📝 内容: '014325110081-余超-2025-07-29 07:34:24'
2025-07-28 23:34:32,866 - modules.friend_request_window - INFO -    📏 内容长度: 35 字符
2025-07-28 23:34:32,866 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110081-\xe4\xbd\x99\xe8\xb6\x85-2025-07-29 07:34:24'
2025-07-28 23:34:32,867 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-28 23:34:33,782 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-28 23:34:39,044 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-28 23:34:39,045 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-28 23:34:39,045 - modules.friend_request_window - INFO -    📝 原始文本: '014325110081-余超-2025-07-29 07:34:24'
2025-07-28 23:34:39,045 - modules.friend_request_window - INFO -    📏 文本长度: 35 字符
2025-07-28 23:34:39,047 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '014325110079-陈浪-2025-07-29 07:33:06...' (前50字符)
2025-07-28 23:34:39,356 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 23:34:39,357 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 23:34:40,260 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 23:34:40,273 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 23:34:40,273 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110081-余超-2025-07-29 07:34:24'
2025-07-28 23:34:40,274 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-28 23:34:40,274 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110081-余超-2025-07-29 07:34:24'
2025-07-28 23:34:40,275 - modules.friend_request_window - INFO -    � 内容长度: 35 字符
2025-07-28 23:34:40,777 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110081-余超-2025-07-29 07:34:24'
2025-07-28 23:34:40,778 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-28 23:34:40,778 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-28 23:34:40,778 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-28 23:34:40,778 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-28 23:34:40,779 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-28 23:34:40,779 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-28 23:34:41,580 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-28 23:34:41,580 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-28 23:34:41,581 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-28 23:34:42,216 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:34:42,216 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-28 23:34:42,216 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-28 23:34:42,217 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-28 23:34:42,732 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:42,971 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:43,220 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:43,454 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:43,690 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:43,928 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:44,195 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:44,430 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:44,661 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:44,895 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:45,127 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:45,360 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:45,595 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:45,833 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:46,062 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:46,296 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:46,532 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:46,762 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:46,995 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:47,225 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:34:47,442 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-28 23:34:47,443 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-28 23:34:48,443 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 23:34:48,445 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-28 23:34:48,446 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-28 23:34:48,446 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-28 23:34:48,447 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-28 23:34:48,447 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 23:34:48,447 - modules.friend_request_window - INFO -    📝 备注信息: '014325110081-余超-2025-07-29 07:34:24'
2025-07-28 23:34:48,948 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-28 23:34:48,949 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:34:48,950 - modules.wechat_auto_add_simple - INFO - ✅ 18627291913 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-28 23:34:48,950 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 18627291913
2025-07-28 23:34:48,951 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:34:52,353 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 4/2968
2025-07-28 23:34:52,353 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13437117559 (宋成龙)
2025-07-28 23:34:52,354 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:34:58,923 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13437117559
2025-07-28 23:34:58,924 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 23:34:58,924 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13437117559 执行添加朋友操作...
2025-07-28 23:34:58,924 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 23:34:58,925 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 23:34:58,926 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 23:34:58,927 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 23:34:58,931 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 23:34:58,934 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 23:34:58,934 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 23:34:58,935 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 23:34:58,935 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 23:34:58,936 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 23:34:58,936 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 23:34:58,936 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 23:34:58,947 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-28 23:34:58,954 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 23:34:58,957 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 23:34:58,962 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 23:34:58,975 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-28 23:34:58,981 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-28 23:34:59,486 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-28 23:34:59,488 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-28 23:34:59,568 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差35.85, 边缘比例0.0390
2025-07-28 23:34:59,576 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_233459.png
2025-07-28 23:34:59,579 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 23:34:59,585 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 23:34:59,587 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-28 23:34:59,589 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-28 23:34:59,591 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-28 23:34:59,599 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_233459.png
2025-07-28 23:34:59,603 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-28 23:34:59,605 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-28 23:34:59,608 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-28 23:34:59,611 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-28 23:34:59,618 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-28 23:34:59,621 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-28 23:34:59,624 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-28 23:34:59,631 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 23:34:59,660 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_233459.png
2025-07-28 23:34:59,676 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 23:34:59,688 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-28 23:34:59,694 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_233459.png
2025-07-28 23:34:59,730 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 23:34:59,734 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-28 23:34:59,735 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 23:34:59,738 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 23:35:00,040 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-28 23:35:00,829 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 23:35:00,831 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 23:35:00,833 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:35:00,833 - modules.wechat_auto_add_simple - INFO - ✅ 13437117559 添加朋友操作执行成功
2025-07-28 23:35:00,833 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:35:00,834 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 23:35:02,835 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-28 23:35:02,835 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-28 23:35:02,836 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-28 23:35:02,836 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 23:35:02,836 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 23:35:02,837 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 23:35:02,837 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 23:35:02,837 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 23:35:02,837 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-28 23:35:02,838 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13437117559
2025-07-28 23:35:02,838 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-28 23:35:02,839 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 23:35:02,839 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:695 in _handle_friend_request_window
2025-07-28 23:35:02,839 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-28 23:35:02,839 - modules.friend_request_window - INFO -    📱 phone: '13437117559'
2025-07-28 23:35:02,840 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-28 23:35:02,840 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-28 23:35:03,266 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-28 23:35:03,267 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-28 23:35:03,267 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-28 23:35:03,267 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 23:35:03,269 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13437117559
2025-07-28 23:35:03,269 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-28 23:35:03,270 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 23:35:03,270 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-28 23:35:03,270 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-28 23:35:03,271 - modules.friend_request_window - INFO -    📱 手机号码: 13437117559
2025-07-28 23:35:03,271 - modules.friend_request_window - INFO -    🆔 准考证: 014325110082
2025-07-28 23:35:03,271 - modules.friend_request_window - INFO -    👤 姓名: 宋成龙
2025-07-28 23:35:03,272 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 23:35:03,272 - modules.friend_request_window - INFO -    📝 备注格式: '014325110082-宋成龙-2025-07-29 07:35:03'
2025-07-28 23:35:03,272 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 23:35:03,273 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110082-宋成龙-2025-07-29 07:35:03'
2025-07-28 23:35:03,274 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 23:35:03,276 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 15469260, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-28 23:35:03,279 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 15469260)
2025-07-28 23:35:03,280 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-28 23:35:03,280 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-28 23:35:03,281 - modules.friend_request_window - INFO - 🔄 激活窗口: 15469260
2025-07-28 23:35:03,985 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-28 23:35:03,985 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-28 23:35:03,986 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-28 23:35:03,986 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:499 in _analyze_search_result
2025-07-28 23:35:03,986 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-28 23:35:03,986 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 23:35:03,987 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-28 23:35:03,987 - modules.friend_request_window - INFO -    3. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:695 in _handle_friend_request_window
2025-07-28 23:35:03,987 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-28 23:35:03,987 - modules.friend_request_window - INFO -    4. D:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-28 23:35:03,988 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-28 23:35:03,988 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-28 23:35:03,988 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-28 23:35:03,988 - modules.friend_request_window - INFO -    📝 remark参数: '014325110082-宋成龙-2025-07-29 07:35:03' (类型: <class 'str'>, 长度: 36)
2025-07-28 23:35:03,989 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-28 23:35:03,989 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110082-宋成龙-2025-07-29 07:35:03'
2025-07-28 23:35:03,989 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-28 23:35:03,989 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-28 23:35:03,990 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-28 23:35:03,990 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-28 23:35:03,991 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-28 23:35:03,992 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-28 23:35:03,992 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-28 23:35:04,938 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-28 23:35:10,182 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-28 23:35:10,183 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-28 23:35:10,183 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-28 23:35:10,183 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-28 23:35:10,184 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '014325110079-陈浪-2025-07-29 07:33:06...' (前50字符)
2025-07-28 23:35:10,494 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 23:35:10,495 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 23:35:11,398 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 23:35:11,408 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 23:35:11,408 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-28 23:35:11,409 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-28 23:35:11,412 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-28 23:35:11,413 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-28 23:35:11,914 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-28 23:35:11,914 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-28 23:35:11,914 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-28 23:35:11,915 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-28 23:35:11,915 - modules.friend_request_window - INFO -    📝 内容: '014325110082-宋成龙-2025-07-29 07:35:03'
2025-07-28 23:35:11,915 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-28 23:35:11,916 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110082-\xe5\xae\x8b\xe6\x88\x90\xe9\xbe\x99-2025-07-29 07:35:03'
2025-07-28 23:35:11,916 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-28 23:35:12,829 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-28 23:35:18,075 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-28 23:35:18,075 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-28 23:35:18,076 - modules.friend_request_window - INFO -    📝 原始文本: '014325110082-宋成龙-2025-07-29 07:35:03'
2025-07-28 23:35:18,076 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-28 23:35:18,077 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '014325110079-陈浪-2025-07-29 07:33:06...' (前50字符)
2025-07-28 23:35:18,387 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 23:35:18,388 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 23:35:19,291 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 23:35:19,299 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 23:35:19,299 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110082-宋成龙-2025-07-29 07:35:03'
2025-07-28 23:35:19,300 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-28 23:35:19,301 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110082-宋成龙-2025-07-29 07:35:03'
2025-07-28 23:35:19,301 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-28 23:35:19,802 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110082-宋成龙-2025-07-29 07:35:03'
2025-07-28 23:35:19,803 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-28 23:35:19,803 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-28 23:35:19,804 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-28 23:35:19,804 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-28 23:35:19,804 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-28 23:35:19,804 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-28 23:35:20,605 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-28 23:35:20,606 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-28 23:35:20,606 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-28 23:35:21,211 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:35:21,212 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-28 23:35:21,213 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-28 23:35:21,214 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-28 23:35:21,731 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:35:21,961 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:35:22,195 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:35:22,430 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:35:22,664 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:35:22,895 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:35:23,127 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:35:23,360 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:35:23,590 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:35:23,827 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:35:24,058 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:35:24,289 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:35:24,521 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:35:24,750 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:35:24,980 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:35:25,214 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:35:25,444 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:35:25,675 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:35:25,907 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:35:26,142 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:35:26,362 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-28 23:35:26,362 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-28 23:35:27,362 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 23:35:27,364 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-28 23:35:27,365 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-28 23:35:27,365 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-28 23:35:27,365 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-28 23:35:27,366 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 23:35:27,366 - modules.friend_request_window - INFO -    📝 备注信息: '014325110082-宋成龙-2025-07-29 07:35:03'
2025-07-28 23:35:27,866 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-28 23:35:27,867 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:35:27,868 - modules.wechat_auto_add_simple - INFO - ✅ 13437117559 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-28 23:35:27,868 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 13437117559
2025-07-28 23:35:27,869 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:35:31,176 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 5/2968
2025-07-28 23:35:31,176 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18271296851 (秦智鹏)
2025-07-28 23:35:31,177 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:35:37,754 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18271296851
2025-07-28 23:35:37,755 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 23:35:37,755 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18271296851 执行添加朋友操作...
2025-07-28 23:35:37,755 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 23:35:37,755 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 23:35:37,756 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 23:35:37,758 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 23:35:37,762 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 23:35:37,765 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 23:35:37,765 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 23:35:37,767 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 23:35:37,768 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 23:35:37,768 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 23:35:37,770 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 23:35:37,771 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 23:35:37,779 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-28 23:35:37,784 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 23:35:37,787 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 23:35:37,791 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 23:35:37,799 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-28 23:35:37,804 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-28 23:35:38,307 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-28 23:35:38,309 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-28 23:35:38,379 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差41.65, 边缘比例0.0406
2025-07-28 23:35:38,387 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_233538.png
2025-07-28 23:35:38,390 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 23:35:38,394 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 23:35:38,396 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-28 23:35:38,399 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-28 23:35:38,400 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-28 23:35:38,405 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_233538.png
2025-07-28 23:35:38,408 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-28 23:35:38,414 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-28 23:35:38,416 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-28 23:35:38,418 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-28 23:35:38,420 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-28 23:35:38,422 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-28 23:35:38,425 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-28 23:35:38,430 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 23:35:38,440 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_233538.png
2025-07-28 23:35:38,445 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 23:35:38,448 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-28 23:35:38,456 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_233538.png
2025-07-28 23:35:38,487 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 23:35:38,492 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-28 23:35:38,497 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 23:35:38,500 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 23:35:38,804 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-28 23:35:39,576 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 23:35:39,578 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 23:35:39,579 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:35:39,579 - modules.wechat_auto_add_simple - INFO - ✅ 18271296851 添加朋友操作执行成功
2025-07-28 23:35:39,580 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:35:39,580 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 23:35:41,581 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-28 23:35:41,582 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-28 23:35:41,582 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-28 23:35:41,582 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 23:35:41,583 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 23:35:41,583 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 23:35:41,583 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 23:35:41,584 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 23:35:41,584 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-28 23:35:41,585 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18271296851
2025-07-28 23:35:41,585 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-28 23:35:41,586 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 23:35:41,586 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:695 in _handle_friend_request_window
2025-07-28 23:35:41,586 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-28 23:35:41,587 - modules.friend_request_window - INFO -    📱 phone: '18271296851'
2025-07-28 23:35:41,587 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-28 23:35:41,587 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-28 23:35:42,087 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-28 23:35:42,088 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-28 23:35:42,089 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-28 23:35:42,089 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 23:35:42,091 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18271296851
2025-07-28 23:35:42,091 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-28 23:35:42,092 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 23:35:42,092 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-28 23:35:42,093 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-28 23:35:42,094 - modules.friend_request_window - INFO -    📱 手机号码: 18271296851
2025-07-28 23:35:42,095 - modules.friend_request_window - INFO -    🆔 准考证: 014325110083
2025-07-28 23:35:42,096 - modules.friend_request_window - INFO -    👤 姓名: 秦智鹏
2025-07-28 23:35:42,097 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 23:35:42,100 - modules.friend_request_window - INFO -    📝 备注格式: '014325110083-秦智鹏-2025-07-29 07:35:42'
2025-07-28 23:35:42,104 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 23:35:42,105 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110083-秦智鹏-2025-07-29 07:35:42'
2025-07-28 23:35:42,105 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 23:35:42,107 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 9177376, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-28 23:35:42,112 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 9177376)
2025-07-28 23:35:42,114 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-28 23:35:42,115 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-28 23:35:42,116 - modules.friend_request_window - INFO - 🔄 激活窗口: 9177376
2025-07-28 23:35:42,818 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-28 23:35:42,818 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-28 23:35:42,819 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-28 23:35:42,819 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:499 in _analyze_search_result
2025-07-28 23:35:42,819 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-28 23:35:42,820 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 23:35:42,820 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-28 23:35:42,820 - modules.friend_request_window - INFO -    3. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:695 in _handle_friend_request_window
2025-07-28 23:35:42,820 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-28 23:35:42,821 - modules.friend_request_window - INFO -    4. D:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-28 23:35:42,821 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-28 23:35:42,821 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-28 23:35:42,821 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-28 23:35:42,822 - modules.friend_request_window - INFO -    📝 remark参数: '014325110083-秦智鹏-2025-07-29 07:35:42' (类型: <class 'str'>, 长度: 36)
2025-07-28 23:35:42,822 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-28 23:35:42,822 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110083-秦智鹏-2025-07-29 07:35:42'
2025-07-28 23:35:42,822 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-28 23:35:42,823 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-28 23:35:42,824 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-28 23:35:42,824 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-28 23:35:42,825 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-28 23:35:42,825 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-28 23:35:42,825 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-28 23:35:43,745 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-28 23:35:48,989 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-28 23:35:48,989 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-28 23:35:48,990 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-28 23:35:48,990 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-28 23:35:48,991 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '014325110079-陈浪-2025-07-29 07:33:06...' (前50字符)
2025-07-28 23:35:49,300 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 23:35:49,301 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 23:35:50,203 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 23:35:50,212 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 23:35:50,213 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-28 23:35:50,215 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-28 23:35:50,216 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-28 23:35:50,217 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-28 23:35:50,719 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-28 23:35:50,719 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-28 23:35:50,719 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-28 23:35:50,720 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-28 23:35:50,720 - modules.friend_request_window - INFO -    📝 内容: '014325110083-秦智鹏-2025-07-29 07:35:42'
2025-07-28 23:35:50,721 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-28 23:35:50,721 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110083-\xe7\xa7\xa6\xe6\x99\xba\xe9\xb9\x8f-2025-07-29 07:35:42'
2025-07-28 23:35:50,721 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-28 23:35:51,643 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-28 23:35:56,891 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-28 23:35:56,891 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-28 23:35:56,892 - modules.friend_request_window - INFO -    📝 原始文本: '014325110083-秦智鹏-2025-07-29 07:35:42'
2025-07-28 23:35:56,892 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-28 23:35:56,893 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '014325110079-陈浪-2025-07-29 07:33:06...' (前50字符)
2025-07-28 23:35:57,204 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 23:35:57,205 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 23:35:58,107 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 23:35:58,117 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 23:35:58,117 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110083-秦智鹏-2025-07-29 07:35:42'
2025-07-28 23:35:58,118 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-28 23:35:58,119 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110083-秦智鹏-2025-07-29 07:35:42'
2025-07-28 23:35:58,119 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-28 23:35:58,620 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110083-秦智鹏-2025-07-29 07:35:42'
2025-07-28 23:35:58,621 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-28 23:35:58,621 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-28 23:35:58,621 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-28 23:35:58,621 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-28 23:35:58,622 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-28 23:35:58,622 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-28 23:35:59,423 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-28 23:35:59,423 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-28 23:35:59,423 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-28 23:36:00,042 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:36:00,043 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-28 23:36:00,049 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-28 23:36:00,051 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-28 23:36:00,570 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:00,799 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:01,032 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:01,274 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:01,503 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:01,738 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:01,971 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:02,214 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:02,451 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:02,686 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:02,920 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:03,155 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:03,390 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:03,621 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:03,852 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:04,084 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:04,318 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:04,548 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:04,784 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:05,016 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:05,232 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-28 23:36:05,232 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-28 23:36:06,233 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 23:36:06,236 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-28 23:36:06,237 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-28 23:36:06,237 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-28 23:36:06,237 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-28 23:36:06,238 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 23:36:06,239 - modules.friend_request_window - INFO -    📝 备注信息: '014325110083-秦智鹏-2025-07-29 07:35:42'
2025-07-28 23:36:06,740 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-28 23:36:06,741 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:36:06,741 - modules.wechat_auto_add_simple - INFO - ✅ 18271296851 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-28 23:36:06,742 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 18271296851
2025-07-28 23:36:06,743 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:36:10,079 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 6/2968
2025-07-28 23:36:10,079 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18883645159 (周伟德)
2025-07-28 23:36:10,079 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:36:16,669 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18883645159
2025-07-28 23:36:16,670 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 23:36:16,670 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18883645159 执行添加朋友操作...
2025-07-28 23:36:16,671 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 23:36:16,671 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 23:36:16,672 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 23:36:16,675 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 23:36:16,686 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 23:36:16,695 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 23:36:16,695 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 23:36:16,695 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 23:36:16,696 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 23:36:16,696 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 23:36:16,696 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 23:36:16,697 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 23:36:16,701 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-28 23:36:16,706 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 23:36:16,712 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 23:36:16,715 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 23:36:16,718 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-28 23:36:16,726 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-28 23:36:17,229 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-28 23:36:17,231 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-28 23:36:17,294 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差38.86, 边缘比例0.0354
2025-07-28 23:36:17,302 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_233617.png
2025-07-28 23:36:17,305 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 23:36:17,308 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 23:36:17,312 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-28 23:36:17,314 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-28 23:36:17,316 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-28 23:36:17,321 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_233617.png
2025-07-28 23:36:17,327 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-28 23:36:17,330 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-28 23:36:17,332 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-28 23:36:17,335 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-28 23:36:17,337 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-28 23:36:17,343 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-28 23:36:17,347 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-28 23:36:17,350 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 23:36:17,362 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_233617.png
2025-07-28 23:36:17,364 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 23:36:17,367 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-28 23:36:17,372 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_233617.png
2025-07-28 23:36:17,404 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 23:36:17,409 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-28 23:36:17,413 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 23:36:17,414 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 23:36:17,717 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-28 23:36:18,491 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 23:36:18,493 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 23:36:18,502 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:36:18,502 - modules.wechat_auto_add_simple - INFO - ✅ 18883645159 添加朋友操作执行成功
2025-07-28 23:36:18,504 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:36:18,507 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 23:36:20,510 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-28 23:36:20,511 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-28 23:36:20,511 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-28 23:36:20,512 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 23:36:20,512 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 23:36:20,512 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 23:36:20,513 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 23:36:20,513 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 23:36:20,514 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-28 23:36:20,514 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18883645159
2025-07-28 23:36:20,515 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-28 23:36:20,515 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 23:36:20,516 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:695 in _handle_friend_request_window
2025-07-28 23:36:20,516 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-28 23:36:20,517 - modules.friend_request_window - INFO -    📱 phone: '18883645159'
2025-07-28 23:36:20,517 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-28 23:36:20,518 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-28 23:36:20,905 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-28 23:36:20,906 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-28 23:36:20,907 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-28 23:36:20,907 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 23:36:20,908 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18883645159
2025-07-28 23:36:20,909 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-28 23:36:20,909 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 23:36:20,910 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-28 23:36:20,910 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-28 23:36:20,910 - modules.friend_request_window - INFO -    📱 手机号码: 18883645159
2025-07-28 23:36:20,911 - modules.friend_request_window - INFO -    🆔 准考证: 014325110084
2025-07-28 23:36:20,911 - modules.friend_request_window - INFO -    👤 姓名: 周伟德
2025-07-28 23:36:20,912 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 23:36:20,912 - modules.friend_request_window - INFO -    📝 备注格式: '014325110084-周伟德-2025-07-29 07:36:20'
2025-07-28 23:36:20,913 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 23:36:20,913 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110084-周伟德-2025-07-29 07:36:20'
2025-07-28 23:36:20,915 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 23:36:20,918 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 9439922, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-28 23:36:20,921 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 9439922)
2025-07-28 23:36:20,921 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-28 23:36:20,923 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-28 23:36:20,924 - modules.friend_request_window - INFO - 🔄 激活窗口: 9439922
2025-07-28 23:36:21,628 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-28 23:36:21,629 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-28 23:36:21,630 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-28 23:36:21,630 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:499 in _analyze_search_result
2025-07-28 23:36:21,630 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-28 23:36:21,631 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 23:36:21,631 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-28 23:36:21,631 - modules.friend_request_window - INFO -    3. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:695 in _handle_friend_request_window
2025-07-28 23:36:21,631 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-28 23:36:21,631 - modules.friend_request_window - INFO -    4. D:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-28 23:36:21,632 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-28 23:36:21,632 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-28 23:36:21,632 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-28 23:36:21,633 - modules.friend_request_window - INFO -    📝 remark参数: '014325110084-周伟德-2025-07-29 07:36:20' (类型: <class 'str'>, 长度: 36)
2025-07-28 23:36:21,633 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-28 23:36:21,633 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110084-周伟德-2025-07-29 07:36:20'
2025-07-28 23:36:21,633 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-28 23:36:21,634 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-28 23:36:21,634 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-28 23:36:21,634 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-28 23:36:21,635 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-28 23:36:21,636 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-28 23:36:21,636 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-28 23:36:22,575 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-28 23:36:27,818 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-28 23:36:27,818 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-28 23:36:27,819 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-28 23:36:27,819 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-28 23:36:27,820 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '014325110079-陈浪-2025-07-29 07:33:06...' (前50字符)
2025-07-28 23:36:28,128 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 23:36:28,129 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 23:36:29,032 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 23:36:29,042 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 23:36:29,049 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-28 23:36:29,049 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-28 23:36:29,050 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-28 23:36:29,054 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-28 23:36:29,555 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-28 23:36:29,556 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-28 23:36:29,556 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-28 23:36:29,557 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-28 23:36:29,557 - modules.friend_request_window - INFO -    📝 内容: '014325110084-周伟德-2025-07-29 07:36:20'
2025-07-28 23:36:29,557 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-28 23:36:29,558 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110084-\xe5\x91\xa8\xe4\xbc\x9f\xe5\xbe\xb7-2025-07-29 07:36:20'
2025-07-28 23:36:29,558 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-28 23:36:30,475 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-28 23:36:35,720 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-28 23:36:35,721 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-28 23:36:35,721 - modules.friend_request_window - INFO -    📝 原始文本: '014325110084-周伟德-2025-07-29 07:36:20'
2025-07-28 23:36:35,721 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-28 23:36:35,722 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '014325110079-陈浪-2025-07-29 07:33:06...' (前50字符)
2025-07-28 23:36:36,031 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 23:36:36,031 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 23:36:36,933 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 23:36:36,941 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 23:36:36,942 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110084-周伟德-2025-07-29 07:36:20'
2025-07-28 23:36:36,943 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-28 23:36:36,944 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110084-周伟德-2025-07-29 07:36:20'
2025-07-28 23:36:36,945 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-28 23:36:37,446 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110084-周伟德-2025-07-29 07:36:20'
2025-07-28 23:36:37,446 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-28 23:36:37,446 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-28 23:36:37,447 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-28 23:36:37,447 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-28 23:36:37,447 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-28 23:36:37,448 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-28 23:36:38,249 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-28 23:36:38,249 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-28 23:36:38,249 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-28 23:36:38,856 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:36:38,857 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-28 23:36:38,857 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-28 23:36:38,858 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-28 23:36:39,375 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:39,606 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:39,837 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:40,070 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:40,300 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:40,531 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:40,762 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:40,996 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:41,245 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:41,478 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:41,710 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:41,940 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:42,173 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:42,405 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:42,637 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:42,868 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:43,100 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:43,331 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:43,560 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:43,790 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-28 23:36:44,008 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-28 23:36:44,008 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-28 23:36:45,009 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 23:36:45,012 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-28 23:36:45,012 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-28 23:36:45,012 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-28 23:36:45,013 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-28 23:36:45,013 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 23:36:45,013 - modules.friend_request_window - INFO -    📝 备注信息: '014325110084-周伟德-2025-07-29 07:36:20'
2025-07-28 23:36:45,514 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-28 23:36:45,515 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:36:45,515 - modules.wechat_auto_add_simple - INFO - ✅ 18883645159 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-28 23:36:45,515 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 18883645159
2025-07-28 23:36:45,516 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:36:48,864 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 7/2968
2025-07-28 23:36:48,864 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13217152603 (胡军)
2025-07-28 23:36:48,865 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:36:55,450 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13217152603
2025-07-28 23:36:55,450 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 23:36:55,451 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13217152603 执行添加朋友操作...
2025-07-28 23:36:55,451 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 23:36:55,451 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 23:36:55,452 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 23:36:55,454 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 23:36:55,458 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 23:36:55,464 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 23:36:55,465 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 23:36:55,466 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 23:36:55,466 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 23:36:55,467 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 23:36:55,467 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 23:36:55,467 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 23:36:55,474 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-28 23:36:55,478 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 23:36:55,481 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 23:36:55,483 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 23:36:55,486 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-28 23:36:55,493 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-28 23:36:55,997 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-28 23:36:56,000 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-28 23:36:56,091 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-07-28 23:36:56,093 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-07-28 23:36:56,101 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_233656.png
2025-07-28 23:36:56,104 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 23:36:56,109 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 23:36:56,111 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-28 23:36:56,116 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-28 23:36:56,117 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-28 23:36:56,125 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_233656.png
2025-07-28 23:36:56,128 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-28 23:36:56,130 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-28 23:36:56,132 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-28 23:36:56,134 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-28 23:36:56,137 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-28 23:36:56,142 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-28 23:36:56,143 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-28 23:36:56,146 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 23:36:56,156 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_233656.png
2025-07-28 23:36:56,159 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 23:36:56,161 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-28 23:36:56,165 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_233656.png
2025-07-28 23:36:56,192 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 23:36:56,196 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-28 23:36:56,200 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 23:36:56,203 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 23:36:56,510 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-28 23:36:57,321 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 23:36:57,324 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 23:36:57,326 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:36:57,326 - modules.wechat_auto_add_simple - INFO - ✅ 13217152603 添加朋友操作执行成功
2025-07-28 23:36:57,326 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:36:57,327 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 23:36:59,330 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-28 23:36:59,331 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-28 23:36:59,331 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-28 23:36:59,332 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 23:36:59,332 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 23:36:59,333 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 23:36:59,333 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 23:36:59,333 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 23:36:59,334 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-28 23:36:59,334 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13217152603
2025-07-28 23:36:59,335 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-28 23:36:59,335 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 23:36:59,337 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:695 in _handle_friend_request_window
2025-07-28 23:36:59,338 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-28 23:36:59,341 - modules.friend_request_window - INFO -    📱 phone: '13217152603'
2025-07-28 23:36:59,344 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-28 23:36:59,345 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-28 23:36:59,734 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-28 23:36:59,735 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-28 23:36:59,735 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-28 23:36:59,736 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 23:36:59,737 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13217152603
2025-07-28 23:36:59,737 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-28 23:36:59,738 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 23:36:59,738 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-28 23:36:59,738 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-28 23:36:59,739 - modules.friend_request_window - INFO -    📱 手机号码: 13217152603
2025-07-28 23:36:59,739 - modules.friend_request_window - INFO -    🆔 准考证: 014325110085
2025-07-28 23:36:59,739 - modules.friend_request_window - INFO -    👤 姓名: 胡军
2025-07-28 23:36:59,739 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 23:36:59,740 - modules.friend_request_window - INFO -    📝 备注格式: '014325110085-胡军-2025-07-29 07:36:59'
2025-07-28 23:36:59,741 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 23:36:59,741 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110085-胡军-2025-07-29 07:36:59'
2025-07-28 23:36:59,742 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 23:36:59,744 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 9046240, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-28 23:36:59,747 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 9046240)
2025-07-28 23:36:59,747 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-28 23:36:59,748 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-28 23:36:59,748 - modules.friend_request_window - INFO - 🔄 激活窗口: 9046240
2025-07-28 23:37:00,451 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-28 23:37:00,451 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-28 23:37:00,452 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-28 23:37:00,452 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:499 in _analyze_search_result
2025-07-28 23:37:00,453 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-28 23:37:00,453 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 23:37:00,453 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-28 23:37:00,453 - modules.friend_request_window - INFO -    3. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:695 in _handle_friend_request_window
2025-07-28 23:37:00,454 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-28 23:37:00,454 - modules.friend_request_window - INFO -    4. D:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-28 23:37:00,454 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-28 23:37:00,454 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-28 23:37:00,455 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-28 23:37:00,455 - modules.friend_request_window - INFO -    📝 remark参数: '014325110085-胡军-2025-07-29 07:36:59' (类型: <class 'str'>, 长度: 35)
2025-07-28 23:37:00,456 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-28 23:37:00,456 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110085-胡军-2025-07-29 07:36:59'
2025-07-28 23:37:00,456 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-28 23:37:00,457 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-28 23:37:00,457 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-28 23:37:00,458 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-28 23:37:00,459 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-28 23:37:00,459 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-28 23:37:00,459 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-28 23:37:01,406 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-28 23:37:06,652 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-28 23:37:06,652 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-28 23:37:06,653 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-28 23:37:06,653 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-28 23:37:06,654 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '014325110079-陈浪-2025-07-29 07:33:06...' (前50字符)
2025-07-28 23:37:06,965 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 23:37:06,965 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 23:37:07,868 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 23:37:07,876 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 23:37:07,877 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-28 23:37:07,877 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-28 23:37:07,878 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-28 23:37:07,878 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-28 23:37:08,379 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-28 23:37:08,379 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-28 23:37:08,380 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-28 23:37:08,380 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-28 23:37:08,380 - modules.friend_request_window - INFO -    📝 内容: '014325110085-胡军-2025-07-29 07:36:59'
2025-07-28 23:37:08,382 - modules.friend_request_window - INFO -    📏 内容长度: 35 字符
2025-07-28 23:37:08,382 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110085-\xe8\x83\xa1\xe5\x86\x9b-2025-07-29 07:36:59'
2025-07-28 23:37:08,382 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-28 23:37:09,288 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-28 23:37:14,535 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-28 23:37:14,536 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-28 23:37:14,536 - modules.friend_request_window - INFO -    📝 原始文本: '014325110085-胡军-2025-07-29 07:36:59'
2025-07-28 23:37:14,536 - modules.friend_request_window - INFO -    📏 文本长度: 35 字符
2025-07-28 23:37:14,537 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '014325110079-陈浪-2025-07-29 07:33:06...' (前50字符)
2025-07-28 23:37:14,847 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 23:37:14,847 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 23:37:15,749 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 23:37:15,759 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 23:37:15,759 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110085-胡军-2025-07-29 07:36:59'
2025-07-28 23:37:15,760 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-28 23:37:15,761 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110085-胡军-2025-07-29 07:36:59'
2025-07-28 23:37:15,761 - modules.friend_request_window - INFO -    � 内容长度: 35 字符
2025-07-28 23:37:16,263 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110085-胡军-2025-07-29 07:36:59'
2025-07-28 23:37:16,264 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-28 23:37:16,264 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-28 23:37:16,264 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-28 23:37:16,265 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-28 23:37:16,265 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-28 23:37:16,265 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-28 23:37:17,066 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-28 23:37:17,066 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-28 23:37:17,067 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-28 23:37:17,689 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:37:17,689 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-28 23:37:17,690 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-28 23:37:17,690 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-28 23:37:18,191 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-28 23:37:18,193 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-28 23:37:18,194 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-28 23:37:18,194 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-28 23:37:18,194 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-28 23:37:18,194 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-28 23:37:18,195 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-28 23:37:18,195 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-28 23:37:18,195 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-28 23:37:18,195 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-28 23:37:18,196 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-28 23:37:18,196 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-28 23:37:18,196 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-28 23:37:18,197 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-28 23:37:18,197 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-28 23:37:18,198 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-28 23:37:18,198 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-28 23:37:18,200 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-28 23:37:18,201 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-28 23:37:18,704 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-28 23:37:18,704 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-28 23:37:18,704 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-28 23:37:18,705 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-28 23:37:18,705 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-28 23:37:18,705 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-28 23:37:18,705 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-28 23:37:18,706 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-28 23:37:19,622 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-28 23:37:19,622 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-28 23:37:19,622 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-28 23:37:19,623 - modules.friend_request_window - INFO - 🔄 步骤2: 关闭添加朋友窗口...
2025-07-28 23:37:19,623 - modules.friend_request_window - INFO - 🎯 使用指定坐标 (1504, 13) 关闭添加朋友窗口...
2025-07-28 23:37:20,124 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui点击坐标 (1504, 13)...
2025-07-28 23:37:21,237 - modules.friend_request_window - INFO - ✅ pyautogui点击添加朋友窗口关闭按钮成功
2025-07-28 23:37:21,237 - modules.friend_request_window - INFO - 🔄 步骤3: 关闭当前微信窗口...
2025-07-28 23:37:21,238 - modules.friend_request_window - INFO - 🎯 使用指定坐标 (700, 16) 关闭当前微信窗口...
2025-07-28 23:37:21,739 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui点击坐标 (700, 16)...
2025-07-28 23:37:22,885 - modules.friend_request_window - INFO - ✅ pyautogui点击当前微信窗口关闭按钮成功
2025-07-28 23:37:22,945 - modules.friend_request_window - INFO - 🔄 关闭错误相关窗口...
2025-07-28 23:37:22,991 - modules.friend_request_window - INFO - ✅ 频率错误处理完成
2025-07-28 23:37:23,061 - modules.friend_request_window - INFO - 🔄 频率错误处理完成，需要重新开始流程
2025-07-28 23:37:23,129 - modules.friend_request_window - INFO - 🔄 已设置重新开始标志
2025-07-28 23:37:23,178 - modules.friend_request_window - INFO - ✅ 原有频率错误处理成功，已切换到微信2
2025-07-28 23:37:23,205 - modules.friend_request_window - WARNING - ⚠️ 检测到并处理了频率错误，已切换到微信2
2025-07-28 23:37:23,227 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-28 23:37:23,263 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-28 23:37:23,289 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 23:37:23,318 - modules.friend_request_window - INFO -    📝 备注信息: '014325110085-胡军-2025-07-29 07:36:59'
2025-07-28 23:37:23,887 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-28 23:37:23,925 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:37:23,950 - modules.wechat_auto_add_simple - INFO - ✅ 13217152603 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-28 23:37:24,010 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 13217152603
2025-07-28 23:37:24,065 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:37:30,737 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 8/2968
2025-07-28 23:37:30,743 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18986054610 (谭志萍)
2025-07-28 23:37:30,746 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:37:37,716 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18986054610
2025-07-28 23:37:37,717 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 23:37:37,717 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18986054610 执行添加朋友操作...
2025-07-28 23:37:37,717 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 23:37:37,718 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 23:37:37,719 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 23:37:37,721 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 23:37:37,725 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 23:37:37,728 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 23:37:37,728 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 23:37:37,729 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 23:37:37,731 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 23:37:37,735 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 23:37:37,736 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 23:37:37,738 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 23:37:37,747 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 23:37:37,758 - WeChatAutoAdd - DEBUG - 找到微信窗口: ● main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 23:37:37,777 - WeChatAutoAdd - INFO - 共找到 2 个微信窗口
2025-07-28 23:37:37,782 - WeChatAutoAdd - INFO - 找到微信主窗口: 微信
2025-07-28 23:37:38,292 - WeChatAutoAdd - INFO - 目标窗口: 微信
2025-07-28 23:37:38,295 - WeChatAutoAdd - INFO - 窗口位置: (0, 0), 尺寸: 726x650
2025-07-28 23:37:38,402 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差29.84, 边缘比例0.0396
2025-07-28 23:37:38,423 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_233738.png
2025-07-28 23:37:38,427 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 23:37:38,429 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 23:37:38,433 - WeChatAutoAdd - INFO - 截图尺寸: 726x650
2025-07-28 23:37:38,440 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=405-650 (高度:245)
2025-07-28 23:37:38,442 - WeChatAutoAdd - INFO - 特别关注区域: Y=466-526 (距底部154像素)
2025-07-28 23:37:38,451 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_233738.png
2025-07-28 23:37:38,457 - WeChatAutoAdd - INFO - 底部区域原始检测到 160 个轮廓
2025-07-28 23:37:38,460 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(75,642), 尺寸3x5, 长宽比0.60, 面积15
2025-07-28 23:37:38,462 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(83,641), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:37:38,465 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(78,640), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:37:38,472 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(218,639), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 23:37:38,475 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,639), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 23:37:38,479 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(210,639), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 23:37:38,481 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(106,638), 尺寸4x3, 长宽比1.33, 面积12
2025-07-28 23:37:38,487 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(97,638), 尺寸8x3, 长宽比2.67, 面积24
2025-07-28 23:37:38,492 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,638), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 23:37:38,496 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(91,637), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:37:38,498 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(83,636), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:37:38,505 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(247,635), 尺寸4x6, 长宽比0.67, 面积24
2025-07-28 23:37:38,508 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(242,631), 尺寸17x11, 长宽比1.55, 面积187
2025-07-28 23:37:38,511 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(233,631), 尺寸10x9, 长宽比1.11, 面积90
2025-07-28 23:37:38,515 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(231,631), 尺寸6x10, 长宽比0.60, 面积60
2025-07-28 23:37:38,524 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(80,631), 尺寸4x16, 长宽比0.25, 面积64
2025-07-28 23:37:38,527 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,630), 尺寸6x11, 长宽比0.55, 面积66
2025-07-28 23:37:38,531 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,630), 尺寸5x11, 长宽比0.45, 面积55
2025-07-28 23:37:38,541 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,630), 尺寸8x11, 长宽比0.73, 面积88
2025-07-28 23:37:38,545 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,630), 尺寸9x11, 长宽比0.82, 面积99
2025-07-28 23:37:38,549 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(185,629), 尺寸24x13, 长宽比1.85, 面积312
2025-07-28 23:37:38,566 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=84.3 (阈值:60)
2025-07-28 23:37:38,577 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,629), 尺寸15x13, 长宽比1.15, 面积195
2025-07-28 23:37:38,589 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=83.6 (阈值:60)
2025-07-28 23:37:38,592 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,629), 尺寸14x13, 长宽比1.08, 面积182
2025-07-28 23:37:38,606 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=78.1 (阈值:60)
2025-07-28 23:37:38,612 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,612), 尺寸17x1, 长宽比17.00, 面积17
2025-07-28 23:37:38,621 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,610), 尺寸18x1, 长宽比18.00, 面积18
2025-07-28 23:37:38,627 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,604), 尺寸18x3, 长宽比6.00, 面积54
2025-07-28 23:37:38,630 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,601), 尺寸18x1, 长宽比18.00, 面积18
2025-07-28 23:37:38,637 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,599), 尺寸17x1, 长宽比17.00, 面积17
2025-07-28 23:37:38,640 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(200,594), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 23:37:38,645 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(188,593), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 23:37:38,648 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,592), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 23:37:38,656 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(245,592), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 23:37:38,661 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(243,592), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:37:38,665 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(241,592), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 23:37:38,673 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(124,592), 尺寸4x4, 长宽比1.00, 面积16
2025-07-28 23:37:38,676 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,591), 尺寸4x3, 长宽比1.33, 面积12
2025-07-28 23:37:38,679 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,590), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 23:37:38,684 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(181,589), 尺寸1x5, 长宽比0.20, 面积5
2025-07-28 23:37:38,690 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(127,589), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:37:38,694 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(226,588), 尺寸4x6, 长宽比0.67, 面积24
2025-07-28 23:37:38,697 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(224,588), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 23:37:38,702 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,588), 尺寸3x1, 长宽比3.00, 面积3
2025-07-28 23:37:38,709 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(146,588), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 23:37:38,712 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(143,588), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:37:38,716 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(191,587), 尺寸6x8, 长宽比0.75, 面积48
2025-07-28 23:37:38,724 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(179,585), 尺寸11x9, 长宽比1.22, 面积99
2025-07-28 23:37:38,730 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(147,585), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 23:37:38,740 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(198,584), 尺寸27x11, 长宽比2.45, 面积297
2025-07-28 23:37:38,746 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(122,584), 尺寸45x12, 长宽比3.75, 面积540
2025-07-28 23:37:38,761 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,584), 尺寸4x11, 长宽比0.36, 面积44
2025-07-28 23:37:38,767 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,583), 尺寸11x13, 长宽比0.85, 面积143
2025-07-28 23:37:38,778 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(153,583), 尺寸8x11, 长宽比0.73, 面积88
2025-07-28 23:37:38,790 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(147,583), 尺寸7x3, 长宽比2.33, 面积21
2025-07-28 23:37:38,796 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(143,583), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 23:37:38,807 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,583), 尺寸13x11, 长宽比1.18, 面积143
2025-07-28 23:37:38,815 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(221,571), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 23:37:38,829 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(216,571), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 23:37:38,832 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,570), 尺寸7x4, 长宽比1.75, 面积28
2025-07-28 23:37:38,841 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(126,570), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 23:37:38,844 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(205,568), 尺寸2x5, 长宽比0.40, 面积10
2025-07-28 23:37:38,847 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(133,568), 尺寸2x5, 长宽比0.40, 面积10
2025-07-28 23:37:38,851 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(171,565), 尺寸4x4, 长宽比1.00, 面积16
2025-07-28 23:37:38,857 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(254,564), 尺寸4x8, 长宽比0.50, 面积32
2025-07-28 23:37:38,864 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(206,564), 尺寸8x10, 长宽比0.80, 面积80
2025-07-28 23:37:38,872 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,564), 尺寸6x10, 长宽比0.60, 面积60
2025-07-28 23:37:38,876 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(242,563), 尺寸11x11, 长宽比1.00, 面积121
2025-07-28 23:37:38,879 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(233,563), 尺寸10x9, 长宽比1.11, 面积90
2025-07-28 23:37:38,882 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(231,563), 尺寸6x10, 长宽比0.60, 面积60
2025-07-28 23:37:38,889 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(141,563), 尺寸10x11, 长宽比0.91, 面积110
2025-07-28 23:37:38,892 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(199,562), 尺寸6x11, 长宽比0.55, 面积66
2025-07-28 23:37:38,895 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(196,562), 尺寸5x6, 长宽比0.83, 面积30
2025-07-28 23:37:38,898 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(191,562), 尺寸7x11, 长宽比0.64, 面积77
2025-07-28 23:37:38,906 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(151,562), 尺寸28x11, 长宽比2.55, 面积308
2025-07-28 23:37:38,911 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=78.7 (阈值:60)
2025-07-28 23:37:38,914 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(120,561), 尺寸12x12, 长宽比1.00, 面积144
2025-07-28 23:37:38,923 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=78.6 (阈值:60)
2025-07-28 23:37:38,928 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,559), 尺寸38x38, 长宽比1.00, 面积1444
2025-07-28 23:37:38,932 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=71.2 (阈值:60)
2025-07-28 23:37:38,942 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,547), 尺寸16x22, 长宽比0.73, 面积352
2025-07-28 23:37:38,946 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(161,526), 尺寸4x1, 长宽比4.00, 面积4
2025-07-28 23:37:38,951 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,524), 尺寸4x2, 长宽比2.00, 面积8
2025-07-28 23:37:38,960 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,523), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 23:37:38,965 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,522), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 23:37:38,975 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(161,522), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 23:37:38,982 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,521), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 23:37:38,997 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(155,521), 尺寸5x7, 长宽比0.71, 面积35
2025-07-28 23:37:39,011 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(143,521), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:37:39,023 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(146,520), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 23:37:39,039 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(179,519), 尺寸10x7, 长宽比1.43, 面积70
2025-07-28 23:37:39,043 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(162,519), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 23:37:39,049 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(196,518), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 23:37:39,057 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,518), 尺寸1x5, 长宽比0.20, 面积5
2025-07-28 23:37:39,062 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,518), 尺寸4x2, 长宽比2.00, 面积8
2025-07-28 23:37:39,065 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(150,518), 尺寸1x9, 长宽比0.11, 面积9
2025-07-28 23:37:39,072 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(180,516), 尺寸10x2, 长宽比5.00, 面积20
2025-07-28 23:37:39,075 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(155,516), 尺寸29x12, 长宽比2.42, 面积348
2025-07-28 23:37:39,081 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=516 (距底部154像素区域)
2025-07-28 23:37:39,089 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 23:37:39,093 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,516), 尺寸4x13, 长宽比0.31, 面积52
2025-07-28 23:37:39,097 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(185,515), 尺寸42x13, 长宽比3.23, 面积546
2025-07-28 23:37:39,105 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=515 (距底部154像素区域)
2025-07-28 23:37:39,108 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 23:37:39,112 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(123,515), 尺寸26x14, 长宽比1.86, 面积364
2025-07-28 23:37:39,115 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=515 (距底部154像素区域)
2025-07-28 23:37:39,124 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 23:37:39,128 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,505), 尺寸8x1, 长宽比8.00, 面积8
2025-07-28 23:37:39,131 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,503), 尺寸9x1, 长宽比9.00, 面积9
2025-07-28 23:37:39,140 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,502), 尺寸8x4, 长宽比2.00, 面积32
2025-07-28 23:37:39,143 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(139,502), 尺寸6x2, 长宽比3.00, 面积12
2025-07-28 23:37:39,146 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,499), 尺寸8x3, 长宽比2.67, 面积24
2025-07-28 23:37:39,156 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(163,498), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:37:39,161 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(154,498), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:37:39,166 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,497), 尺寸1x4, 长宽比0.25, 面积4
2025-07-28 23:37:39,175 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(229,495), 尺寸29x11, 长宽比2.64, 面积319
2025-07-28 23:37:39,181 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=495 (距底部154像素区域)
2025-07-28 23:37:39,190 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 23:37:39,195 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(163,495), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 23:37:39,199 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,495), 尺寸10x10, 长宽比1.00, 面积100
2025-07-28 23:37:39,209 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=495 (距底部154像素区域)
2025-07-28 23:37:39,214 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 23:37:39,223 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(170,494), 尺寸5x11, 长宽比0.45, 面积55
2025-07-28 23:37:39,227 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(168,494), 尺寸1x7, 长宽比0.14, 面积7
2025-07-28 23:37:39,230 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(161,493), 尺寸6x13, 长宽比0.46, 面积78
2025-07-28 23:37:39,238 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,493), 尺寸10x1, 长宽比10.00, 面积10
2025-07-28 23:37:39,240 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,493), 尺寸41x14, 长宽比2.93, 面积574
2025-07-28 23:37:39,244 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=493 (距底部154像素区域)
2025-07-28 23:37:39,248 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 23:37:39,255 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,491), 尺寸37x37, 长宽比1.00, 面积1369
2025-07-28 23:37:39,259 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=491 (距底部154像素区域)
2025-07-28 23:37:39,262 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 23:37:39,266 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,456), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 23:37:39,273 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(247,456), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 23:37:39,276 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(244,456), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 23:37:39,279 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(203,456), 尺寸9x3, 长宽比3.00, 面积27
2025-07-28 23:37:39,285 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(208,454), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 23:37:39,290 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(203,454), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:37:39,295 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(175,454), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:37:39,297 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,454), 尺寸10x8, 长宽比1.25, 面积80
2025-07-28 23:37:39,305 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(201,453), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:37:39,309 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(158,453), 尺寸4x5, 长宽比0.80, 面积20
2025-07-28 23:37:39,314 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,452), 尺寸4x4, 长宽比1.00, 面积16
2025-07-28 23:37:39,321 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,452), 尺寸7x4, 长宽比1.75, 面积28
2025-07-28 23:37:39,325 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,452), 尺寸8x7, 长宽比1.14, 面积56
2025-07-28 23:37:39,328 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,452), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:37:39,332 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,452), 尺寸1x3, 长宽比0.33, 面积3
2025-07-28 23:37:39,338 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,452), 尺寸6x10, 长宽比0.60, 面积60
2025-07-28 23:37:39,345 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,451), 尺寸5x4, 长宽比1.25, 面积20
2025-07-28 23:37:39,356 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(159,451), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 23:37:39,363 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,451), 尺寸5x4, 长宽比1.25, 面积20
2025-07-28 23:37:39,371 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(132,451), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:37:39,378 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(123,451), 尺寸8x7, 长宽比1.14, 面积56
2025-07-28 23:37:39,387 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(171,450), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 23:37:39,390 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(230,449), 尺寸13x11, 长宽比1.18, 面积143
2025-07-28 23:37:39,395 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,449), 尺寸5x1, 长宽比5.00, 面积5
2025-07-28 23:37:39,405 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,449), 尺寸23x11, 长宽比2.09, 面积253
2025-07-28 23:37:39,409 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,448), 尺寸17x12, 长宽比1.42, 面积204
2025-07-28 23:37:39,421 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(173,448), 尺寸10x8, 长宽比1.25, 面积80
2025-07-28 23:37:39,424 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,447), 尺寸19x13, 长宽比1.46, 面积247
2025-07-28 23:37:39,429 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,447), 尺寸11x13, 长宽比0.85, 面积143
2025-07-28 23:37:39,437 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(159,447), 尺寸13x3, 长宽比4.33, 面积39
2025-07-28 23:37:39,439 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(218,435), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 23:37:39,443 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,435), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 23:37:39,446 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(152,435), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 23:37:39,455 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(253,431), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 23:37:39,458 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(186,429), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:37:39,461 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(229,427), 尺寸30x11, 长宽比2.73, 面积330
2025-07-28 23:37:39,465 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(186,427), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:37:39,472 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(188,426), 尺寸3x11, 长宽比0.27, 面积33
2025-07-28 23:37:39,475 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,426), 尺寸6x11, 长宽比0.55, 面积66
2025-07-28 23:37:39,479 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,426), 尺寸5x11, 长宽比0.45, 面积55
2025-07-28 23:37:39,486 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,426), 尺寸17x11, 长宽比1.55, 面积187
2025-07-28 23:37:39,490 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=87.1 (阈值:60)
2025-07-28 23:37:39,495 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(202,425), 尺寸9x13, 长宽比0.69, 面积117
2025-07-28 23:37:39,498 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,425), 尺寸7x13, 长宽比0.54, 面积91
2025-07-28 23:37:39,507 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(153,425), 尺寸15x13, 长宽比1.15, 面积195
2025-07-28 23:37:39,512 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=88.5 (阈值:60)
2025-07-28 23:37:39,515 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,425), 尺寸14x13, 长宽比1.08, 面积182
2025-07-28 23:37:39,523 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=78.1 (阈值:60)
2025-07-28 23:37:39,526 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,423), 尺寸38x38, 长宽比1.00, 面积1444
2025-07-28 23:37:39,530 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=71.2 (阈值:60)
2025-07-28 23:37:39,537 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,405), 尺寸726x245, 长宽比2.96, 面积177870
2025-07-28 23:37:39,541 - WeChatAutoAdd - INFO - 底部区域找到 22 个按钮候选
2025-07-28 23:37:39,545 - WeChatAutoAdd - INFO - 选择特殊位置按钮: Y=491 (距底部154像素)
2025-07-28 23:37:39,549 - WeChatAutoAdd - INFO - 在底部找到按钮: (90, 509), 尺寸: 37x37, 位置得分: 3.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-07-28 23:37:39,557 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 23:37:39,587 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_233739.png
2025-07-28 23:37:39,590 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 23:37:39,594 - WeChatAutoAdd - INFO - 开始验证按钮区域(90, 509)是否包含'添加到通讯录'文字
2025-07-28 23:37:39,601 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_233739.png
2025-07-28 23:37:39,637 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 23:37:39,641 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0508, 平均亮度=227.0, 亮度标准差=45.1
2025-07-28 23:37:39,643 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 23:37:39,646 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 23:37:39,959 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(90, 509) -> 屏幕坐标(90, 509)
2025-07-28 23:37:40,802 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 23:37:40,805 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 23:37:40,810 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:37:40,810 - modules.wechat_auto_add_simple - INFO - ✅ 18986054610 添加朋友操作执行成功
2025-07-28 23:37:40,811 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:37:40,811 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 23:37:42,813 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:37:42,814 - modules.wechat_auto_add_simple - INFO - ℹ️ 18986054610 未发现申请添加朋友窗口，可能直接添加成功
2025-07-28 23:37:42,814 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 18986054610
2025-07-28 23:37:42,815 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:37:46,149 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 9/2968
2025-07-28 23:37:46,149 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 17704006423 (刘林蓓)
2025-07-28 23:37:46,149 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
