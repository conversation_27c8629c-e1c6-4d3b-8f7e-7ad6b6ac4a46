2025-07-28 14:49:53,280 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 14:49:53,281 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 14:49:53,281 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-28 14:49:53,282 - WeChatAutoAdd - INFO - ✅ 使用传入的窗口管理器实例
2025-07-28 14:49:53,283 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 14:49:53,284 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-28 14:49:53,284 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 14:49:53,285 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 14:49:53,286 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 14:49:53,287 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 14:49:53,287 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 14:49:53,288 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 14:49:53,290 - step_executor - INFO - ✅ 使用传入的窗口管理器实例
2025-07-28 14:49:53,293 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250728_144953.log
2025-07-28 14:49:53,293 - modules.wechat_auto_add_simple - INFO - ✅ 使用传入的窗口管理器实例
2025-07-28 14:49:53,293 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-28 14:49:53,294 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-28 14:49:53,294 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-28 14:49:53,295 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-28 14:49:53,295 - __main__ - INFO - 📅 当前北京时间: 2025-07-28 14:49:53
2025-07-28 14:49:53,296 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-28 14:49:53,296 - __main__ - INFO - 📅 启动时间: 2025-07-28 14:49:53
2025-07-28 14:49:53,296 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-28 14:49:53,297 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 14:49:53,850 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 14:49:53,850 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 14:49:54,406 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 14:49:54,407 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 14:49:54,407 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-28 14:49:54,408 - __main__ - INFO - ✅ 找到 2 个微信窗口
2025-07-28 14:49:54,408 - __main__ - INFO -   窗口 1: 微信 (句柄: 197994)
2025-07-28 14:49:54,408 - __main__ - INFO -   窗口 2: 微信 (句柄: 525668)
2025-07-28 14:49:54,408 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-28 14:49:55,271 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-28 14:49:55,272 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-28 14:49:55,272 - __main__ - INFO - 📋 待处理联系人数: 3003
2025-07-28 14:49:55,272 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-28 14:49:55,272 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 3003
2025-07-28 14:49:55,273 - __main__ - INFO - 
============================================================
2025-07-28 14:49:55,273 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口
2025-07-28 14:49:55,273 - __main__ - INFO - ============================================================
2025-07-28 14:49:55,274 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-28 14:49:55,274 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-28 14:49:55,274 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-28 14:49:55,275 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-28 14:49:55,275 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 14:49:55,593 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 14:49:55,594 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 14:49:55,594 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 14:49:55,594 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 14:49:55,595 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 14:49:55,595 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 14:49:55,595 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 14:49:55,596 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 14:49:55,596 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 14:49:55,596 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 14:49:55,799 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 14:49:55,799 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 14:49:55,799 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-28 14:49:55,800 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 14:49:56,104 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 14:49:56,104 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 14:49:56,104 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 14:49:56,105 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 14:49:56,105 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 14:49:56,105 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 14:49:56,106 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 14:49:56,106 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 14:49:56,107 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 14:49:56,107 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 14:49:56,308 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 14:49:56,309 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 14:49:56,310 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-28 14:49:56,611 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 14:49:56,612 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 14:49:56,612 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 14:49:56,612 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 14:49:56,613 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 14:49:56,613 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 14:49:56,613 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-28 14:49:57,614 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-28 14:49:57,614 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-28 14:49:57,615 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-28 14:49:57,615 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-28 14:49:57,616 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-28 14:49:57,616 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-28 14:49:57,617 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-28 14:49:57,618 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-28 14:49:57,819 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-28 14:49:57,820 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-28 14:50:00,204 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-28 14:50:00,205 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-28 14:50:00,205 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.7 秒...
2025-07-28 14:50:01,883 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-28 14:50:02,083 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-28 14:50:02,084 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-28 14:50:04,453 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-28 14:50:04,453 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-28 14:50:04,453 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.9 秒...
2025-07-28 14:50:07,325 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-28 14:50:07,526 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-28 14:50:07,527 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-28 14:50:09,904 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-28 14:50:09,904 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-28 14:50:09,905 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 3.0 秒...
2025-07-28 14:50:12,893 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-28 14:50:13,094 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-28 14:50:13,095 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-28 14:50:15,463 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-28 14:50:15,463 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-28 14:50:15,463 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.7 秒...
2025-07-28 14:50:17,192 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-28 14:50:17,394 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-28 14:50:17,394 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-28 14:50:19,770 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-28 14:50:19,771 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-28 14:50:19,772 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-28 14:50:19,772 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-28 14:50:19,773 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 14:50:19,774 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 14:50:19,775 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 14:50:19,776 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 3016948, 进程: Weixin.exe)
2025-07-28 14:50:19,777 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 14:50:19,779 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 14:50:19,786 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-28 14:50:19,787 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 3016948)
2025-07-28 14:50:19,788 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 3016948) - 增强版
2025-07-28 14:50:20,091 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 14:50:20,092 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 14:50:20,092 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-28 14:50:20,093 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-28 14:50:20,093 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-28 14:50:20,093 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-28 14:50:20,299 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-28 14:50:20,299 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-28 14:50:20,503 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 14:50:20,503 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 14:50:20,503 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-28 14:50:20,504 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-28 14:50:20,504 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-28 14:50:20,504 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-28 14:50:20,504 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-28 14:50:21,505 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-28 14:50:21,505 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 14:50:21,507 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 14:50:21,507 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 14:50:21,509 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 3016948, 进程: Weixin.exe)
2025-07-28 14:50:21,509 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 14:50:21,510 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 14:50:21,512 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-28 14:50:21,513 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-28 14:50:21,513 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-28 14:50:21,514 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-28 14:50:21,514 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-28 14:50:21,515 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 14:50:21,823 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 14:50:21,823 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 14:50:21,824 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 14:50:21,824 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 14:50:21,824 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 14:50:21,825 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 14:50:21,825 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 14:50:21,825 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 14:50:21,825 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 14:50:21,826 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 14:50:22,027 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 14:50:22,028 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 14:50:22,029 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-28 14:50:22,330 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 14:50:22,330 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-28 14:50:23,330 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-28 14:50:23,331 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-28 14:50:23,331 - __main__ - INFO - 🔧 步骤3前强化窗口管理：确保添加好友窗口在前台
2025-07-28 14:50:23,331 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 14:50:23,635 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 14:50:23,635 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 14:50:23,636 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 14:50:23,636 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 14:50:23,636 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 14:50:23,637 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 14:50:23,637 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 14:50:23,637 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 14:50:23,638 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 14:50:23,638 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 14:50:23,839 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 14:50:23,840 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 14:50:23,840 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-28 14:50:23,840 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 14:50:24,144 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 14:50:24,144 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 14:50:24,145 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 14:50:24,145 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 14:50:24,145 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 14:50:24,146 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 14:50:24,146 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 14:50:24,146 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 14:50:24,147 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 14:50:24,147 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 14:50:24,349 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 14:50:24,349 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 14:50:24,350 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-28 14:50:24,651 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 14:50:24,652 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 14:50:24,652 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 14:50:24,652 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 14:50:24,653 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 14:50:24,653 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 14:50:24,653 - __main__ - INFO - ✅ 步骤3前窗口管理策略应用成功
2025-07-28 14:50:26,654 - __main__ - INFO - 📊 当前窗口分配到 5 个联系人
2025-07-28 14:50:26,654 - step_executor - INFO - 📞 开始为窗口执行简单添加好友流程
2025-07-28 14:50:26,654 - step_executor - INFO - 🖥️ 窗口信息: 微信 (句柄: 197994)
2025-07-28 14:50:26,655 - step_executor - INFO - 📊 待处理联系人数: 5
2025-07-28 14:50:26,655 - step_executor - INFO - 🔧 强化窗口管理：激活+置顶+应用管理策略
2025-07-28 14:50:26,656 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 14:50:26,959 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 14:50:26,960 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 14:50:26,960 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 14:50:26,960 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 14:50:26,960 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 14:50:26,961 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 14:50:26,961 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 14:50:26,961 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 14:50:26,962 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 14:50:26,962 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 14:50:27,165 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 14:50:27,165 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 14:50:27,166 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-28 14:50:27,166 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 14:50:27,470 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 14:50:27,470 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 14:50:27,470 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 14:50:27,471 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 14:50:27,471 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 14:50:27,471 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 14:50:27,472 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 14:50:27,472 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 14:50:27,472 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 14:50:27,473 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 14:50:27,674 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 14:50:27,675 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 14:50:27,677 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-28 14:50:27,977 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 14:50:27,978 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 14:50:27,978 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 14:50:27,979 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 14:50:27,979 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 14:50:27,979 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 14:50:27,979 - step_executor - INFO - ✅ 窗口管理策略应用成功
2025-07-28 14:50:29,480 - step_executor - INFO - 
📍 处理联系人 1/5
2025-07-28 14:50:29,480 - step_executor - INFO - 🔄 重新激活窗口确保操作在正确窗口中执行
2025-07-28 14:50:29,481 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-28 14:50:29,481 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 14:50:29,785 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 14:50:29,786 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 14:50:29,786 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 14:50:29,787 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 14:50:29,787 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 14:50:29,787 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 14:50:29,788 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 14:50:29,788 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 14:50:29,788 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 14:50:29,788 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 14:50:29,991 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 14:50:29,991 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 14:50:29,992 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-28 14:50:30,293 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 14:50:30,794 - step_executor - INFO - 📞 手机号: 13046777941
2025-07-28 14:50:30,794 - step_executor - INFO - 👤 姓名: 徐金超
2025-07-28 14:50:30,795 - step_executor - ERROR - ❌ 处理联系人 13046777941 异常: 'DataManager' object has no attribute 'get_contact_status'
2025-07-28 14:50:30,797 - step_executor - ERROR - Traceback (most recent call last):
  File "d:\程序-测试\微信7.28 - 副本 (2)\step_executor.py", line 109, in execute_simple_add_friend_for_window
    current_status = self.data_manager.get_contact_status(phone)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DataManager' object has no attribute 'get_contact_status'

2025-07-28 14:50:30,797 - step_executor - ERROR - ❌ 简单添加好友流程执行异常: 'DataManager' object has no attribute 'update_contact_status'
2025-07-28 14:50:30,799 - step_executor - ERROR - Traceback (most recent call last):
  File "d:\程序-测试\微信7.28 - 副本 (2)\step_executor.py", line 109, in execute_simple_add_friend_for_window
    current_status = self.data_manager.get_contact_status(phone)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DataManager' object has no attribute 'get_contact_status'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "d:\程序-测试\微信7.28 - 副本 (2)\step_executor.py", line 176, in execute_simple_add_friend_for_window
    self.data_manager.update_contact_status(
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DataManager' object has no attribute 'update_contact_status'. Did you mean: 'update_phone_status'?

2025-07-28 14:50:30,799 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-28 14:50:30,799 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 14:50:31,103 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 14:50:31,103 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 14:50:31,103 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 14:50:31,104 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 14:50:31,104 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 14:50:31,104 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 14:50:31,105 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 14:50:31,105 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 14:50:31,105 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 14:50:31,106 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 14:50:31,307 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 14:50:31,308 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 14:50:31,309 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-28 14:50:31,610 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 14:50:31,610 - __main__ - INFO - ✅ 步骤3：简单添加好友完成 - 无联系人处理
2025-07-28 14:50:32,611 - __main__ - INFO - 📍 当前执行步骤: IMAGE_RECOGNITION (步骤 4)
2025-07-28 14:50:32,611 - __main__ - INFO - 🖼️ 步骤4：图像识别添加 - 窗口 1
2025-07-28 14:50:32,612 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 14:50:32,615 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 14:50:32,617 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-28 14:50:32,618 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 14:50:32,620 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 14:50:32,621 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-28 14:50:32,622 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-28 14:50:33,123 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-28 14:50:33,124 - WeChatAutoAdd - INFO - 窗口位置: (0, 0), 尺寸: 328x454
2025-07-28 14:50:33,196 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差42.65, 边缘比例0.0862
2025-07-28 14:50:33,206 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_145033.png
2025-07-28 14:50:33,207 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 14:50:33,208 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 14:50:33,209 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-28 14:50:33,210 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-28 14:50:33,210 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-28 14:50:33,216 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_145033.png
2025-07-28 14:50:33,217 - WeChatAutoAdd - INFO - 底部区域原始检测到 286 个轮廓
2025-07-28 14:50:33,218 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(187,453), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,219 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(161,453), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,220 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,453), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,223 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,453), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:50:33,225 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(105,453), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,225 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(87,453), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,226 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(76,453), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,226 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(229,452), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 14:50:33,227 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(225,451), 尺寸1x3, 长宽比0.33, 面积3
2025-07-28 14:50:33,228 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,451), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 14:50:33,228 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(127,451), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,230 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(122,451), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,231 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(234,450), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:50:33,233 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(148,450), 尺寸6x4, 长宽比1.50, 面积24
2025-07-28 14:50:33,234 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(153,449), 尺寸4x5, 长宽比0.80, 面积20
2025-07-28 14:50:33,235 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(143,449), 尺寸5x5, 长宽比1.00, 面积25
2025-07-28 14:50:33,235 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(101,449), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,236 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(83,449), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 14:50:33,237 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(81,449), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,242 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(75,449), 尺寸11x5, 长宽比2.20, 面积55
2025-07-28 14:50:33,243 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(232,448), 尺寸7x6, 长宽比1.17, 面积42
2025-07-28 14:50:33,244 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(179,448), 尺寸3x6, 长宽比0.50, 面积18
2025-07-28 14:50:33,244 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,448), 尺寸1x3, 长宽比0.33, 面积3
2025-07-28 14:50:33,245 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(132,448), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:50:33,245 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,448), 尺寸14x6, 长宽比2.33, 面积84
2025-07-28 14:50:33,246 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(78,448), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:50:33,247 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(204,447), 尺寸27x7, 长宽比3.86, 面积189
2025-07-28 14:50:33,247 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(205,447), 尺寸3x1, 长宽比3.00, 面积3
2025-07-28 14:50:33,248 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(182,447), 尺寸22x7, 长宽比3.14, 面积154
2025-07-28 14:50:33,250 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(155,447), 尺寸23x7, 长宽比3.29, 面积161
2025-07-28 14:50:33,253 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(147,447), 尺寸7x1, 长宽比7.00, 面积7
2025-07-28 14:50:33,255 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(143,447), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 14:50:33,256 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(137,447), 尺寸3x7, 长宽比0.43, 面积21
2025-07-28 14:50:33,256 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(96,447), 尺寸11x7, 长宽比1.57, 面积77
2025-07-28 14:50:33,257 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(76,447), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,258 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(103,446), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,261 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(80,445), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 14:50:33,262 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(101,444), 尺寸1x4, 长宽比0.25, 面积4
2025-07-28 14:50:33,263 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(75,444), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,267 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(105,440), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:50:33,271 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(97,440), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,271 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(75,439), 尺寸5x6, 长宽比0.83, 面积30
2025-07-28 14:50:33,272 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(105,438), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:50:33,273 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(97,438), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,273 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(83,438), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:50:33,274 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(80,438), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:50:33,274 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(76,437), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,277 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(241,436), 尺寸3x1, 长宽比3.00, 面积3
2025-07-28 14:50:33,278 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(103,436), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,278 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(99,436), 尺寸8x10, 长宽比0.80, 面积80
2025-07-28 14:50:33,279 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(98,436), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,279 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(96,436), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,281 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(238,435), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:50:33,284 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(218,435), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:50:33,289 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,435), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 14:50:33,293 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(210,435), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:50:33,297 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(152,435), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 14:50:33,298 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(88,435), 尺寸3x1, 长宽比3.00, 面积3
2025-07-28 14:50:33,298 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(103,434), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,300 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(100,433), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,301 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(96,433), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:50:33,304 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(83,433), 尺寸17x21, 长宽比0.81, 面积357
2025-07-28 14:50:33,305 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(244,432), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 14:50:33,305 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(240,432), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,306 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(76,432), 尺寸5x5, 长宽比1.00, 面积25
2025-07-28 14:50:33,306 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(101,431), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,307 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(91,431), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,307 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(241,430), 尺寸2x5, 长宽比0.40, 面积10
2025-07-28 14:50:33,308 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,430), 尺寸3x5, 长宽比0.60, 面积15
2025-07-28 14:50:33,308 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(103,430), 尺寸4x4, 长宽比1.00, 面积16
2025-07-28 14:50:33,309 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(98,430), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,310 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(83,430), 尺寸4x3, 长宽比1.33, 面积12
2025-07-28 14:50:33,311 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(78,430), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:50:33,312 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(245,429), 尺寸6x8, 长宽比0.75, 面积48
2025-07-28 14:50:33,312 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(91,429), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,313 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(89,429), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,314 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(103,428), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,314 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(95,428), 尺寸2x4, 长宽比0.50, 面积8
2025-07-28 14:50:33,316 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(85,428), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,317 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(80,428), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 14:50:33,318 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(78,428), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,321 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,427), 尺寸12x10, 长宽比1.20, 面积120
2025-07-28 14:50:33,323 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(233,427), 尺寸14x10, 长宽比1.40, 面积140
2025-07-28 14:50:33,323 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,427), 尺寸7x11, 长宽比0.64, 面积77
2025-07-28 14:50:33,324 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(106,427), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:50:33,324 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(93,427), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,325 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,426), 尺寸5x11, 长宽比0.45, 面积55
2025-07-28 14:50:33,325 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,426), 尺寸17x11, 长宽比1.55, 面积187
2025-07-28 14:50:33,326 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=87.1 (阈值:60)
2025-07-28 14:50:33,327 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(104,426), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,328 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(101,426), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,329 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(98,426), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 14:50:33,329 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(95,426), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,330 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(91,426), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,330 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(88,426), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 14:50:33,331 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,426), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,331 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(83,426), 尺寸1x3, 长宽比0.33, 面积3
2025-07-28 14:50:33,336 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(81,426), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,337 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(77,426), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,338 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(185,425), 尺寸24x13, 长宽比1.85, 面积312
2025-07-28 14:50:33,338 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=82.9 (阈值:60)
2025-07-28 14:50:33,339 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,425), 尺寸7x9, 长宽比0.78, 面积63
2025-07-28 14:50:33,340 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(153,425), 尺寸15x13, 长宽比1.15, 面积195
2025-07-28 14:50:33,341 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=88.5 (阈值:60)
2025-07-28 14:50:33,342 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,425), 尺寸14x13, 长宽比1.08, 面积182
2025-07-28 14:50:33,343 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=78.1 (阈值:60)
2025-07-28 14:50:33,343 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(73,423), 尺寸37x31, 长宽比1.19, 面积1147
2025-07-28 14:50:33,344 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(99,391), 尺寸6x2, 长宽比3.00, 面积12
2025-07-28 14:50:33,345 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(231,389), 尺寸8x3, 长宽比2.67, 面积24
2025-07-28 14:50:33,346 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(224,389), 尺寸4x1, 长宽比4.00, 面积4
2025-07-28 14:50:33,346 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,389), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,347 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(239,388), 尺寸9x2, 长宽比4.50, 面积18
2025-07-28 14:50:33,347 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(224,388), 尺寸9x4, 长宽比2.25, 面积36
2025-07-28 14:50:33,348 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(124,388), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:50:33,350 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(101,388), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 14:50:33,351 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,387), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:50:33,352 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(151,387), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,357 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(230,386), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,358 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(225,386), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 14:50:33,358 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(85,386), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:50:33,359 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(232,385), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 14:50:33,359 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(175,385), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:50:33,360 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,384), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 14:50:33,360 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(170,384), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:50:33,361 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(90,384), 尺寸8x8, 长宽比1.00, 面积64
2025-07-28 14:50:33,361 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(181,382), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 14:50:33,362 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(234,381), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,363 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(226,381), 尺寸4x5, 长宽比0.80, 面积20
2025-07-28 14:50:33,364 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(221,381), 尺寸4x7, 长宽比0.57, 面积28
2025-07-28 14:50:33,365 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(180,381), 尺寸22x11, 长宽比2.00, 面积242
2025-07-28 14:50:33,367 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,381), 尺寸11x11, 长宽比1.00, 面积121
2025-07-28 14:50:33,370 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,381), 尺寸8x3, 长宽比2.67, 面积24
2025-07-28 14:50:33,371 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(91,381), 尺寸3x1, 长宽比3.00, 面积3
2025-07-28 14:50:33,371 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,379), 尺寸11x8, 长宽比1.38, 面积88
2025-07-28 14:50:33,372 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(203,379), 尺寸22x13, 长宽比1.69, 面积286
2025-07-28 14:50:33,373 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(181,379), 尺寸10x6, 长宽比1.67, 面积60
2025-07-28 14:50:33,373 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,379), 尺寸33x13, 长宽比2.54, 面积429
2025-07-28 14:50:33,375 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,379), 尺寸29x13, 长宽比2.23, 面积377
2025-07-28 14:50:33,376 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(91,379), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:50:33,376 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(91,377), 尺寸3x1, 长宽比3.00, 面积3
2025-07-28 14:50:33,377 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(99,376), 尺寸2x5, 长宽比0.40, 面积10
2025-07-28 14:50:33,377 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(90,374), 尺寸4x2, 长宽比2.00, 面积8
2025-07-28 14:50:33,378 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(19,373), 尺寸22x22, 长宽比1.00, 面积484
2025-07-28 14:50:33,379 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(92,371), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,379 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(84,370), 尺寸2x12, 长宽比0.17, 面积24
2025-07-28 14:50:33,380 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(218,367), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:50:33,381 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,367), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 14:50:33,384 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(210,367), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:50:33,387 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(152,367), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 14:50:33,387 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(99,367), 尺寸1x3, 长宽比0.33, 面积3
2025-07-28 14:50:33,388 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(95,367), 尺寸12x24, 长宽比0.50, 面积288
2025-07-28 14:50:33,388 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(85,367), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 14:50:33,389 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(252,365), 尺寸1x3, 长宽比0.33, 面积3
2025-07-28 14:50:33,389 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(96,363), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 14:50:33,390 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,362), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:50:33,390 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,359), 尺寸11x10, 长宽比1.10, 面积110
2025-07-28 14:50:33,391 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(233,359), 尺寸18x10, 长宽比1.80, 面积180
2025-07-28 14:50:33,392 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,359), 尺寸7x11, 长宽比0.64, 面积77
2025-07-28 14:50:33,393 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,358), 尺寸5x11, 长宽比0.45, 面积55
2025-07-28 14:50:33,394 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,358), 尺寸17x11, 长宽比1.55, 面积187
2025-07-28 14:50:33,396 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=87.1 (阈值:60)
2025-07-28 14:50:33,396 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(185,357), 尺寸24x13, 长宽比1.85, 面积312
2025-07-28 14:50:33,397 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=82.9 (阈值:60)
2025-07-28 14:50:33,400 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,357), 尺寸7x9, 长宽比0.78, 面积63
2025-07-28 14:50:33,401 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(153,357), 尺寸15x13, 长宽比1.15, 面积195
2025-07-28 14:50:33,404 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=88.5 (阈值:60)
2025-07-28 14:50:33,405 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,357), 尺寸14x13, 长宽比1.08, 面积182
2025-07-28 14:50:33,406 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=78.1 (阈值:60)
2025-07-28 14:50:33,406 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,355), 尺寸38x37, 长宽比1.03, 面积1406
2025-07-28 14:50:33,407 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=84.3 (阈值:60)
2025-07-28 14:50:33,407 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(29,338), 尺寸6x8, 长宽比0.75, 面积48
2025-07-28 14:50:33,408 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(31,337), 尺寸6x9, 长宽比0.67, 面积54
2025-07-28 14:50:33,409 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,337), 尺寸5x9, 长宽比0.56, 面积45
2025-07-28 14:50:33,410 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(30,332), 尺寸11x3, 长宽比3.67, 面积33
2025-07-28 14:50:33,411 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(19,332), 尺寸6x3, 长宽比2.00, 面积18
2025-07-28 14:50:33,411 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(27,325), 尺寸4x7, 长宽比0.57, 面积28
2025-07-28 14:50:33,412 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(313,323), 尺寸2x131, 长宽比0.02, 面积262
2025-07-28 14:50:33,412 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(176,323), 尺寸4x1, 长宽比4.00, 面积4
2025-07-28 14:50:33,413 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(153,323), 尺寸4x1, 长宽比4.00, 面积4
2025-07-28 14:50:33,413 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(168,321), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,414 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,321), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,416 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,321), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,417 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,320), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:50:33,419 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(247,320), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 14:50:33,420 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(244,320), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 14:50:33,421 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(227,320), 尺寸9x3, 长宽比3.00, 面积27
2025-07-28 14:50:33,421 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(236,319), 尺寸7x5, 长宽比1.40, 面积35
2025-07-28 14:50:33,422 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(89,319), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,422 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(232,318), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 14:50:33,423 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(227,318), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,423 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(199,318), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,423 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(225,317), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,424 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(176,317), 尺寸7x9, 长宽比0.78, 面积63
2025-07-28 14:50:33,424 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,317), 尺寸4x4, 长宽比1.00, 面积16
2025-07-28 14:50:33,426 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(146,317), 尺寸4x4, 长宽比1.00, 面积16
2025-07-28 14:50:33,427 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(173,316), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,428 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(150,316), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,428 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(242,315), 尺寸1x4, 长宽比0.25, 面积4
2025-07-28 14:50:33,429 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,315), 尺寸4x4, 长宽比1.00, 面积16
2025-07-28 14:50:33,429 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(168,315), 尺寸17x11, 长宽比1.55, 面积187
2025-07-28 14:50:33,430 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=315 (距底部154像素区域)
2025-07-28 14:50:33,432 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 14:50:33,434 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(168,315), 尺寸4x2, 长宽比2.00, 面积8
2025-07-28 14:50:33,437 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,315), 尺寸22x11, 长宽比2.00, 面积242
2025-07-28 14:50:33,438 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=315 (距底部154像素区域)
2025-07-28 14:50:33,438 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 14:50:33,439 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,315), 尺寸4x2, 长宽比2.00, 面积8
2025-07-28 14:50:33,440 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(126,315), 尺寸18x11, 长宽比1.64, 面积198
2025-07-28 14:50:33,440 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=315 (距底部154像素区域)
2025-07-28 14:50:33,442 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 14:50:33,442 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(195,314), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:50:33,443 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(236,313), 尺寸5x1, 长宽比5.00, 面积5
2025-07-28 14:50:33,444 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,313), 尺寸24x11, 长宽比2.18, 面积264
2025-07-28 14:50:33,444 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=313 (距底部154像素区域)
2025-07-28 14:50:33,445 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 14:50:33,445 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(218,312), 尺寸17x12, 长宽比1.42, 面积204
2025-07-28 14:50:33,446 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=312 (距底部154像素区域)
2025-07-28 14:50:33,447 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 14:50:33,448 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(197,312), 尺寸10x8, 长宽比1.25, 面积80
2025-07-28 14:50:33,449 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=312 (距底部154像素区域)
2025-07-28 14:50:33,450 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 14:50:33,451 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,312), 尺寸10x10, 长宽比1.00, 面积100
2025-07-28 14:50:33,452 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=312 (距底部154像素区域)
2025-07-28 14:50:33,454 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 14:50:33,457 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(207,311), 尺寸11x13, 长宽比0.85, 面积143
2025-07-28 14:50:33,459 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,311), 尺寸13x3, 长宽比4.33, 面积39
2025-07-28 14:50:33,459 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(93,311), 尺寸3x7, 长宽比0.43, 面积21
2025-07-28 14:50:33,460 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(96,308), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 14:50:33,461 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(93,308), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:50:33,461 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(322,303), 尺寸6x17, 长宽比0.35, 面积102
2025-07-28 14:50:33,461 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(96,302), 尺寸6x2, 长宽比3.00, 面积12
2025-07-28 14:50:33,462 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(218,299), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:50:33,462 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,299), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 14:50:33,463 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(210,299), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:50:33,465 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(98,292), 尺寸2x6, 长宽比0.33, 面积12
2025-07-28 14:50:33,466 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(96,292), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:50:33,467 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(233,291), 尺寸26x10, 长宽比2.60, 面积260
2025-07-28 14:50:33,470 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=291 (距底部154像素区域)
2025-07-28 14:50:33,471 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 14:50:33,472 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,291), 尺寸7x11, 长宽比0.64, 面积77
2025-07-28 14:50:33,473 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(136,291), 尺寸1x4, 长宽比0.25, 面积4
2025-07-28 14:50:33,475 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,290), 尺寸5x11, 长宽比0.45, 面积55
2025-07-28 14:50:33,475 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,290), 尺寸5x11, 长宽比0.45, 面积55
2025-07-28 14:50:33,476 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(99,290), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:50:33,477 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(94,290), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,477 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(202,289), 尺寸7x13, 长宽比0.54, 面积91
2025-07-28 14:50:33,478 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(185,289), 尺寸8x13, 长宽比0.62, 面积104
2025-07-28 14:50:33,478 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,289), 尺寸7x9, 长宽比0.78, 面积63
2025-07-28 14:50:33,479 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(153,289), 尺寸15x13, 长宽比1.15, 面积195
2025-07-28 14:50:33,479 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=289 (距底部154像素区域)
2025-07-28 14:50:33,480 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 14:50:33,481 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(136,289), 尺寸18x13, 长宽比1.38, 面积234
2025-07-28 14:50:33,483 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=289 (距底部154像素区域)
2025-07-28 14:50:33,486 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 14:50:33,487 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,289), 尺寸14x13, 长宽比1.08, 面积182
2025-07-28 14:50:33,488 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=289 (距底部154像素区域)
2025-07-28 14:50:33,489 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 14:50:33,489 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(89,289), 尺寸21x36, 长宽比0.58, 面积756
2025-07-28 14:50:33,490 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(93,287), 尺寸11x2, 长宽比5.50, 面积22
2025-07-28 14:50:33,490 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,287), 尺寸23x38, 长宽比0.61, 面积874
2025-07-28 14:50:33,491 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(316,282), 尺寸12x31, 长宽比0.39, 面积372
2025-07-28 14:50:33,491 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(20,279), 尺寸20x18, 长宽比1.11, 面积360
2025-07-28 14:50:33,492 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=279 (距底部154像素区域)
2025-07-28 14:50:33,494 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 14:50:33,495 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(223,254), 尺寸13x2, 长宽比6.50, 面积26
2025-07-28 14:50:33,496 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(125,254), 尺寸4x1, 长宽比4.00, 面积4
2025-07-28 14:50:33,496 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(232,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,497 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(144,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,498 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(253,252), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:50:33,504 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,252), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:50:33,505 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(247,252), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 14:50:33,506 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(191,252), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 14:50:33,506 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(316,250), 尺寸12x3, 长宽比4.00, 面积36
2025-07-28 14:50:33,507 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(244,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,508 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(162,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 14:50:33,509 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,247), 尺寸5x4, 长宽比1.25, 面积20
2025-07-28 14:50:33,510 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(178,247), 尺寸12x9, 长宽比1.33, 面积108
2025-07-28 14:50:33,511 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,246), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,512 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,246), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 14:50:33,512 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,246), 尺寸2x5, 长宽比0.40, 面积10
2025-07-28 14:50:33,512 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,244), 尺寸11x10, 长宽比1.10, 面积110
2025-07-28 14:50:33,513 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(197,244), 尺寸17x12, 长宽比1.42, 面积204
2025-07-28 14:50:33,514 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(185,244), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 14:50:33,516 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,244), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:50:33,517 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(123,244), 尺寸55x12, 长宽比4.58, 面积660
2025-07-28 14:50:33,519 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,244), 尺寸8x11, 长宽比0.73, 面积88
2025-07-28 14:50:33,521 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(232,243), 尺寸14x13, 长宽比1.08, 面积182
2025-07-28 14:50:33,523 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(209,243), 尺寸12x13, 长宽比0.92, 面积156
2025-07-28 14:50:33,523 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(245,232), 尺寸6x1, 长宽比6.00, 面积6
2025-07-28 14:50:33,524 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(241,232), 尺寸3x1, 长宽比3.00, 面积3
2025-07-28 14:50:33,524 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(132,232), 尺寸3x1, 长宽比3.00, 面积3
2025-07-28 14:50:33,525 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(152,231), 尺寸8x4, 长宽比2.00, 面积32
2025-07-28 14:50:33,525 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(124,231), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 14:50:33,526 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(126,230), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:50:33,526 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(239,229), 尺寸1x3, 长宽比0.33, 面积3
2025-07-28 14:50:33,528 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(19,229), 尺寸22x22, 长宽比1.00, 面积484
2025-07-28 14:50:33,529 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(124,228), 尺寸22x7, 长宽比3.14, 面积154
2025-07-28 14:50:33,529 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(241,226), 尺寸2x5, 长宽比0.40, 面积10
2025-07-28 14:50:33,530 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(139,225), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 14:50:33,530 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(133,225), 尺寸4x2, 长宽比2.00, 面积8
2025-07-28 14:50:33,531 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,225), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 14:50:33,532 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(234,224), 尺寸4x8, 长宽比0.50, 面积32
2025-07-28 14:50:33,533 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(239,223), 尺寸20x10, 长宽比2.00, 面积200
2025-07-28 14:50:33,534 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,223), 尺寸2x10, 长宽比0.20, 面积20
2025-07-28 14:50:33,538 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,223), 尺寸4x12, 长宽比0.33, 面积48
2025-07-28 14:50:33,540 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(316,222), 尺寸1x4, 长宽比0.25, 面积4
2025-07-28 14:50:33,540 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,222), 尺寸27x11, 长宽比2.45, 面积297
2025-07-28 14:50:33,541 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=96.5 (阈值:60)
2025-07-28 14:50:33,542 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(316,221), 尺寸5x17, 长宽比0.29, 面积85
2025-07-28 14:50:33,543 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,221), 尺寸11x5, 长宽比2.20, 面积55
2025-07-28 14:50:33,545 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(126,221), 尺寸6x1, 长宽比6.00, 面积6
2025-07-28 14:50:33,546 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,221), 尺寸12x8, 长宽比1.50, 面积96
2025-07-28 14:50:33,547 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=97.1 (阈值:60)
2025-07-28 14:50:33,548 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(316,218), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 14:50:33,550 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,215), 尺寸42x41, 长宽比1.02, 面积1722
2025-07-28 14:50:33,557 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(318,209), 尺寸7x20, 长宽比0.35, 面积140
2025-07-28 14:50:33,558 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(313,209), 尺寸15x114, 长宽比0.13, 面积1710
2025-07-28 14:50:33,559 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(272,209), 尺寸1x245, 长宽比0.00, 面积245
2025-07-28 14:50:33,559 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(270,209), 尺寸1x245, 长宽比0.00, 面积245
2025-07-28 14:50:33,560 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(61,209), 尺寸1x245, 长宽比0.00, 面积245
2025-07-28 14:50:33,560 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸1x245, 长宽比0.00, 面积245
2025-07-28 14:50:33,561 - WeChatAutoAdd - INFO - 底部区域找到 32 个按钮候选
2025-07-28 14:50:33,561 - WeChatAutoAdd - INFO - 选择特殊位置按钮: Y=279 (距底部154像素)
2025-07-28 14:50:33,562 - WeChatAutoAdd - INFO - 在底部找到按钮: (30, 288), 尺寸: 20x18, 位置得分: 3.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-07-28 14:50:33,563 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 14:50:33,578 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_145033.png
2025-07-28 14:50:33,584 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 14:50:33,585 - WeChatAutoAdd - INFO - 开始验证按钮区域(30, 288)是否包含'添加到通讯录'文字
2025-07-28 14:50:33,590 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_145033.png
2025-07-28 14:50:33,674 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 14:50:33,684 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0600, 平均亮度=226.2, 亮度标准差=41.5
2025-07-28 14:50:33,686 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 14:50:33,687 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 14:50:33,988 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(30, 288) -> 屏幕坐标(30, 288)
2025-07-28 14:50:34,788 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 14:50:34,808 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 14:50:34,828 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-28 14:50:34,842 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 14:50:35,171 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 14:50:35,172 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 14:50:35,172 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 14:50:35,172 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 14:50:35,172 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 14:50:35,173 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 14:50:35,173 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 14:50:35,173 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 14:50:35,174 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 14:50:35,174 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 14:50:35,375 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 14:50:35,376 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 14:50:35,377 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-28 14:50:35,677 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 14:50:35,678 - __main__ - INFO - ✅ 步骤4：图像识别添加完成
2025-07-28 14:50:36,679 - __main__ - INFO - 📍 当前执行步骤: FRIEND_REQUEST (步骤 5)
2025-07-28 14:50:36,679 - __main__ - INFO - 👥 步骤5：好友申请窗口 - 窗口 1
2025-07-28 14:50:36,679 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13046777941
2025-07-28 14:50:36,682 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-28 14:50:36,683 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\main_controller.py:412 in execute_single_window_flow
2025-07-28 14:50:36,683 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\main_controller.py:334 in execute_step_5_friend_request
2025-07-28 14:50:36,683 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-28 14:50:36,684 - modules.friend_request_window - INFO -    📱 phone: '13046777941'
2025-07-28 14:50:36,684 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-28 14:50:36,685 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-28 14:50:37,199 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-28 14:50:37,199 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-28 14:50:37,200 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-28 14:50:37,201 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 14:50:37,202 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13046777941
2025-07-28 14:50:37,203 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-28 14:50:37,203 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 14:50:37,204 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-28 14:50:37,205 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-28 14:50:37,205 - modules.friend_request_window - INFO -    📱 手机号码: 13046777941
2025-07-28 14:50:37,205 - modules.friend_request_window - INFO -    🆔 准考证: 015825120129
2025-07-28 14:50:37,206 - modules.friend_request_window - INFO -    👤 姓名: 徐金超
2025-07-28 14:50:37,206 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 14:50:37,206 - modules.friend_request_window - INFO -    📝 备注格式: '015825120129-徐金超-2025-07-28 14:50:37'
2025-07-28 14:50:37,206 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 14:50:37,207 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '015825120129-徐金超-2025-07-28 14:50:37'
2025-07-28 14:50:37,207 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 14:50:37,209 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-28 14:50:37,209 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-28 14:50:37,210 - __main__ - ERROR - ❌ 好友申请流程失败
2025-07-28 14:50:37,211 - __main__ - ERROR - ❌ 步骤 5 执行失败，终止当前窗口处理
2025-07-28 14:50:37,211 - __main__ - ERROR - ❌ 第 1 个微信窗口处理失败
2025-07-28 14:50:37,212 - __main__ - INFO - ⏳ 窗口切换延迟...
2025-07-28 14:50:40,212 - __main__ - INFO - 
============================================================
2025-07-28 14:50:40,213 - __main__ - INFO - 🎯 开始处理第 2/2 个微信窗口
2025-07-28 14:50:40,213 - __main__ - INFO - ============================================================
2025-07-28 14:50:40,213 - __main__ - INFO - 🚀 开始处理微信窗口 2
2025-07-28 14:50:40,214 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 525668)
2025-07-28 14:50:40,214 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-28 14:50:40,214 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 2
2025-07-28 14:50:40,215 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-28 14:50:40,649 - modules.window_manager - INFO - ✅ 临时置顶策略成功
2025-07-28 14:50:40,650 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 14:50:40,651 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 14:50:40,651 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 14:50:40,652 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 14:50:40,652 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 14:50:40,652 - modules.window_manager - INFO - 📏 当前窗口位置: (207, 108), 大小: 726x650
2025-07-28 14:50:40,653 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 14:50:40,653 - modules.window_manager - INFO - 🔄 尝试调整窗口大小和位置...
2025-07-28 14:50:40,956 - modules.window_manager - INFO - 📊 验证结果 - 当前位置: (0, 0), 实际大小: 726x650
2025-07-28 14:50:40,956 - modules.window_manager - INFO - 📊 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 14:50:40,956 - modules.window_manager - INFO - 📊 差异: 位置(0, 0), 大小(3, 0)
2025-07-28 14:50:40,957 - modules.window_manager - INFO - ✅ 窗口大小和位置验证通过
2025-07-28 14:50:40,957 - modules.window_manager - INFO - ✅ 虽然API可能报告失败，但窗口实际已调整到目标大小和位置
2025-07-28 14:50:40,957 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 14:50:41,159 - modules.window_manager - INFO - ✅ 窗口可见且非最小化（部分成功）
2025-07-28 14:50:41,160 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 14:50:41,160 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 525668
2025-07-28 14:50:41,160 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-28 14:50:41,571 - modules.window_manager - INFO - ✅ 临时置顶策略成功
2025-07-28 14:50:41,572 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 14:50:41,572 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 14:50:41,573 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 14:50:41,573 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 14:50:41,573 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 14:50:41,574 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 14:50:41,574 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 14:50:41,575 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 14:50:41,575 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 14:50:41,776 - modules.window_manager - INFO - ✅ 窗口可见且非最小化（部分成功）
2025-07-28 14:50:41,777 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 14:50:41,780 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 525668 (API返回: None)
2025-07-28 14:50:42,081 - modules.window_manager - WARNING - ⚠️ 窗口可能未完全置于最前面 (当前前台: 197994)
2025-07-28 14:50:42,082 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 14:50:42,082 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 14:50:42,082 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 14:50:42,083 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 14:50:42,084 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 14:50:42,084 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-28 14:50:43,084 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-28 14:50:43,085 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 2
2025-07-28 14:50:43,085 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-28 14:50:43,085 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-28 14:50:43,086 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-28 14:50:43,086 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-28 14:50:43,086 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-28 14:50:43,086 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-28 14:50:43,287 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-28 14:50:43,288 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-28 14:50:45,673 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-28 14:50:45,673 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-28 14:50:45,674 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.2 秒...
2025-07-28 14:50:47,877 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-28 14:50:48,078 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-28 14:50:48,079 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-28 14:50:50,451 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-28 14:50:50,451 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-28 14:50:50,452 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.6 秒...
2025-07-28 14:50:53,053 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-28 14:50:53,254 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-28 14:50:53,255 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-28 14:50:55,684 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-28 14:50:55,685 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-28 14:50:55,685 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.9 秒...
