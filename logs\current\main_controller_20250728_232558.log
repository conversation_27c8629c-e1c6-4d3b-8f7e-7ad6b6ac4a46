2025-07-28 23:25:58,986 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 23:25:58,987 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 23:25:58,988 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-28 23:25:58,988 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 23:25:58,989 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-28 23:25:58,989 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 23:25:58,990 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 23:25:58,991 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 23:25:58,991 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 23:25:58,992 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 23:25:58,994 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:25:59,002 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 23:25:59,005 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250728_232559.log
2025-07-28 23:25:59,007 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-28 23:25:59,008 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-28 23:25:59,009 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-28 23:25:59,015 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-28 23:25:59,016 - __main__ - INFO - 📅 当前北京时间: 2025-07-29 07:25:59
2025-07-28 23:25:59,017 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-28 23:25:59,018 - __main__ - INFO - 📅 启动时间: 2025-07-29 07:25:59
2025-07-28 23:25:59,019 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-28 23:25:59,019 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 23:25:59,559 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 23:25:59,562 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 23:26:00,086 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 23:26:00,086 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 23:26:00,088 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-28 23:26:00,089 - __main__ - INFO - ✅ 找到 2 个微信窗口
2025-07-28 23:26:00,089 - __main__ - INFO -   窗口 1: 微信 (句柄: 197994)
2025-07-28 23:26:00,089 - __main__ - INFO -   窗口 2: 微信 (句柄: 525668)
2025-07-28 23:26:00,090 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-28 23:26:01,268 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-28 23:26:01,269 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-28 23:26:01,269 - __main__ - INFO - 📋 待处理联系人数: 2975
2025-07-28 23:26:01,270 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-28 23:26:01,270 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2975
2025-07-28 23:26:01,270 - __main__ - INFO - 
============================================================
2025-07-28 23:26:01,270 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口
2025-07-28 23:26:01,271 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-28 23:26:01,271 - __main__ - INFO - ============================================================
2025-07-28 23:26:01,271 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-28 23:26:01,271 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-28 23:26:01,272 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-28 23:26:01,272 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-28 23:26:01,272 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 23:26:01,586 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 23:26:01,589 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 23:26:01,589 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 23:26:01,590 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 23:26:01,590 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 23:26:01,591 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 23:26:01,591 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 23:26:01,591 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 23:26:01,595 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 23:26:01,596 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 23:26:01,799 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 23:26:01,799 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 23:26:01,799 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-28 23:26:01,800 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 23:26:02,104 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 23:26:02,105 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 23:26:02,105 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 23:26:02,106 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 23:26:02,106 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 23:26:02,106 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 23:26:02,107 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 23:26:02,107 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 23:26:02,108 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 23:26:02,108 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 23:26:02,310 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 23:26:02,311 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 23:26:02,313 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-28 23:26:02,613 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 23:26:02,614 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 23:26:02,614 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 23:26:02,615 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 23:26:02,615 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 23:26:02,616 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 23:26:02,616 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-28 23:26:02,616 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-28 23:26:03,617 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-28 23:26:03,617 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-28 23:26:03,618 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-28 23:26:03,618 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-28 23:26:03,618 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-28 23:26:03,619 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-28 23:26:03,619 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-28 23:26:03,619 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-28 23:26:03,820 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-28 23:26:03,821 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-28 23:26:06,219 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-28 23:26:06,220 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-28 23:26:06,220 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.9 秒...
2025-07-28 23:26:09,098 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-28 23:26:09,299 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-28 23:26:09,300 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-28 23:26:11,679 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-28 23:26:11,680 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-28 23:26:11,680 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.9 秒...
2025-07-28 23:26:13,603 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-28 23:26:13,804 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-28 23:26:13,805 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-28 23:26:16,180 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-28 23:26:16,180 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-28 23:26:16,180 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-28 23:26:18,520 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-28 23:26:18,721 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-28 23:26:18,722 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-28 23:26:21,095 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-28 23:26:21,096 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-28 23:26:21,097 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.2 秒...
2025-07-28 23:26:23,331 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-28 23:26:23,533 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-28 23:26:23,533 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-28 23:26:25,912 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-28 23:26:25,912 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-28 23:26:25,913 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-28 23:26:25,913 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-28 23:26:25,914 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 23:26:25,916 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 23:26:25,917 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 23:26:25,918 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 23:26:25,922 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 23:26:25,925 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 12585338, 进程: Weixin.exe)
2025-07-28 23:26:25,940 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-28 23:26:25,940 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 12585338)
2025-07-28 23:26:25,952 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 12585338) - 增强版
2025-07-28 23:26:26,256 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 23:26:26,257 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 23:26:26,257 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-28 23:26:26,260 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-28 23:26:26,261 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-28 23:26:26,262 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-28 23:26:26,469 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-28 23:26:26,469 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-28 23:26:26,672 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 23:26:26,672 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 23:26:26,673 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-28 23:26:26,673 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-28 23:26:26,673 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-28 23:26:26,673 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-28 23:26:26,674 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-28 23:26:27,681 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-28 23:26:27,683 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-28 23:26:27,684 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 23:26:27,685 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-28 23:26:27,688 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-28 23:26:27,689 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-28 23:26:27,690 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 12585338, 进程: Weixin.exe)
2025-07-28 23:26:27,695 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-28 23:26:27,697 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-28 23:26:27,698 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-28 23:26:27,699 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-28 23:26:27,703 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-28 23:26:27,705 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-28 23:26:28,016 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 23:26:28,017 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 23:26:28,017 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-28 23:26:28,017 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-28 23:26:28,017 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-28 23:26:28,018 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-28 23:26:28,018 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-28 23:26:28,019 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-28 23:26:28,019 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-28 23:26:28,020 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-28 23:26:28,222 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 23:26:28,223 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 23:26:28,225 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-28 23:26:28,526 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 23:26:28,526 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-28 23:26:28,527 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-28 23:26:29,528 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-28 23:26:29,528 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-28 23:26:29,528 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-28 23:26:29,532 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250728_232629.log
2025-07-28 23:26:29,533 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-28 23:26:29,533 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-28 23:26:29,534 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-28 23:26:29,534 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-28 23:26:29,537 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-28 23:26:29,537 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-28 23:26:29,537 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-28 23:26:29,537 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-28 23:26:29,538 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-28 23:26:29,538 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-28 23:26:29,538 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-28 23:26:29,539 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 23:26:29,540 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 12585338
2025-07-28 23:26:29,540 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 12585338) - 增强版
2025-07-28 23:26:29,850 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-28 23:26:29,851 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-28 23:26:29,851 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-28 23:26:29,851 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-28 23:26:29,852 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-28 23:26:29,852 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-28 23:26:29,852 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-28 23:26:29,853 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-28 23:26:30,055 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-28 23:26:30,070 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-28 23:26:30,080 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 12585338 (API返回: None)
2025-07-28 23:26:30,384 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-28 23:26:30,384 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-28 23:26:30,385 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-28 23:26:30,385 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-28 23:26:30,386 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-28 23:26:30,387 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-28 23:26:30,387 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-28 23:26:30,393 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-28 23:26:30,395 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-28 23:26:31,321 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-28 23:26:31,321 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 23:26:31,761 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2975 个
2025-07-28 23:26:31,812 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2975 个 (总计: 3135 个)
2025-07-28 23:26:31,852 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 10 个联系人后切换
2025-07-28 23:26:31,914 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:26:31,985 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-28 23:26:31,996 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2975
2025-07-28 23:26:32,019 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18644888251 (陈钦贵)
2025-07-28 23:26:32,038 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:26:38,655 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18644888251
2025-07-28 23:26:38,655 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 23:26:38,656 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18644888251 执行添加朋友操作...
2025-07-28 23:26:38,656 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 23:26:38,656 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 23:26:38,659 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 23:26:38,660 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 23:26:38,663 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 2 个图片文件
2025-07-28 23:26:38,666 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 23:26:38,667 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 23:26:38,668 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 23:26:38,668 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 23:26:38,669 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 23:26:38,670 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 23:26:38,670 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 23:26:38,677 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-28 23:26:38,682 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 23:26:38,687 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 23:26:38,689 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 23:26:38,694 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-28 23:26:38,696 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-28 23:26:39,201 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-28 23:26:39,202 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-28 23:26:39,305 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差28.62, 边缘比例0.0352
2025-07-28 23:26:39,330 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_232639.png
2025-07-28 23:26:39,332 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 23:26:39,333 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 23:26:39,334 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-28 23:26:39,335 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-28 23:26:39,336 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-28 23:26:39,347 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_232639.png
2025-07-28 23:26:39,352 - WeChatAutoAdd - INFO - 底部区域原始检测到 38 个轮廓
2025-07-28 23:26:39,356 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 23:26:39,360 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 23:26:39,361 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:26:39,362 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-07-28 23:26:39,363 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 23:26:39,368 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 23:26:39,370 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-07-28 23:26:39,371 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:26:39,372 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-07-28 23:26:39,377 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-07-28 23:26:39,385 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-07-28 23:26:39,387 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:26:39,389 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 23:26:39,395 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 23:26:39,397 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:26:39,398 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 23:26:39,412 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 23:26:39,414 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:26:39,416 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:26:39,421 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-07-28 23:26:39,431 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 23:26:39,433 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-07-28 23:26:39,434 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:26:39,435 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-07-28 23:26:39,436 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-07-28 23:26:39,437 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-07-28 23:26:39,439 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-07-28 23:26:39,440 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-07-28 23:26:39,445 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-07-28 23:26:39,447 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-07-28 23:26:39,448 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-07-28 23:26:39,449 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-07-28 23:26:39,450 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-07-28 23:26:39,452 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-07-28 23:26:39,453 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-07-28 23:26:39,454 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-07-28 23:26:39,456 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-07-28 23:26:39,462 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-28 23:26:39,464 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-07-28 23:26:39,465 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-07-28 23:26:39,466 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-28 23:26:39,467 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 23:26:39,481 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_232639.png
2025-07-28 23:26:39,482 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 23:26:39,483 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-07-28 23:26:39,487 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_232639.png
2025-07-28 23:26:39,555 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 23:26:39,558 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-07-28 23:26:39,563 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 23:26:39,569 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 23:26:39,873 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-07-28 23:26:40,661 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 23:26:40,662 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 23:26:40,663 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:26:40,663 - modules.wechat_auto_add_simple - INFO - ✅ 18644888251 添加朋友操作执行成功
2025-07-28 23:26:40,663 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:26:40,664 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 23:26:42,665 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-28 23:26:42,666 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-28 23:26:42,666 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-28 23:26:42,666 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 23:26:42,666 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 23:26:42,667 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 23:26:42,667 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 23:26:42,667 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 23:26:42,668 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-28 23:26:42,668 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18644888251
2025-07-28 23:26:42,671 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-28 23:26:42,672 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 23:26:42,672 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:695 in _handle_friend_request_window
2025-07-28 23:26:42,673 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-28 23:26:42,674 - modules.friend_request_window - INFO -    📱 phone: '18644888251'
2025-07-28 23:26:42,675 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-28 23:26:42,677 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-28 23:26:43,670 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-28 23:26:43,671 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-28 23:26:43,671 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-28 23:26:43,672 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 23:26:43,678 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18644888251
2025-07-28 23:26:43,679 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-28 23:26:43,679 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 23:26:43,680 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-28 23:26:43,681 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-28 23:26:43,681 - modules.friend_request_window - INFO -    📱 手机号码: 18644888251
2025-07-28 23:26:43,682 - modules.friend_request_window - INFO -    🆔 准考证: 014325110071
2025-07-28 23:26:43,682 - modules.friend_request_window - INFO -    👤 姓名: 陈钦贵
2025-07-28 23:26:43,682 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 23:26:43,683 - modules.friend_request_window - INFO -    📝 备注格式: '014325110071-陈钦贵-2025-07-29 07:26:43'
2025-07-28 23:26:43,683 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 23:26:43,683 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110071-陈钦贵-2025-07-29 07:26:43'
2025-07-28 23:26:43,683 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 23:26:43,685 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-28 23:26:43,686 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-28 23:26:43,687 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:26:43,687 - modules.wechat_auto_add_simple - INFO - ✅ 18644888251 申请添加朋友窗口处理完成: 申请添加朋友窗口处理失败: 未找到好友申请窗口
2025-07-28 23:26:43,687 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 18644888251
2025-07-28 23:26:43,688 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:26:47,769 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2975
2025-07-28 23:26:47,770 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18910332622 (王洋洋)
2025-07-28 23:26:47,771 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:26:54,352 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18910332622
2025-07-28 23:26:54,352 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 23:26:54,353 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18910332622 执行添加朋友操作...
2025-07-28 23:26:54,353 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 23:26:54,354 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 23:26:54,355 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 23:26:54,366 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 23:26:54,378 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 23:26:54,385 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 23:26:54,386 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 23:26:54,388 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 23:26:54,392 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 23:26:54,393 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 23:26:54,398 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 23:26:54,400 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 23:26:54,405 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-28 23:26:54,436 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 23:26:54,455 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 23:26:54,598 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 23:26:54,709 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-28 23:26:54,735 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-28 23:26:55,237 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-28 23:26:55,238 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-28 23:26:55,318 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-07-28 23:26:55,319 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-07-28 23:26:55,329 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_232655.png
2025-07-28 23:26:55,331 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 23:26:55,332 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 23:26:55,334 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-28 23:26:55,336 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-28 23:26:55,338 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-28 23:26:55,346 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_232655.png
2025-07-28 23:26:55,347 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-28 23:26:55,348 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-28 23:26:55,351 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-28 23:26:55,353 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-28 23:26:55,354 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-28 23:26:55,355 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-28 23:26:55,355 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-28 23:26:55,362 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 23:26:55,373 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_232655.png
2025-07-28 23:26:55,379 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 23:26:55,381 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-28 23:26:55,386 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_232655.png
2025-07-28 23:26:55,421 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 23:26:55,428 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-28 23:26:55,430 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 23:26:55,431 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 23:26:55,733 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-28 23:26:56,510 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 23:26:56,512 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 23:26:56,513 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:26:56,513 - modules.wechat_auto_add_simple - INFO - ✅ 18910332622 添加朋友操作执行成功
2025-07-28 23:26:56,514 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:26:56,514 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 23:26:58,515 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-28 23:26:58,516 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-28 23:26:58,516 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-28 23:26:58,516 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-28 23:26:58,517 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-28 23:26:58,517 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-28 23:26:58,517 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-28 23:26:58,518 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-28 23:26:58,518 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-28 23:26:58,518 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18910332622
2025-07-28 23:26:58,519 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-28 23:26:58,519 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 23:26:58,519 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:695 in _handle_friend_request_window
2025-07-28 23:26:58,520 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-28 23:26:58,520 - modules.friend_request_window - INFO -    📱 phone: '18910332622'
2025-07-28 23:26:58,520 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-28 23:26:58,521 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-28 23:26:59,015 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-28 23:26:59,015 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-28 23:26:59,015 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-28 23:26:59,016 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-28 23:26:59,017 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18910332622
2025-07-28 23:26:59,017 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-28 23:26:59,018 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 23:26:59,018 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-28 23:26:59,018 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-28 23:26:59,019 - modules.friend_request_window - INFO -    📱 手机号码: 18910332622
2025-07-28 23:26:59,019 - modules.friend_request_window - INFO -    🆔 准考证: 014325110072
2025-07-28 23:26:59,019 - modules.friend_request_window - INFO -    👤 姓名: 王洋洋
2025-07-28 23:26:59,019 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 23:26:59,020 - modules.friend_request_window - INFO -    📝 备注格式: '014325110072-王洋洋-2025-07-29 07:26:59'
2025-07-28 23:26:59,020 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-28 23:26:59,021 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110072-王洋洋-2025-07-29 07:26:59'
2025-07-28 23:26:59,022 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-28 23:26:59,026 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 14027468, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-28 23:26:59,028 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 14027468)
2025-07-28 23:26:59,029 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-28 23:26:59,029 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-28 23:26:59,029 - modules.friend_request_window - INFO - 🔄 激活窗口: 14027468
2025-07-28 23:26:59,732 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-28 23:26:59,733 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-28 23:26:59,733 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-28 23:26:59,734 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:499 in _analyze_search_result
2025-07-28 23:26:59,734 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-28 23:26:59,734 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:592 in _execute_auto_add_friend
2025-07-28 23:26:59,734 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-28 23:26:59,735 - modules.friend_request_window - INFO -    3. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:695 in _handle_friend_request_window
2025-07-28 23:26:59,735 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-28 23:26:59,735 - modules.friend_request_window - INFO -    4. D:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-28 23:26:59,735 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-28 23:26:59,736 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-28 23:26:59,736 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-28 23:26:59,736 - modules.friend_request_window - INFO -    📝 remark参数: '014325110072-王洋洋-2025-07-29 07:26:59' (类型: <class 'str'>, 长度: 36)
2025-07-28 23:26:59,736 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-28 23:26:59,737 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110072-王洋洋-2025-07-29 07:26:59'
2025-07-28 23:26:59,737 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-28 23:26:59,737 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-28 23:26:59,737 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-28 23:26:59,738 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-28 23:26:59,738 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-28 23:26:59,741 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-28 23:26:59,742 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-28 23:27:00,720 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-28 23:27:06,002 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-28 23:27:06,003 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-28 23:27:06,003 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-28 23:27:06,004 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-28 23:27:06,006 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'PS D:\程序-测试\微信7.28 - 副本 (2)> python main_controlle...' (前50字符)
2025-07-28 23:27:06,324 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 23:27:06,325 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 23:27:07,228 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 23:27:07,240 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 23:27:07,243 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-28 23:27:07,244 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-28 23:27:07,244 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-28 23:27:07,246 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-28 23:27:07,747 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-28 23:27:07,748 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-28 23:27:07,748 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-28 23:27:07,748 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-28 23:27:07,749 - modules.friend_request_window - INFO -    📝 内容: '014325110072-王洋洋-2025-07-29 07:26:59'
2025-07-28 23:27:07,749 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-28 23:27:07,749 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110072-\xe7\x8e\x8b\xe6\xb4\x8b\xe6\xb4\x8b-2025-07-29 07:26:59'
2025-07-28 23:27:07,750 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-28 23:27:08,660 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-28 23:27:13,912 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-28 23:27:13,913 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-28 23:27:13,913 - modules.friend_request_window - INFO -    📝 原始文本: '014325110072-王洋洋-2025-07-29 07:26:59'
2025-07-28 23:27:13,913 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-28 23:27:13,914 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'PS D:\程序-测试\微信7.28 - 副本 (2)> python main_controlle...' (前50字符)
2025-07-28 23:27:14,221 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-28 23:27:14,224 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-28 23:27:15,127 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-28 23:27:15,134 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-28 23:27:15,134 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110072-王洋洋-2025-07-29 07:26:59'
2025-07-28 23:27:15,135 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-28 23:27:15,135 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110072-王洋洋-2025-07-29 07:26:59'
2025-07-28 23:27:15,136 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-28 23:27:15,637 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110072-王洋洋-2025-07-29 07:26:59'
2025-07-28 23:27:15,637 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-28 23:27:15,638 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-28 23:27:15,640 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-28 23:27:15,641 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-28 23:27:15,641 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-28 23:27:15,641 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-28 23:27:16,442 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-28 23:27:16,442 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-28 23:27:16,442 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-28 23:27:17,066 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:27:17,069 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-28 23:27:17,070 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-28 23:27:17,071 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-28 23:27:17,574 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-28 23:27:17,576 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-28 23:27:17,576 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-28 23:27:17,576 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-28 23:27:17,577 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-28 23:27:17,577 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-28 23:27:17,577 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-28 23:27:17,578 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-28 23:27:17,578 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-28 23:27:17,578 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-28 23:27:17,579 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-28 23:27:17,579 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-28 23:27:17,580 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-28 23:27:17,580 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-28 23:27:17,580 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-28 23:27:17,582 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-28 23:27:17,582 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-28 23:27:17,584 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-28 23:27:17,584 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-28 23:27:18,086 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-28 23:27:18,087 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-28 23:27:18,087 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-28 23:27:18,087 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-28 23:27:18,090 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-28 23:27:18,090 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-28 23:27:18,091 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-28 23:27:18,093 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-28 23:27:19,037 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-28 23:27:19,037 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-28 23:27:19,037 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-28 23:27:19,040 - modules.friend_request_window - INFO - 🔄 步骤2: 关闭添加朋友窗口...
2025-07-28 23:27:19,040 - modules.friend_request_window - INFO - 🎯 使用指定坐标 (1504, 13) 关闭添加朋友窗口...
2025-07-28 23:27:19,540 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui点击坐标 (1504, 13)...
2025-07-28 23:27:20,660 - modules.friend_request_window - INFO - ✅ pyautogui点击添加朋友窗口关闭按钮成功
2025-07-28 23:27:20,660 - modules.friend_request_window - INFO - 🔄 步骤3: 关闭当前微信窗口...
2025-07-28 23:27:20,660 - modules.friend_request_window - INFO - 🎯 使用指定坐标 (700, 16) 关闭当前微信窗口...
2025-07-28 23:27:21,165 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui点击坐标 (700, 16)...
2025-07-28 23:27:22,297 - modules.friend_request_window - INFO - ✅ pyautogui点击当前微信窗口关闭按钮成功
2025-07-28 23:27:22,297 - modules.friend_request_window - INFO - 🔄 关闭错误相关窗口...
2025-07-28 23:27:22,312 - modules.friend_request_window - INFO - ✅ 频率错误处理完成
2025-07-28 23:27:22,313 - modules.friend_request_window - INFO - 🔄 频率错误处理完成，需要重新开始流程
2025-07-28 23:27:22,313 - modules.friend_request_window - INFO - 🔄 已设置重新开始标志
2025-07-28 23:27:22,315 - modules.friend_request_window - INFO - ✅ 原有频率错误处理成功，已切换到微信2
2025-07-28 23:27:22,315 - modules.friend_request_window - WARNING - ⚠️ 检测到并处理了频率错误，已切换到微信2
2025-07-28 23:27:22,316 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-28 23:27:22,316 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-28 23:27:22,316 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-28 23:27:22,316 - modules.friend_request_window - INFO -    📝 备注信息: '014325110072-王洋洋-2025-07-29 07:26:59'
2025-07-28 23:27:22,817 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-28 23:27:22,818 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:27:22,818 - modules.wechat_auto_add_simple - INFO - ✅ 18910332622 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-28 23:27:22,819 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 18910332622
2025-07-28 23:27:22,820 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:27:26,397 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 3/2975
2025-07-28 23:27:26,397 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13135871818 (章协平)
2025-07-28 23:27:26,397 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:27:33,222 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13135871818
2025-07-28 23:27:33,223 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 23:27:33,223 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13135871818 执行添加朋友操作...
2025-07-28 23:27:33,223 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 23:27:33,224 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 23:27:33,225 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 23:27:33,226 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 23:27:33,230 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 23:27:33,232 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 23:27:33,233 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 23:27:33,234 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 23:27:33,235 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 23:27:33,235 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 23:27:33,236 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 23:27:33,236 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 23:27:33,243 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 23:27:33,248 - WeChatAutoAdd - DEBUG - 找到微信窗口: ● main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 23:27:33,250 - WeChatAutoAdd - INFO - 共找到 2 个微信窗口
2025-07-28 23:27:33,257 - WeChatAutoAdd - INFO - 找到微信主窗口: 微信
2025-07-28 23:27:33,760 - WeChatAutoAdd - INFO - 目标窗口: 微信
2025-07-28 23:27:33,761 - WeChatAutoAdd - INFO - 窗口位置: (0, 0), 尺寸: 726x650
2025-07-28 23:27:33,836 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差29.86, 边缘比例0.0396
2025-07-28 23:27:33,856 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_232733.png
2025-07-28 23:27:33,862 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 23:27:33,864 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 23:27:33,865 - WeChatAutoAdd - INFO - 截图尺寸: 726x650
2025-07-28 23:27:33,867 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=405-650 (高度:245)
2025-07-28 23:27:33,869 - WeChatAutoAdd - INFO - 特别关注区域: Y=466-526 (距底部154像素)
2025-07-28 23:27:33,882 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_232733.png
2025-07-28 23:27:33,885 - WeChatAutoAdd - INFO - 底部区域原始检测到 160 个轮廓
2025-07-28 23:27:33,886 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(75,642), 尺寸3x5, 长宽比0.60, 面积15
2025-07-28 23:27:33,893 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(83,641), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:27:33,897 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(78,640), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:27:33,899 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(218,639), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 23:27:33,901 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,639), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 23:27:33,902 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(210,639), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 23:27:33,910 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(106,638), 尺寸4x3, 长宽比1.33, 面积12
2025-07-28 23:27:33,912 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(97,638), 尺寸8x3, 长宽比2.67, 面积24
2025-07-28 23:27:33,916 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,638), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 23:27:33,918 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(91,637), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:27:33,924 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(83,636), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:27:33,927 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(247,635), 尺寸4x6, 长宽比0.67, 面积24
2025-07-28 23:27:33,928 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(242,631), 尺寸17x11, 长宽比1.55, 面积187
2025-07-28 23:27:33,930 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(233,631), 尺寸10x9, 长宽比1.11, 面积90
2025-07-28 23:27:33,943 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(231,631), 尺寸6x10, 长宽比0.60, 面积60
2025-07-28 23:27:33,951 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(80,631), 尺寸4x16, 长宽比0.25, 面积64
2025-07-28 23:27:33,953 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,630), 尺寸6x11, 长宽比0.55, 面积66
2025-07-28 23:27:33,959 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,630), 尺寸5x11, 长宽比0.45, 面积55
2025-07-28 23:27:33,960 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,630), 尺寸8x11, 长宽比0.73, 面积88
2025-07-28 23:27:33,961 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,630), 尺寸9x11, 长宽比0.82, 面积99
2025-07-28 23:27:33,961 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(185,629), 尺寸24x13, 长宽比1.85, 面积312
2025-07-28 23:27:33,965 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=84.3 (阈值:60)
2025-07-28 23:27:33,966 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,629), 尺寸15x13, 长宽比1.15, 面积195
2025-07-28 23:27:33,968 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=83.6 (阈值:60)
2025-07-28 23:27:33,970 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,629), 尺寸14x13, 长宽比1.08, 面积182
2025-07-28 23:27:33,975 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=78.1 (阈值:60)
2025-07-28 23:27:33,982 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,612), 尺寸17x1, 长宽比17.00, 面积17
2025-07-28 23:27:33,984 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,610), 尺寸18x1, 长宽比18.00, 面积18
2025-07-28 23:27:33,986 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,604), 尺寸18x3, 长宽比6.00, 面积54
2025-07-28 23:27:33,994 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,601), 尺寸18x1, 长宽比18.00, 面积18
2025-07-28 23:27:33,996 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,599), 尺寸17x1, 长宽比17.00, 面积17
2025-07-28 23:27:33,998 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(200,594), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 23:27:34,000 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(188,593), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 23:27:34,001 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,592), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 23:27:34,003 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(245,592), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 23:27:34,012 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(243,592), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:27:34,013 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(241,592), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 23:27:34,014 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(124,592), 尺寸4x4, 长宽比1.00, 面积16
2025-07-28 23:27:34,016 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,591), 尺寸4x3, 长宽比1.33, 面积12
2025-07-28 23:27:34,017 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,590), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 23:27:34,018 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(181,589), 尺寸1x5, 长宽比0.20, 面积5
2025-07-28 23:27:34,023 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(127,589), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:27:34,028 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(226,588), 尺寸4x6, 长宽比0.67, 面积24
2025-07-28 23:27:34,034 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(224,588), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 23:27:34,036 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,588), 尺寸3x1, 长宽比3.00, 面积3
2025-07-28 23:27:34,043 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(146,588), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 23:27:34,049 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(143,588), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:27:34,050 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(191,587), 尺寸6x8, 长宽比0.75, 面积48
2025-07-28 23:27:34,053 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(179,585), 尺寸11x9, 长宽比1.22, 面积99
2025-07-28 23:27:34,062 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(147,585), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 23:27:34,064 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(198,584), 尺寸27x11, 长宽比2.45, 面积297
2025-07-28 23:27:34,067 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(122,584), 尺寸45x12, 长宽比3.75, 面积540
2025-07-28 23:27:34,068 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,584), 尺寸4x11, 长宽比0.36, 面积44
2025-07-28 23:27:34,080 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,583), 尺寸11x13, 长宽比0.85, 面积143
2025-07-28 23:27:34,083 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(153,583), 尺寸8x11, 长宽比0.73, 面积88
2025-07-28 23:27:34,085 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(147,583), 尺寸7x3, 长宽比2.33, 面积21
2025-07-28 23:27:34,092 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(143,583), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 23:27:34,098 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,583), 尺寸13x11, 长宽比1.18, 面积143
2025-07-28 23:27:34,100 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(221,571), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 23:27:34,102 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(216,571), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 23:27:34,110 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,570), 尺寸7x4, 长宽比1.75, 面积28
2025-07-28 23:27:34,112 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(126,570), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 23:27:34,114 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(205,568), 尺寸2x5, 长宽比0.40, 面积10
2025-07-28 23:27:34,118 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(133,568), 尺寸2x5, 长宽比0.40, 面积10
2025-07-28 23:27:34,127 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(171,565), 尺寸4x4, 长宽比1.00, 面积16
2025-07-28 23:27:34,128 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(254,564), 尺寸4x8, 长宽比0.50, 面积32
2025-07-28 23:27:34,130 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(206,564), 尺寸8x10, 长宽比0.80, 面积80
2025-07-28 23:27:34,131 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,564), 尺寸6x10, 长宽比0.60, 面积60
2025-07-28 23:27:34,133 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(242,563), 尺寸11x11, 长宽比1.00, 面积121
2025-07-28 23:27:34,135 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(233,563), 尺寸10x9, 长宽比1.11, 面积90
2025-07-28 23:27:34,142 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(231,563), 尺寸6x10, 长宽比0.60, 面积60
2025-07-28 23:27:34,144 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(141,563), 尺寸10x11, 长宽比0.91, 面积110
2025-07-28 23:27:34,146 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(199,562), 尺寸6x11, 长宽比0.55, 面积66
2025-07-28 23:27:34,148 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(196,562), 尺寸5x6, 长宽比0.83, 面积30
2025-07-28 23:27:34,150 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(191,562), 尺寸7x11, 长宽比0.64, 面积77
2025-07-28 23:27:34,151 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(151,562), 尺寸28x11, 长宽比2.55, 面积308
2025-07-28 23:27:34,157 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=78.7 (阈值:60)
2025-07-28 23:27:34,160 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(120,561), 尺寸12x12, 长宽比1.00, 面积144
2025-07-28 23:27:34,162 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=78.6 (阈值:60)
2025-07-28 23:27:34,164 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,559), 尺寸38x38, 长宽比1.00, 面积1444
2025-07-28 23:27:34,166 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=71.2 (阈值:60)
2025-07-28 23:27:34,167 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,547), 尺寸16x22, 长宽比0.73, 面积352
2025-07-28 23:27:34,170 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(161,526), 尺寸4x1, 长宽比4.00, 面积4
2025-07-28 23:27:34,175 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,524), 尺寸4x2, 长宽比2.00, 面积8
2025-07-28 23:27:34,178 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,523), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 23:27:34,181 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,522), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 23:27:34,182 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(161,522), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 23:27:34,183 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,521), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 23:27:34,185 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(155,521), 尺寸5x7, 长宽比0.71, 面积35
2025-07-28 23:27:34,186 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(143,521), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:27:34,194 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(146,520), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 23:27:34,195 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(179,519), 尺寸10x7, 长宽比1.43, 面积70
2025-07-28 23:27:34,198 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(162,519), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 23:27:34,200 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(196,518), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 23:27:34,201 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,518), 尺寸1x5, 长宽比0.20, 面积5
2025-07-28 23:27:34,203 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,518), 尺寸4x2, 长宽比2.00, 面积8
2025-07-28 23:27:34,209 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(150,518), 尺寸1x9, 长宽比0.11, 面积9
2025-07-28 23:27:34,211 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(180,516), 尺寸10x2, 长宽比5.00, 面积20
2025-07-28 23:27:34,216 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(155,516), 尺寸29x12, 长宽比2.42, 面积348
2025-07-28 23:27:34,217 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=516 (距底部154像素区域)
2025-07-28 23:27:34,219 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 23:27:34,228 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,516), 尺寸4x13, 长宽比0.31, 面积52
2025-07-28 23:27:34,232 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(185,515), 尺寸42x13, 长宽比3.23, 面积546
2025-07-28 23:27:34,233 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=515 (距底部154像素区域)
2025-07-28 23:27:34,235 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 23:27:34,243 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(123,515), 尺寸26x14, 长宽比1.86, 面积364
2025-07-28 23:27:34,249 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=515 (距底部154像素区域)
2025-07-28 23:27:34,252 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 23:27:34,276 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,505), 尺寸8x1, 长宽比8.00, 面积8
2025-07-28 23:27:34,324 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,503), 尺寸9x1, 长宽比9.00, 面积9
2025-07-28 23:27:34,334 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,502), 尺寸8x4, 长宽比2.00, 面积32
2025-07-28 23:27:34,345 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(139,502), 尺寸6x2, 长宽比3.00, 面积12
2025-07-28 23:27:34,348 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,499), 尺寸8x3, 长宽比2.67, 面积24
2025-07-28 23:27:34,351 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(163,498), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:27:34,353 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(154,498), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:27:34,360 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,497), 尺寸1x4, 长宽比0.25, 面积4
2025-07-28 23:27:34,362 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(229,495), 尺寸29x11, 长宽比2.64, 面积319
2025-07-28 23:27:34,365 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=495 (距底部154像素区域)
2025-07-28 23:27:34,366 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 23:27:34,369 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(163,495), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 23:27:34,376 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,495), 尺寸10x10, 长宽比1.00, 面积100
2025-07-28 23:27:34,377 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=495 (距底部154像素区域)
2025-07-28 23:27:34,379 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 23:27:34,380 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(170,494), 尺寸5x11, 长宽比0.45, 面积55
2025-07-28 23:27:34,382 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(168,494), 尺寸1x7, 长宽比0.14, 面积7
2025-07-28 23:27:34,385 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(161,493), 尺寸6x13, 长宽比0.46, 面积78
2025-07-28 23:27:34,386 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,493), 尺寸10x1, 长宽比10.00, 面积10
2025-07-28 23:27:34,395 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,493), 尺寸41x14, 长宽比2.93, 面积574
2025-07-28 23:27:34,399 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=493 (距底部154像素区域)
2025-07-28 23:27:34,401 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 23:27:34,403 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,491), 尺寸37x37, 长宽比1.00, 面积1369
2025-07-28 23:27:34,410 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=491 (距底部154像素区域)
2025-07-28 23:27:34,411 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 23:27:34,412 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,456), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 23:27:34,414 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(247,456), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 23:27:34,416 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(244,456), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 23:27:34,418 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(203,456), 尺寸9x3, 长宽比3.00, 面积27
2025-07-28 23:27:34,425 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(208,454), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 23:27:34,427 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(203,454), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:27:34,429 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(175,454), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:27:34,431 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,454), 尺寸10x8, 长宽比1.25, 面积80
2025-07-28 23:27:34,433 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(201,453), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:27:34,434 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(158,453), 尺寸4x5, 长宽比0.80, 面积20
2025-07-28 23:27:34,436 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,452), 尺寸4x4, 长宽比1.00, 面积16
2025-07-28 23:27:34,441 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,452), 尺寸7x4, 长宽比1.75, 面积28
2025-07-28 23:27:34,448 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,452), 尺寸8x7, 长宽比1.14, 面积56
2025-07-28 23:27:34,450 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,452), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:27:34,453 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,452), 尺寸1x3, 长宽比0.33, 面积3
2025-07-28 23:27:34,461 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,452), 尺寸6x10, 长宽比0.60, 面积60
2025-07-28 23:27:34,466 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,451), 尺寸5x4, 长宽比1.25, 面积20
2025-07-28 23:27:34,469 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(159,451), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 23:27:34,477 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,451), 尺寸5x4, 长宽比1.25, 面积20
2025-07-28 23:27:34,482 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(132,451), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:27:34,486 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(123,451), 尺寸8x7, 长宽比1.14, 面积56
2025-07-28 23:27:34,495 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(171,450), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 23:27:34,502 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(230,449), 尺寸13x11, 长宽比1.18, 面积143
2025-07-28 23:27:34,515 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,449), 尺寸5x1, 长宽比5.00, 面积5
2025-07-28 23:27:34,517 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,449), 尺寸23x11, 长宽比2.09, 面积253
2025-07-28 23:27:34,524 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,448), 尺寸17x12, 长宽比1.42, 面积204
2025-07-28 23:27:34,530 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(173,448), 尺寸10x8, 长宽比1.25, 面积80
2025-07-28 23:27:34,533 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,447), 尺寸19x13, 长宽比1.46, 面积247
2025-07-28 23:27:34,535 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,447), 尺寸11x13, 长宽比0.85, 面积143
2025-07-28 23:27:34,540 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(159,447), 尺寸13x3, 长宽比4.33, 面积39
2025-07-28 23:27:34,544 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(218,435), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 23:27:34,545 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,435), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 23:27:34,547 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(152,435), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 23:27:34,548 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(253,431), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 23:27:34,550 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(186,429), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:27:34,551 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(229,427), 尺寸30x11, 长宽比2.73, 面积330
2025-07-28 23:27:34,557 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(186,427), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:27:34,560 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(188,426), 尺寸3x11, 长宽比0.27, 面积33
2025-07-28 23:27:34,564 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,426), 尺寸6x11, 长宽比0.55, 面积66
2025-07-28 23:27:34,566 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,426), 尺寸5x11, 长宽比0.45, 面积55
2025-07-28 23:27:34,567 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,426), 尺寸17x11, 长宽比1.55, 面积187
2025-07-28 23:27:34,569 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=87.1 (阈值:60)
2025-07-28 23:27:34,579 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(202,425), 尺寸9x13, 长宽比0.69, 面积117
2025-07-28 23:27:34,580 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,425), 尺寸7x13, 长宽比0.54, 面积91
2025-07-28 23:27:34,581 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(153,425), 尺寸15x13, 长宽比1.15, 面积195
2025-07-28 23:27:34,583 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=88.5 (阈值:60)
2025-07-28 23:27:34,586 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,425), 尺寸14x13, 长宽比1.08, 面积182
2025-07-28 23:27:34,594 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=78.1 (阈值:60)
2025-07-28 23:27:34,595 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,423), 尺寸38x38, 长宽比1.00, 面积1444
2025-07-28 23:27:34,597 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=71.2 (阈值:60)
2025-07-28 23:27:34,598 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,405), 尺寸726x245, 长宽比2.96, 面积177870
2025-07-28 23:27:34,600 - WeChatAutoAdd - INFO - 底部区域找到 22 个按钮候选
2025-07-28 23:27:34,602 - WeChatAutoAdd - INFO - 选择特殊位置按钮: Y=491 (距底部154像素)
2025-07-28 23:27:34,607 - WeChatAutoAdd - INFO - 在底部找到按钮: (90, 509), 尺寸: 37x37, 位置得分: 3.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-07-28 23:27:34,612 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 23:27:34,636 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_232734.png
2025-07-28 23:27:34,644 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 23:27:34,649 - WeChatAutoAdd - INFO - 开始验证按钮区域(90, 509)是否包含'添加到通讯录'文字
2025-07-28 23:27:34,654 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_232734.png
2025-07-28 23:27:34,696 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 23:27:34,699 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0508, 平均亮度=227.0, 亮度标准差=45.1
2025-07-28 23:27:34,700 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 23:27:34,701 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 23:27:35,010 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(90, 509) -> 屏幕坐标(90, 509)
2025-07-28 23:27:35,810 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 23:27:35,814 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 23:27:35,816 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:27:35,817 - modules.wechat_auto_add_simple - INFO - ✅ 13135871818 添加朋友操作执行成功
2025-07-28 23:27:35,818 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:27:35,824 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 23:27:37,829 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:27:37,830 - modules.wechat_auto_add_simple - INFO - ℹ️ 13135871818 未发现申请添加朋友窗口，可能直接添加成功
2025-07-28 23:27:37,830 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 13135871818
2025-07-28 23:27:37,831 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:27:41,332 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 4/2975
2025-07-28 23:27:41,332 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13164180165 (朱国涛)
2025-07-28 23:27:41,333 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:27:47,899 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13164180165
2025-07-28 23:27:47,899 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-28 23:27:47,900 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13164180165 执行添加朋友操作...
2025-07-28 23:27:47,900 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-28 23:27:47,900 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-28 23:27:47,901 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-28 23:27:47,902 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-28 23:27:47,908 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-28 23:27:47,912 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-28 23:27:47,913 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-28 23:27:47,913 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-28 23:27:47,913 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-28 23:27:47,913 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-28 23:27:47,914 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-28 23:27:47,914 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-28 23:27:47,917 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-28 23:27:47,921 - WeChatAutoAdd - DEBUG - 找到微信窗口: ● main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-28 23:27:47,926 - WeChatAutoAdd - INFO - 共找到 2 个微信窗口
2025-07-28 23:27:47,928 - WeChatAutoAdd - INFO - 找到微信主窗口: 微信
2025-07-28 23:27:48,432 - WeChatAutoAdd - INFO - 目标窗口: 微信
2025-07-28 23:27:48,434 - WeChatAutoAdd - INFO - 窗口位置: (0, 0), 尺寸: 726x650
2025-07-28 23:27:48,526 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差36.60, 边缘比例0.0605
2025-07-28 23:27:48,551 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250728_232748.png
2025-07-28 23:27:48,565 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-28 23:27:48,577 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-28 23:27:48,579 - WeChatAutoAdd - INFO - 截图尺寸: 726x650
2025-07-28 23:27:48,584 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=405-650 (高度:245)
2025-07-28 23:27:48,592 - WeChatAutoAdd - INFO - 特别关注区域: Y=466-526 (距底部154像素)
2025-07-28 23:27:48,604 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250728_232748.png
2025-07-28 23:27:48,608 - WeChatAutoAdd - INFO - 底部区域原始检测到 92 个轮廓
2025-07-28 23:27:48,609 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(724,648), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 23:27:48,610 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,612), 尺寸17x1, 长宽比17.00, 面积17
2025-07-28 23:27:48,614 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,610), 尺寸18x1, 长宽比18.00, 面积18
2025-07-28 23:27:48,615 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,604), 尺寸18x3, 长宽比6.00, 面积54
2025-07-28 23:27:48,616 - WeChatAutoAdd - INFO - 重要轮廓: 位置(614,602), 尺寸96x32, 长宽比3.00, 已知特征:False
2025-07-28 23:27:48,618 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度96 (需要70-95)
2025-07-28 23:27:48,624 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,601), 尺寸18x1, 长宽比18.00, 面积18
2025-07-28 23:27:48,625 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,599), 尺寸17x1, 长宽比17.00, 面积17
2025-07-28 23:27:48,627 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,547), 尺寸16x22, 长宽比0.73, 面积352
2025-07-28 23:27:48,629 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(161,526), 尺寸4x1, 长宽比4.00, 面积4
2025-07-28 23:27:48,630 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,524), 尺寸4x2, 长宽比2.00, 面积8
2025-07-28 23:27:48,632 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,523), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 23:27:48,633 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,522), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 23:27:48,634 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(161,522), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 23:27:48,638 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,521), 尺寸2x3, 长宽比0.67, 面积6
2025-07-28 23:27:48,642 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(155,521), 尺寸5x7, 长宽比0.71, 面积35
2025-07-28 23:27:48,644 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(143,521), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:27:48,647 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(146,520), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 23:27:48,649 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(179,519), 尺寸10x7, 长宽比1.43, 面积70
2025-07-28 23:27:48,651 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(162,519), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 23:27:48,660 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(196,518), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 23:27:48,662 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,518), 尺寸1x5, 长宽比0.20, 面积5
2025-07-28 23:27:48,665 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,518), 尺寸4x2, 长宽比2.00, 面积8
2025-07-28 23:27:48,666 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(150,518), 尺寸1x9, 长宽比0.11, 面积9
2025-07-28 23:27:48,668 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(180,516), 尺寸10x2, 长宽比5.00, 面积20
2025-07-28 23:27:48,677 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(155,516), 尺寸29x12, 长宽比2.42, 面积348
2025-07-28 23:27:48,681 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=516 (距底部154像素区域)
2025-07-28 23:27:48,684 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 23:27:48,693 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,516), 尺寸4x13, 长宽比0.31, 面积52
2025-07-28 23:27:48,694 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(185,515), 尺寸42x13, 长宽比3.23, 面积546
2025-07-28 23:27:48,697 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=515 (距底部154像素区域)
2025-07-28 23:27:48,699 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 23:27:48,700 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(123,515), 尺寸26x14, 长宽比1.86, 面积364
2025-07-28 23:27:48,702 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=515 (距底部154像素区域)
2025-07-28 23:27:48,710 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 23:27:48,713 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,505), 尺寸8x1, 长宽比8.00, 面积8
2025-07-28 23:27:48,715 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,503), 尺寸9x1, 长宽比9.00, 面积9
2025-07-28 23:27:48,716 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,502), 尺寸8x4, 长宽比2.00, 面积32
2025-07-28 23:27:48,724 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(139,502), 尺寸6x2, 长宽比3.00, 面积12
2025-07-28 23:27:48,727 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,499), 尺寸8x3, 长宽比2.67, 面积24
2025-07-28 23:27:48,732 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(163,498), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:27:48,734 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(154,498), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:27:48,742 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,497), 尺寸1x4, 长宽比0.25, 面积4
2025-07-28 23:27:48,747 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(229,495), 尺寸29x11, 长宽比2.64, 面积319
2025-07-28 23:27:48,752 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=495 (距底部154像素区域)
2025-07-28 23:27:48,761 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 23:27:48,765 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(163,495), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 23:27:48,767 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,495), 尺寸10x10, 长宽比1.00, 面积100
2025-07-28 23:27:48,769 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=495 (距底部154像素区域)
2025-07-28 23:27:48,778 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 23:27:48,782 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(170,494), 尺寸5x11, 长宽比0.45, 面积55
2025-07-28 23:27:48,785 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(168,494), 尺寸1x7, 长宽比0.14, 面积7
2025-07-28 23:27:48,917 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,493), 尺寸7x13, 长宽比0.54, 面积91
2025-07-28 23:27:48,925 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,493), 尺寸10x1, 长宽比10.00, 面积10
2025-07-28 23:27:48,964 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,493), 尺寸41x14, 长宽比2.93, 面积574
2025-07-28 23:27:48,967 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=493 (距底部154像素区域)
2025-07-28 23:27:48,976 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 23:27:48,978 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,491), 尺寸37x37, 长宽比1.00, 面积1369
2025-07-28 23:27:48,996 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=491 (距底部154像素区域)
2025-07-28 23:27:49,001 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-28 23:27:49,007 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,456), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 23:27:49,019 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(247,456), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 23:27:49,027 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(244,456), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 23:27:49,031 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(203,456), 尺寸9x3, 长宽比3.00, 面积27
2025-07-28 23:27:49,035 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(208,454), 尺寸3x3, 长宽比1.00, 面积9
2025-07-28 23:27:49,050 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(203,454), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:27:49,059 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(175,454), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:27:49,062 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,454), 尺寸10x8, 长宽比1.25, 面积80
2025-07-28 23:27:49,066 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(201,453), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:27:49,074 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(158,453), 尺寸4x5, 长宽比0.80, 面积20
2025-07-28 23:27:49,076 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,452), 尺寸4x4, 长宽比1.00, 面积16
2025-07-28 23:27:49,078 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,452), 尺寸7x4, 长宽比1.75, 面积28
2025-07-28 23:27:49,081 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,452), 尺寸8x7, 长宽比1.14, 面积56
2025-07-28 23:27:49,083 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,452), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:27:49,085 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,452), 尺寸1x3, 长宽比0.33, 面积3
2025-07-28 23:27:49,094 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,452), 尺寸6x10, 长宽比0.60, 面积60
2025-07-28 23:27:49,096 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,451), 尺寸5x4, 长宽比1.25, 面积20
2025-07-28 23:27:49,099 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(159,451), 尺寸2x1, 长宽比2.00, 面积2
2025-07-28 23:27:49,100 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,451), 尺寸5x4, 长宽比1.25, 面积20
2025-07-28 23:27:49,102 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(132,451), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:27:49,110 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(123,451), 尺寸8x7, 长宽比1.14, 面积56
2025-07-28 23:27:49,112 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(171,450), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 23:27:49,113 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(230,449), 尺寸13x11, 长宽比1.18, 面积143
2025-07-28 23:27:49,117 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,449), 尺寸5x1, 长宽比5.00, 面积5
2025-07-28 23:27:49,128 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,449), 尺寸23x11, 长宽比2.09, 面积253
2025-07-28 23:27:49,133 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,448), 尺寸17x12, 长宽比1.42, 面积204
2025-07-28 23:27:49,142 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(173,448), 尺寸10x8, 长宽比1.25, 面积80
2025-07-28 23:27:49,147 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,447), 尺寸19x13, 长宽比1.46, 面积247
2025-07-28 23:27:49,152 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,447), 尺寸11x13, 长宽比0.85, 面积143
2025-07-28 23:27:49,159 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(159,447), 尺寸13x3, 长宽比4.33, 面积39
2025-07-28 23:27:49,163 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(218,435), 尺寸1x2, 长宽比0.50, 面积2
2025-07-28 23:27:49,175 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,435), 尺寸3x2, 长宽比1.50, 面积6
2025-07-28 23:27:49,178 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(152,435), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 23:27:49,179 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(253,431), 尺寸2x2, 长宽比1.00, 面积4
2025-07-28 23:27:49,181 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(186,429), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:27:49,182 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(229,427), 尺寸30x11, 长宽比2.73, 面积330
2025-07-28 23:27:49,198 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(186,427), 尺寸1x1, 长宽比1.00, 面积1
2025-07-28 23:27:49,212 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(188,426), 尺寸3x11, 长宽比0.27, 面积33
2025-07-28 23:27:49,224 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,426), 尺寸6x11, 长宽比0.55, 面积66
2025-07-28 23:27:49,228 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,426), 尺寸5x11, 长宽比0.45, 面积55
2025-07-28 23:27:49,230 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,426), 尺寸17x11, 长宽比1.55, 面积187
2025-07-28 23:27:49,233 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=87.1 (阈值:60)
2025-07-28 23:27:49,243 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(202,425), 尺寸9x13, 长宽比0.69, 面积117
2025-07-28 23:27:49,247 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,425), 尺寸7x13, 长宽比0.54, 面积91
2025-07-28 23:27:49,251 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(153,425), 尺寸15x13, 长宽比1.15, 面积195
2025-07-28 23:27:49,260 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=88.5 (阈值:60)
2025-07-28 23:27:49,263 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,425), 尺寸14x13, 长宽比1.08, 面积182
2025-07-28 23:27:49,267 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=78.1 (阈值:60)
2025-07-28 23:27:49,273 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,423), 尺寸38x38, 长宽比1.00, 面积1444
2025-07-28 23:27:49,276 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=71.2 (阈值:60)
2025-07-28 23:27:49,277 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,405), 尺寸726x245, 长宽比2.96, 面积177870
2025-07-28 23:27:49,279 - WeChatAutoAdd - INFO - 底部区域找到 15 个按钮候选
2025-07-28 23:27:49,281 - WeChatAutoAdd - INFO - 选择特殊位置按钮: Y=491 (距底部154像素)
2025-07-28 23:27:49,283 - WeChatAutoAdd - INFO - 在底部找到按钮: (90, 509), 尺寸: 37x37, 位置得分: 3.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-07-28 23:27:49,285 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-28 23:27:49,321 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250728_232749.png
2025-07-28 23:27:49,325 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-28 23:27:49,327 - WeChatAutoAdd - INFO - 开始验证按钮区域(90, 509)是否包含'添加到通讯录'文字
2025-07-28 23:27:49,331 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250728_232749.png
2025-07-28 23:27:49,360 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-28 23:27:49,363 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0509, 平均亮度=215.2, 亮度标准差=45.9
2025-07-28 23:27:49,365 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-28 23:27:49,367 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-28 23:27:49,677 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(90, 509) -> 屏幕坐标(90, 509)
2025-07-28 23:27:50,514 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-28 23:27:50,516 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-28 23:27:50,519 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:27:50,526 - modules.wechat_auto_add_simple - INFO - ✅ 13164180165 添加朋友操作执行成功
2025-07-28 23:27:50,529 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:27:50,540 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-28 23:27:52,550 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:27:52,583 - modules.wechat_auto_add_simple - INFO - ℹ️ 13164180165 未发现申请添加朋友窗口，可能直接添加成功
2025-07-28 23:27:52,590 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 13164180165
2025-07-28 23:27:52,594 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-28 23:27:56,455 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 5/2975
2025-07-28 23:27:56,459 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13092760008 (石刚)
2025-07-28 23:27:56,460 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
